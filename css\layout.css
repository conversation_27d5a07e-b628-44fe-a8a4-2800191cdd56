/* Layout styles */

/* Container */
.container {
    max-width: 1200px;
    padding: 1rem;
}

/* Card layout */
.card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    margin-bottom: 0.5rem !important;
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.12);
    transform: translateY(-2px);
}

.card-header {
    background: #f8f9fa;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding: 0.75rem 1rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    align-items: center;
}

.card-body {
    padding: 0.5rem 0.5rem 0.3rem 0.5rem !important;
}

/* Flex utilities */
.d-flex {
    gap: 0.25rem !important;
}

.d-flex.justify-content-between {
    margin-top: 0.25rem !important;
}

/* Grid layouts */
.goal-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 0.6rem;
    padding: 0.6rem;
}

/* Categories grid */
.categories-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    padding: 1rem 0;
} 