/* 人脉管理系统样式 */

/* 基础样式 */
body {
    background-color: #f8f9fa;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    text-decoration: none;
}

.navbar-brand:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* 工具栏样式 */
.toolbar {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 筛选栏样式 */
.filter-bar {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 统计信息样式 */
.stats-bar {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-item {
    padding: 10px;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #0d6efd;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 5px;
}

/* 主要内容区域 */
.main-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    min-height: 500px;
}

/* 视图容器 */
.view-container {
    width: 100%;
}

/* 人脉卡片样式 */
.contact-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.contact-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.contact-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.contact-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #212529;
    margin: 0;
}

.contact-position {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 2px 0 0 0;
}



.contact-importance {
    font-size: 0.9rem;
    color: #ffc107;
}

.contact-info {
    margin-bottom: 10px;
}

.contact-detail {
    font-size: 0.85rem;
    color: #6c757d;
    margin: 2px 0;
}

.contact-detail i {
    width: 16px;
    text-align: center;
    margin-right: 8px;
}

.contact-tags {
    margin-bottom: 10px;
}

.contact-tag {
    display: inline-block;
    background: #e9ecef;
    color: #495057;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    margin: 2px 4px 2px 0;
}

.contact-connections {
    margin-bottom: 10px;
}

.connection-item {
    display: inline-block;
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    margin: 2px 4px 2px 0;
    cursor: pointer;
}

.connection-item:hover {
    background: #bbdefb;
}

.contact-actions {
    display: flex;
    gap: 8px;
}

.contact-actions .btn {
    padding: 4px 8px;
    font-size: 0.75rem;
}

/* 关系网络视图样式 */
.network-controls {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 10px;
}

.network-canvas-container {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

#networkCanvas {
    display: block;
    cursor: grab;
}

#networkCanvas:active {
    cursor: grabbing;
}

/* 关系树视图样式 */
.tree-controls {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 10px;
}

.tree-container {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
}

.tree-node {
    margin: 10px 0;
    padding: 10px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: white;
    position: relative;
}

.tree-node.center-node {
    background: #e3f2fd;
    border-color: #1976d2;
}

.tree-node-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

.tree-node-name {
    font-weight: 600;
    color: #212529;
}

.tree-node-relation {
    font-size: 0.85rem;
    color: #6c757d;
}

.tree-children {
    margin-left: 20px;
    margin-top: 10px;
    border-left: 2px solid #e9ecef;
    padding-left: 15px;
}

.tree-toggle {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 0.8rem;
    padding: 2px 6px;
}

.tree-toggle:hover {
    color: #495057;
}

/* 模态框样式优化 */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    padding: 20px 24px 16px;
}

.modal-body {
    padding: 20px 24px;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 16px 24px 20px;
}

/* 连接管理样式 */
.connection-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 6px;
}

.connection-row select {
    flex: 1;
}

.connection-row input {
    flex: 1;
}

.connection-row .btn {
    padding: 4px 8px;
    font-size: 0.75rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .toolbar .row,
    .filter-bar .row {
        gap: 10px;
    }
    
    .toolbar .col-md-6,
    .filter-bar .col-md-3 {
        margin-bottom: 10px;
    }
    
    .contact-card {
        padding: 12px;
    }
    
    .contact-actions {
        flex-wrap: wrap;
    }
    
    .stats-bar .col-md-3 {
        margin-bottom: 15px;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .network-canvas-container {
        height: 400px;
    }
    
    #networkCanvas {
        width: 100% !important;
        height: 400px !important;
    }
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.loading i {
    font-size: 2rem;
    margin-bottom: 10px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #dee2e6;
}

.empty-state h5 {
    margin-bottom: 10px;
    color: #495057;
}

/* 搜索高亮 */
.highlight {
    background-color: #fff3cd;
    padding: 1px 2px;
    border-radius: 2px;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}


