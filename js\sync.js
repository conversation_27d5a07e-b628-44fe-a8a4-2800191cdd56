// 云同步功能实现 - 重构版

// 配置
const SyncConfig = {
    API_URL: 'https://sst.25ai.me',
    AUTH_KEY: 'st_sync_auth',
    TOKEN_EXPIRY_DAYS: 180,  // 令牌有效期设置为180天（半年）
    AUTO_SYNC: {
        ENABLED: false,          // 是否启用自动同步 - 修改为禁用
        INTERVAL: 60 * 60 * 1000, // 自动同步间隔时间（毫秒），默认1小时
        ON_CHANGE: false,        // 数据变更时是否自动同步 - 修改为禁用
        ON_LOAD: false,          // 页面加载时是否自动同步 - 修改为禁用
        INACTIVITY_DELAY: 5 * 60 * 1000 // 用户无活动5分钟后同步
    },
    // 新增：网络和性能配置
    NETWORK: {
        TIMEOUT: 15000,          // 请求超时时间（毫秒）
        RETRY_COUNT: 3,          // 重试次数
        RETRY_DELAY: 1000,       // 重试延迟（毫秒）
        CHUNK_SIZE: 1024 * 1024, // 大文件分块大小（1MB）
        COMPRESSION_THRESHOLD: 1 * 1024 // 压缩阈值（1KB）
    },
    // 缓存配置 - 已禁用，每次都获取最新数据
    CACHE: {
        ENABLED: false,  // 禁用缓存，每次都从服务器获取最新数据
        MAX_AGE: 0,
        KEY_PREFIX: 'sync_cache_'
    }
};

// 同步状态
let syncState = {
    isLoggedIn: false,
    userId: null,
    username: null,
    token: null,
    lastSync: null,
    syncInProgress: false,
    tokenCreatedAt: null,        // 新增字段：令牌创建时间
    autoSyncEnabled: false,      // 自动同步是否启用 - 修改为禁用
    autoSyncTimer: null,         // 自动同步计时器
    lastSyncSuccess: true,       // 上次同步是否成功
    pendingChanges: false,       // 是否有未同步的更改
    lastUserActivity: Date.now(), // 最后用户活动时间
    activityCheckTimer: null,    // 用户活动检查计时器
    syncScheduled: false,        // 是否已安排同步
    // 新增：性能和网络状态
    networkStatus: 'online',     // 网络状态
    lastDataHash: null,          // 上次数据哈希，用于增量同步
    compressionEnabled: true,    // 是否启用压缩

    retryCount: 0,              // 当前重试次数
    lastError: null             // 最后一次错误
};

// DOM元素缓存
let domElements = {};

// ================ 初始化 ================

// 页面加载完成后初始化同步功能
document.addEventListener('DOMContentLoaded', function() {
    console.log('初始化云同步功能');
    initSync();
});

// 初始化同步功能
function initSync() {
    try {
        // 1. 创建UI
        createSyncUI();
        
        // 2. 恢复登录状态
        restoreAuthState();
        
        // 3. 初始化功能(等待appData加载完成)
        if (typeof appData === 'undefined') {
            setTimeout(initSyncFunctionality, 300);
        } else {
            initSyncFunctionality();
        }
    } catch (e) {
        handleError('初始化同步功能失败', e);
    }
}

// 初始化功能部分
function initSyncFunctionality() {
    try {
        // 钩住各种函数
        hookSaveFunction();

        // 初始化网络状态监听
        initNetworkMonitoring();

        // 不再初始化自动同步
        // 不再添加用户活动监听

        console.log('同步功能初始化成功');
    } catch (e) {
        handleError('初始化同步功能失败', e);
    }
}

// ================ UI管理 ================

// 创建同步UI
function createSyncUI() {
    // 先检查是否已经存在同步UI
    if (document.getElementById('sync-panel')) {
        return;
    }
    
    // 查找数据同步容器
    const syncContainer = document.getElementById('data-sync-container');
    
    if (syncContainer) {
        createSyncUIInContainer(syncContainer);
    } else {
        createSyncUIWithFallback();
    }
    
    // 注册事件处理
    registerEventHandlers();
    
    // 初始化UI状态
    updateSyncUI();
}

// 在指定容器中创建UI
function createSyncUIInContainer(container) {
    // 清空容器内容
    container.innerHTML = '';
    
    // 创建同步面板
    const syncPanel = document.createElement('div');
    syncPanel.id = 'sync-panel';
    syncPanel.innerHTML = getSyncPanelHTML();
    
    // 添加到同步容器
    container.appendChild(syncPanel);
    
    // 不再显示状态徽章
}

// 当找不到指定容器时的回退方法
function createSyncUIWithFallback() {
    // 查找可能的目标元素
    let targetElement = null;
    let insertMode = 'after';
    
    // 1. 找数据相关卡片
    const dataCards = Array.from(document.querySelectorAll('.card')).filter(card => {
        const header = card.querySelector('.card-header');
        return header && (header.textContent.includes('数据管理') || header.textContent.includes('数据同步'));
    });
    
    if (dataCards.length > 0) {
        targetElement = dataCards[0];
    } 
    // 2. 找相关按钮
    else {
        const relevantButtons = Array.from(document.querySelectorAll('button')).filter(btn => 
            btn.textContent.includes('任务进度') || 
            btn.id === 'currencyToggleBtn'
        );
        
        if (relevantButtons.length > 0) {
            targetElement = relevantButtons[0].closest('.card');
        }
    }
    
    // 3. 找侧边栏或使用body
    if (!targetElement) {
        targetElement = document.querySelector('.col-md-2') || document.body;
        insertMode = 'append';
    }
    
    // 创建同步面板
    const syncPanel = document.createElement('div');
    syncPanel.className = 'card mt-4';
    syncPanel.id = 'sync-panel';
    syncPanel.innerHTML = `
        <div class="card-header py-2">
            <i class="fas fa-cloud me-2"></i>云同步
        </div>
        <div class="card-body p-2">
            ${getSyncPanelHTML()}
        </div>
    `;
    
    // 添加到目标元素
    if (insertMode === 'after' && targetElement.parentNode) {
        targetElement.parentNode.insertBefore(syncPanel, targetElement.nextSibling);
    } else {
        targetElement.appendChild(syncPanel);
    }
}

// 获取同步面板的HTML内容
function getSyncPanelHTML() {
    return `
        <div id="sync-login-form">
            <div class="mb-2">
                <div class="input-group input-group-sm">
                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                    <input type="text" class="form-control" id="sync-username" placeholder="用户名">
                </div>
            </div>
            <div class="mb-2">
                <div class="input-group input-group-sm">
                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                    <input type="password" class="form-control" id="sync-password" placeholder="密码">
                </div>
            </div>
            <div class="text-center">
                <button type="button" class="btn btn-primary btn-sm" id="sync-login-btn">
                    <i class="fas fa-sign-in-alt me-1"></i>登录
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm" id="sync-register-btn">
                    <i class="fas fa-user-plus me-1"></i>注册新账号
                </button>
            </div>
        </div>
        <div id="sync-user-panel" style="display: none;">
            <div class="mb-2">
                <div class="text-center user-info-compact">
                    <div class="d-flex align-items-center justify-content-center mb-1">
                        <i class="fas fa-user-circle me-2 text-primary"></i>
                        <span id="sync-username-display" class="fw-bold">未登录</span>
                    </div>
                    <div class="d-flex align-items-center justify-content-center">
                        <i class="fas fa-clock me-1 text-muted"></i>
                        <span class="text-muted small" id="sync-last-time">上次同步: 从未</span>
                    </div>
                </div>
                <div id="sync-cloud-status" class="alert alert-info d-none mt-2" role="alert">
                    <i class="fas fa-cloud me-2"></i>正在检查云端数据...
                </div>
            </div>

            <div class="row g-1 mb-2">
                <div class="col-6">
                    <button type="button" class="btn btn-success btn-sm w-100" id="sync-upload-btn">
                        <i class="fas fa-upload me-1"></i>上传
                    </button>
                </div>
                <div class="col-6">
                    <button type="button" class="btn btn-warning btn-sm w-100" id="sync-download-btn">
                        <i class="fas fa-download me-1"></i>下载
                    </button>
                </div>
            </div>
            <div class="row g-1 mb-2">
                <div class="col-12">
                    <button type="button" class="btn btn-outline-secondary btn-sm w-100" id="sync-logout-btn">
                        <i class="fas fa-sign-out-alt me-1"></i>退出登录
                    </button>
                </div>
            </div>
        </div>
    `;
}

// 注册UI事件处理程序
function registerEventHandlers() {
    addEventIfExists('sync-login-btn', 'click', handleLogin);
    addEventIfExists('sync-register-btn', 'click', handleRegister);
    addEventIfExists('sync-upload-btn', 'click', synchronizeUpload);
    addEventIfExists('sync-download-btn', 'click', synchronizeDownload);
    addEventIfExists('sync-logout-btn', 'click', handleLogout);

    // 移除自动同步开关事件
}

// 添加事件监听器(如果元素存在)
function addEventIfExists(elementId, event, handler) {
    const element = document.getElementById(elementId);
    if (element) {
        element.addEventListener(event, handler);
    }
}

// 更新同步UI状态
function updateSyncUI() {
    try {
        // 获取UI元素
        if (!domElements.syncLoginForm) {
            domElements.syncLoginForm = document.getElementById('sync-login-form');
            domElements.syncUserPanel = document.getElementById('sync-user-panel');
            domElements.syncUsername = document.getElementById('sync-username-display');
            domElements.syncStatus = document.getElementById('sync-status');
            domElements.syncLastTime = document.getElementById('sync-last-time');
        }
        
        // 更新UI元素
        if (syncState.isLoggedIn) {
            // 显示用户面板，隐藏登录表单
            if (domElements.syncLoginForm) domElements.syncLoginForm.style.display = 'none';
            if (domElements.syncUserPanel) domElements.syncUserPanel.style.display = 'block';
            
            // 更新用户名显示
            if (domElements.syncUsername) {
                domElements.syncUsername.textContent = syncState.username;
            }
            
            // 不再更新状态标签

            // 更新最后同步时间
            if (domElements.syncLastTime) {
                if (syncState.lastSync) {
                    const date = new Date(syncState.lastSync);
                    domElements.syncLastTime.textContent = `上次同步: ${date.toLocaleString()}`;
                } else {
                    domElements.syncLastTime.textContent = '上次同步: 从未';
                }
            }


        } else {
            // 显示登录表单，隐藏用户面板
            if (domElements.syncLoginForm) domElements.syncLoginForm.style.display = 'block';
            if (domElements.syncUserPanel) domElements.syncUserPanel.style.display = 'none';
        }
    } catch (e) {
        // 捕获ui错误但不影响功能
        console.error('更新同步UI失败:', e);
    }
}

// 更新UI上的云数据状态
function updateCloudStatusUI(message, type = 'info') {
    const statusAlert = document.getElementById('sync-cloud-status');
    if (!statusAlert) return;
    
    statusAlert.textContent = message;
    statusAlert.className = `alert alert-${type} mb-2 d-block`;
}

// 高亮UI元素
function highlightElement(elementId, duration = 3000) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    // 对按钮特殊处理
    if (element.classList.contains('btn-outline-primary')) {
        element.classList.remove('btn-outline-primary');
        element.classList.add('btn-primary');
        setTimeout(() => {
            element.classList.remove('btn-primary');
            element.classList.add('btn-outline-primary');
        }, duration);
    } else {
        // 其他元素添加阴影
        element.style.boxShadow = '0 0 10px rgba(0,123,255,0.5)';
        setTimeout(() => {
            element.style.boxShadow = 'none';
        }, duration);
    }
}

// ================ 认证管理 ================

// 从localStorage恢复认证状态
function restoreAuthState() {
    try {
        const savedAuth = localStorage.getItem(SyncConfig.AUTH_KEY);
        if (savedAuth) {
            const authData = JSON.parse(savedAuth);
            
            syncState.isLoggedIn = true;
            syncState.userId = authData.userId;
            syncState.username = authData.username;
            syncState.token = authData.token;
            syncState.lastSync = authData.lastSync;
            syncState.tokenCreatedAt = authData.tokenCreatedAt;
            
            // 强制设置为禁用自动同步
            syncState.autoSyncEnabled = false;
            
            // 检查令牌是否过期
            if (isTokenExpired()) {
                console.log('认证令牌已过期，需要重新登录');
                clearAuthState();
                return;
            }
            
            console.log('恢复登录状态:', authData.username);
            updateSyncUI();
            
            // 不再启用自动同步
        }
    } catch (e) {
        handleError('恢复认证状态失败', e);
        clearAuthState();
    }
}

// 保存认证状态到localStorage
function saveAuthState() {
    try {
        const authData = {
            userId: syncState.userId,
            username: syncState.username,
            token: syncState.token,
            lastSync: syncState.lastSync,
            tokenCreatedAt: syncState.tokenCreatedAt,
            autoSyncEnabled: syncState.autoSyncEnabled
        };
        
        localStorage.setItem(SyncConfig.AUTH_KEY, JSON.stringify(authData));
    } catch (e) {
        handleError('保存认证状态失败', e);
    }
}

// 清除认证状态
function clearAuthState() {
    syncState.isLoggedIn = false;
    syncState.userId = null;
    syncState.username = null;
    syncState.token = null;
    syncState.lastSync = null;
    
    // 删除本地存储的认证信息
    localStorage.removeItem(SyncConfig.AUTH_KEY);
    
    // 更新UI
    updateSyncUI();
}

// 处理登录
async function handleLogin() {
    const username = document.getElementById('sync-username')?.value.trim();
    const password = document.getElementById('sync-password')?.value;
    
    if (!username || !password) {
        alert('请输入用户名和密码');
        return;
    }
    
    try {
        setButtonLoading('sync-login-btn', true, '登录中...');
        
        const response = await fetchWithTimeout(`${SyncConfig.API_URL}/login`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({ username, password })
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || '登录失败');
        }
        
        // 更新认证状态
        syncState.isLoggedIn = true;
        syncState.userId = data.userId;
        syncState.username = data.username;
        syncState.token = data.token;
        syncState.tokenCreatedAt = Date.now(); // 记录令牌创建时间
        saveAuthState();
        
        updateSyncUI();
        checkCloudDataStatus();
    } catch (error) {
        handleError('登录失败', error, true);
    } finally {
        setButtonLoading('sync-login-btn', false, '登录');
    }
}

// 处理注册
async function handleRegister() {
    const username = document.getElementById('sync-username')?.value.trim();
    const password = document.getElementById('sync-password')?.value;
    
    if (!username || !password) {
        alert('请输入用户名和密码');
        return;
    }
    
    if (username.length < 3 || password.length < 6) {
        alert('用户名至少3个字符，密码至少6个字符');
        return;
    }
    
    try {
        setButtonLoading('sync-register-btn', true, '注册中...');
        
        const response = await fetchWithTimeout(`${SyncConfig.API_URL}/register`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({ username, password })
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || '注册失败');
        }
        
        // 更新认证状态
        syncState.isLoggedIn = true;
        syncState.userId = data.userId;
        syncState.username = data.username;
        syncState.token = data.token;
        saveAuthState();
        
        updateSyncUI();
        checkCloudDataStatus();
    } catch (error) {
        handleError('注册失败', error, true);
    } finally {
        setButtonLoading('sync-register-btn', false, '注册新账号');
    }
}



// 处理退出登录
function handleLogout() {
    if (confirm('确定要退出登录吗？')) {
        clearAuthState();
        alert('已退出登录');
    }
}



// ================ 数据同步 ================

// 检查云端数据状态
async function checkCloudDataStatus() {
    try {
        updateCloudStatusUI('正在检查云端数据...');
        
        const cloudData = await fetchCloudData();
        
        if (!cloudData || Object.keys(cloudData).length === 0) {
            updateCloudStatusUI('云端没有数据，可以上传本地数据', 'warning');
        } else {
            let cloudTime = cloudData.lastModified ? new Date(cloudData.lastModified).toLocaleString() : '未知';
            updateCloudStatusUI(`云端有数据(${cloudTime})，可以下载`, 'success');
        }
    } catch (error) {
        updateCloudStatusUI(`检查云端失败: ${error.message}`, 'danger');
        console.error('检查云端数据错误:', error);
    }
}

// 上传到云端
async function synchronizeUpload() {
    if (!syncState.isLoggedIn || syncState.syncInProgress) {
        return;
    }

    if (!confirm('确定要将本地数据上传到云端吗？这将覆盖云端的数据。')) {
        return;
    }

    try {
        syncState.syncInProgress = true;
        setButtonLoading('sync-upload-btn', true, '上传中...');
        updateSyncUI();

        // 加载复盘数据
        let reviewData = null;
        try {
            const reviewDataStr = localStorage.getItem('reviewData');
            if (reviewDataStr) {
                reviewData = JSON.parse(reviewDataStr);
            }
        } catch (e) {
            console.error('读取复盘数据失败', e);
        }

        // 压缩复盘数据
        let compressedReviewData = null;
        if (reviewData) {
            try {
                // 基本压缩 - 减少键名长度
                compressedReviewData = {
                    t: reviewData.templates ? reviewData.templates.map(t => ({
                        i: t.id,
                        n: t.name,
                        t: t.type,
                        it: t.items
                    })) : [],
                    r: reviewData.reviews ? reviewData.reviews.map(r => ({
                        i: r.id,
                        t: r.templateId,
                        d: r.date,
                        it: r.items
                    })) : [],
                    s: reviewData.statistics || {}
                };
            } catch (e) {
                console.error('压缩复盘数据失败', e);
                compressedReviewData = null;
            }
        }

        // 准备本地数据
        // 验证数据完整性，防止上传空数据
        if (!appData || Object.keys(appData).length === 0) {
            throw new Error('本地数据为空，无法上传。请检查数据是否正确加载。');
        }

        const localData = {
            stData: appData,
            moneyTrackerData: {
                goals: JSON.parse(localStorage.getItem('moneyGoals')) || [],
                currentGoalId: parseInt(localStorage.getItem('currentGoalId')) || 1,
                currentSubgoalId: parseInt(localStorage.getItem('currentSubgoalId')) || 1
            },
            // 添加网站导航数据
            websitesData: {
                websites: JSON.parse(localStorage.getItem('websites')) || { categories: [] },
                stats: JSON.parse(localStorage.getItem('websiteStats')) || { weekStart: new Date().toDateString(), clicks: {} }
            },
            // 添加每日时间分配数据
            dailyTimeData: JSON.parse(localStorage.getItem('dailyTimeData')) || {
                totalAvailableTime: 24 * 60,
                allocatedTime: 0,
                tasks: [],
                templates: []
            },
            // 添加优先级数据
            priorityData: {
                items: appData.priorityItems || []
            },
            // 添加任务进度数据
            taskProgressData: {
                taskBoards: JSON.parse(localStorage.getItem('taskBoards')) || [],
                tasks: JSON.parse(localStorage.getItem('tasks')) || []
            },
            // 添加阶段进度数据
            phaseProgressData: {
                phases: JSON.parse(localStorage.getItem('st_phases')) || [],
                phaseTasks: JSON.parse(localStorage.getItem('st_phase_tasks')) || {}
            },
            // 添加压缩后的复盘数据
            reviewData: compressedReviewData,
            // 添加账号管理数据
            accountManagerData: JSON.parse(localStorage.getItem('accountManagerData')) || {},
            // 添加人脉管理数据
            contactsData: JSON.parse(localStorage.getItem('contactsData')) || {
                contacts: [],
                connections: [],
                tags: [],
                lastId: 0,
                statistics: {
                    total: 0,
                    recentCount: 0,
                    importantCount: 0,
                    connectionCount: 0
                }
            },
            // 添加AI提示词管理数据
            promptManagerData: JSON.parse(localStorage.getItem('promptManagerData')) || {
                prompts: [],
                tags: [],
                lastId: 0,
                statistics: {
                    total: 0,
                    totalTags: 0
                }
            },
            // 添加赚钱项目数据
            moneyProjectsData: (() => {
                try {
                    const savedData = localStorage.getItem('savingsData');
                    if (savedData) {
                        const mainData = JSON.parse(savedData);
                        return {
                            categories: mainData.moneyProjectCategories || [],
                            projects: mainData.moneyProjects || []
                        };
                    }
                    return { categories: [], projects: [] };
                } catch (e) {
                    console.error('读取赚钱项目数据失败', e);
                    return { categories: [], projects: [] };
                }
            })(),
            // 添加脚本管理数据
            scriptManagerData: (() => {
                try {
                    const scriptManagerData = JSON.parse(localStorage.getItem('scriptManagerData')) || {
                        scripts: [],
                        lastId: 0,
                        statistics: { total: 0, totalSize: 0 }
                    };

                    // 收集所有脚本的完整代码（保持压缩状态）
                    const scriptsWithCode = scriptManagerData.scripts.map(script => {
                        try {
                            const fullScriptKey = `script_${script.id}`;
                            const fullScriptData = localStorage.getItem(fullScriptKey);
                            if (fullScriptData) {
                                const parsedScript = JSON.parse(fullScriptData);
                                // 保持压缩数据，不解压缩，减少同步数据量
                                return {
                                    ...script,
                                    codeData: parsedScript.codeData,
                                    compressionInfo: parsedScript.compressionInfo
                                };
                            }
                            return script;
                        } catch (e) {
                            console.error(`加载脚本 ${script.id} 的代码失败:`, e);
                            return script;
                        }
                    });

                    return {
                        ...scriptManagerData,
                        scripts: scriptsWithCode
                    };
                } catch (e) {
                    console.error('处理脚本管理数据失败:', e);
                    return { scripts: [], lastId: 0, statistics: { total: 0, totalSize: 0 } };
                }
            })(),
            lastModified: Date.now()
        };

        // 计算数据哈希（用于增量同步）
        const dataString = JSON.stringify(localData);
        const dataSize = calculateDataSize(dataString);
        const dataHash = await calculateDataHash(localData);

        console.log(`准备上传数据，大小：${dataSize}`);

        if (dataHash === syncState.lastDataHash) {
            showAutoCloseAlert('数据没有变化，无需上传');
            return;
        }

        // 上传到云端
        await saveCloudData(localData);

        // 保存数据哈希
        syncState.lastDataHash = dataHash;
        syncState.lastSync = Date.now();
        syncState.pendingChanges = false;
        saveAuthState();
        checkCloudDataStatus();

        // 显示成功消息，包含数据大小
        const uploadDataSize = syncState.lastUploadSize || dataSize || '未知大小';
        showAutoCloseAlert(`本地数据已成功上传到云端！\n数据大小：${uploadDataSize}`);
    } catch (error) {
        syncState.lastSyncSuccess = false;
        handleError('上传失败', error, true);
    } finally {
        syncState.syncInProgress = false;
        setButtonLoading('sync-upload-btn', false, '<i class="fas fa-upload"></i> 上传到云端');
        updateSyncUI();
    }
}

// 显示版本选择对话框
async function showVersionSelectionDialog() {
    try {
        // 获取版本列表
        const versions = await fetchDataVersions();

        if (!versions || versions.length === 0) {
            alert('云端没有可用的数据版本');
            return null;
        }

        // 创建版本选择对话框
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-download me-2"></i>选择要下载的数据版本
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            系统保留最近3个版本。选择一个版本下载，最新版本在最上方。
                        </div>
                        <div class="list-group" id="versionList">
                            ${versions.map((version, index) => `
                                <div class="list-group-item list-group-item-action version-item" data-version-id="${version.id}">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">
                                            ${index === 0 ? '<span class="badge bg-primary me-2">最新</span>' : ''}
                                            ${new Date(version.timestamp).toLocaleString()}
                                        </h6>
                                        <small class="text-muted">${formatDataSize(version.size)}</small>
                                    </div>
                                    <p class="mb-1">${version.description}</p>
                                    <small class="text-muted">版本ID: ${version.id}</small>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="confirmDownloadBtn" disabled>下载选中版本</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);

        // 绑定版本选择事件
        let selectedVersionId = null;
        modal.querySelectorAll('.version-item').forEach(item => {
            item.addEventListener('click', function() {
                // 移除其他选中状态
                modal.querySelectorAll('.version-item').forEach(i => i.classList.remove('active'));
                // 添加选中状态
                this.classList.add('active');
                selectedVersionId = this.dataset.versionId;
                modal.querySelector('#confirmDownloadBtn').disabled = false;
            });
        });

        // 返回Promise，等待用户选择
        return new Promise((resolve) => {
            modal.querySelector('#confirmDownloadBtn').addEventListener('click', () => {
                bootstrapModal.hide();
                resolve(selectedVersionId);
            });

            modal.addEventListener('hidden.bs.modal', () => {
                if (!selectedVersionId) {
                    resolve(null);
                }
                document.body.removeChild(modal);
            });

            bootstrapModal.show();
        });
    } catch (error) {
        console.error('显示版本选择对话框失败:', error);
        alert('获取版本列表失败: ' + error.message);
        return null;
    }
}

// 从云端下载（支持版本选择）
async function synchronizeDownload() {
    if (!syncState.isLoggedIn || syncState.syncInProgress) {
        return;
    }

    // 显示版本选择对话框
    const selectedVersionId = await showVersionSelectionDialog();
    if (!selectedVersionId) {
        return; // 用户取消了选择
    }

    if (!confirm('确定要下载选中的数据版本吗？这将覆盖本地的数据。建议先备份本地数据。')) {
        return;
    }

    // 创建本地数据备份
    try {
        const backupData = {
            timestamp: new Date().toISOString(),
            appData: appData, // 主系统数据（当前内存中的最新数据）
            localStorage: {
                // 核心数据
                st_phases: localStorage.getItem('st_phases'),
                st_phase_tasks: localStorage.getItem('st_phase_tasks'),
                moneyGoals: localStorage.getItem('moneyGoals'),

                // 其他模块数据
                websites: localStorage.getItem('websites'),
                websiteStats: localStorage.getItem('websiteStats'),
                dailyTimeData: localStorage.getItem('dailyTimeData'),
                taskBoards: localStorage.getItem('taskBoards'),
                tasks: localStorage.getItem('tasks'),
                accountManagerData: localStorage.getItem('accountManagerData'),
                contactsData: localStorage.getItem('contactsData'),
                promptManagerData: localStorage.getItem('promptManagerData'),
                savingsData: localStorage.getItem('savingsData'),
                scriptManagerData: localStorage.getItem('scriptManagerData'),
                reviewData: localStorage.getItem('reviewData'),

                // 设置数据
                dailyTimeSettings: localStorage.getItem('dailyTimeSettings'),
                st_timezone: localStorage.getItem('st_timezone'),
                st_currency: localStorage.getItem('st_currency'),
                st_exchange_rate: localStorage.getItem('st_exchange_rate'),
                currentViewMode: localStorage.getItem('currentViewMode')
            }
        };
        localStorage.setItem('data_backup_before_sync', JSON.stringify(backupData));
        console.log('已创建数据备份');
    } catch (error) {
        console.error('创建数据备份失败:', error);
    }

    try {
        syncState.syncInProgress = true;
        setButtonLoading('sync-download-btn', true, '下载中...');

        // 根据选择的版本获取数据
        const cloudData = await fetchCloudDataVersion(selectedVersionId);

        if (!cloudData || Object.keys(cloudData).length === 0) {
            throw new Error('云端没有数据，无法下载');
        }
        
        // 计算下载数据大小
        const dataString = JSON.stringify(cloudData);
        const dataSize = calculateDataSize(dataString);
        
        // 应用云端数据到本地
        await applyCloudDataToLocal(cloudData);
        
        syncState.lastSync = Date.now();
        saveAuthState();
        
        // 刷新UI
        if (typeof renderViewMode === 'function' && typeof currentView !== 'undefined') {
            renderViewMode(currentView);
        } else {
            showAutoCloseAlert(`下载成功，数据大小：${dataSize}，页面将刷新以应用新数据`);
            setTimeout(() => location.reload(), 2000);
            return;
        }

        showAutoCloseAlert(`云端数据已成功下载到本地！数据大小：${dataSize}`);
    } catch (error) {
        syncState.lastSyncSuccess = false;

        // 如果下载失败，提供恢复备份的选项
        if (confirm('下载失败！是否要恢复刚才的本地数据备份？')) {
            try {
                restoreDataBackup();
                showAutoCloseAlert('已恢复本地数据备份');
            } catch (restoreError) {
                console.error('恢复备份失败:', restoreError);
                handleError('恢复备份失败', restoreError, true);
            }
        } else {
            handleError('下载失败', error, true);
        }
    } finally {
        syncState.syncInProgress = false;
        setButtonLoading('sync-download-btn', false, '<i class="fas fa-download"></i> 从云端下载');
        updateSyncUI();
    }
}

// 应用云端数据到本地
async function applyCloudDataToLocal(cloudData) {
    // 安全地合并数据，避免覆盖现有数据
    if (cloudData.stData) {
        appData = {
            ...appData,  // 保留现有数据
            ...cloudData.stData  // 应用云端数据
        };
        localStorage.setItem(CONFIG.STORAGE_KEY, JSON.stringify(appData));
    }

    // 更新赚钱目标追踪系统数据
    if (cloudData.moneyTrackerData) {
        localStorage.setItem('moneyGoals', JSON.stringify(cloudData.moneyTrackerData.goals || []));
        localStorage.setItem('currentGoalId', cloudData.moneyTrackerData.currentGoalId || 1);
        localStorage.setItem('currentSubgoalId', cloudData.moneyTrackerData.currentSubgoalId || 1);
    }

    // 更新网站导航数据
    if (cloudData.websitesData) {
        localStorage.setItem('websites', JSON.stringify(cloudData.websitesData.websites || { categories: [] }));
        localStorage.setItem('websiteStats', JSON.stringify(cloudData.websitesData.stats || { weekStart: new Date().toDateString(), clicks: {} }));
    }

    // 更新每日时间分配数据
    if (cloudData.dailyTimeData) {
        localStorage.setItem('dailyTimeData', JSON.stringify(cloudData.dailyTimeData));
    }

    // 更新任务进度数据
    if (cloudData.taskProgressData) {
        localStorage.setItem('taskBoards', JSON.stringify(cloudData.taskProgressData.taskBoards || []));
        localStorage.setItem('tasks', JSON.stringify(cloudData.taskProgressData.tasks || []));
    }

    // 更新阶段进度数据
    if (cloudData.phaseProgressData) {
        // 只有当云端数据不为空时才更新
        if (cloudData.phaseProgressData.phases && cloudData.phaseProgressData.phases.length > 0) {
            localStorage.setItem('st_phases', JSON.stringify(cloudData.phaseProgressData.phases));
        }
        if (cloudData.phaseProgressData.phaseTasks && Object.keys(cloudData.phaseProgressData.phaseTasks).length > 0) {
            localStorage.setItem('st_phase_tasks', JSON.stringify(cloudData.phaseProgressData.phaseTasks));
        }
    }

    // 更新账号管理数据
    if (cloudData.accountManagerData) {
        localStorage.setItem('accountManagerData', JSON.stringify(cloudData.accountManagerData));
    }

    // 更新人脉管理数据
    if (cloudData.contactsData) {
        localStorage.setItem('contactsData', JSON.stringify(cloudData.contactsData));
    }

    // 更新AI提示词管理数据
    if (cloudData.promptManagerData) {
        localStorage.setItem('promptManagerData', JSON.stringify(cloudData.promptManagerData));
    }

    // 更新赚钱项目数据
    if (cloudData.moneyProjectsData) {
        try {
            // 获取主系统数据
            const savedData = localStorage.getItem('savingsData');
            let mainData = savedData ? JSON.parse(savedData) : {};

            // 更新赚钱项目相关数据
            mainData.moneyProjectCategories = cloudData.moneyProjectsData.categories || [];
            mainData.moneyProjects = cloudData.moneyProjectsData.projects || [];

            // 保存回主系统
            localStorage.setItem('savingsData', JSON.stringify(mainData));
            console.log('赚钱项目数据已从云端更新');
        } catch (error) {
            console.error('更新赚钱项目数据失败:', error);
        }
    }

    // 更新脚本管理数据
    if (cloudData.scriptManagerData) {
        try {
            // 分离基本信息和压缩代码数据
            const scriptsWithoutCode = cloudData.scriptManagerData.scripts.map(script => {
                const { codeData, compressionInfo, code, ...scriptInfo } = script;

                // 单独保存压缩的代码数据
                if (codeData || code) {
                    const fullScriptKey = `script_${script.id}`;
                    const dataToStore = codeData ?
                        { codeData, compressionInfo } :
                        { codeData: { compressed: code, isCompressed: false }, compressionInfo: null };
                    localStorage.setItem(fullScriptKey, JSON.stringify(dataToStore));
                }

                return scriptInfo;
            });

            // 保存基本信息
            const scriptManagerDataToSave = {
                ...cloudData.scriptManagerData,
                scripts: scriptsWithoutCode
            };
            localStorage.setItem('scriptManagerData', JSON.stringify(scriptManagerDataToSave));

            // 通知脚本管理器数据已更新
            if (typeof ScriptManager !== 'undefined' && ScriptManager.restoreFromSync) {
                ScriptManager.restoreFromSync(scriptManagerDataToSave);
            }

            console.log('脚本管理数据已成功恢复');
        } catch (e) {
            console.error('恢复脚本管理数据失败:', e);
        }
    }

    // 处理复盘数据（如果有）
    if (cloudData.reviewData) {
        try {
            // 解压复盘数据
            const decompressedReviewData = {
                templates: cloudData.reviewData.t ? cloudData.reviewData.t.map(t => ({
                    id: t.i,
                    name: t.n,
                    type: t.t,
                    items: t.it
                })) : [],
                reviews: cloudData.reviewData.r ? cloudData.reviewData.r.map(r => ({
                    id: r.i,
                    templateId: r.t,
                    date: r.d,
                    items: r.it
                })) : [],
                statistics: cloudData.reviewData.s || {
                    totalCount: 0,
                    dailyCount: 0,
                    weeklyCount: 0,
                    monthlyCount: 0,
                    streakCount: 0
                }
            };

            // 保存解压后的复盘数据
            localStorage.setItem('reviewData', JSON.stringify(decompressedReviewData));
            console.log('复盘数据已成功解压并保存');
        } catch (e) {
            console.error('解压复盘数据失败', e);
        }
    }
}

// 恢复数据备份
function restoreDataBackup() {
    const backupData = localStorage.getItem('data_backup_before_sync');
    if (!backupData) {
        throw new Error('没有找到数据备份');
    }

    const backup = JSON.parse(backupData);

    // 恢复主数据
    appData = backup.appData;
    localStorage.setItem(CONFIG.STORAGE_KEY, JSON.stringify(appData));

    // 恢复其他数据
    if (backup.localStorage) {
        Object.entries(backup.localStorage).forEach(([key, value]) => {
            if (value !== null) {
                localStorage.setItem(key, value);
            }
        });
    }

    console.log('数据备份已恢复');
}

// 处理手动恢复备份功能已删除

// 获取云端数据版本列表
async function fetchDataVersions() {
    try {
        // 首先检查令牌是否过期
        if (isTokenExpired()) {
            clearAuthState();
            throw new Error('登录已过期，请重新登录');
        }

        console.log('获取云端数据版本列表');

        const response = await fetchWithRetry(`${SyncConfig.API_URL}/data/${syncState.userId}/versions`, {
            method: 'GET',
            headers: {
                'Authorization': syncState.token
            }
        });

        if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
                clearAuthState();
                updateSyncUI();
                throw new Error('登录状态已失效，请重新登录');
            }

            const errorData = await response.json();
            throw new Error(errorData.error || '获取版本列表失败');
        }

        const result = await response.json();
        console.log('成功获取版本列表:', result.versions.length, '个版本');

        return result.versions;
    } catch (error) {
        console.error('获取版本列表错误:', error);
        throw error;
    }
}

// 获取特定版本的云端数据
async function fetchCloudDataVersion(versionId) {
    try {
        // 首先检查令牌是否过期
        if (isTokenExpired()) {
            clearAuthState();
            throw new Error('登录已过期，请重新登录');
        }

        console.log('获取云端数据版本:', versionId);

        const response = await fetchWithRetry(`${SyncConfig.API_URL}/data/${syncState.userId}/version/${versionId}`, {
            method: 'GET',
            headers: {
                'Authorization': syncState.token
            }
        });

        if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
                clearAuthState();
                updateSyncUI();
                throw new Error('登录状态已失效，请重新登录');
            }

            const errorData = await response.json();
            throw new Error(errorData.error || '获取指定版本数据失败');
        }

        const data = await response.json();
        console.log('成功获取版本数据:', versionId);

        return data;
    } catch (error) {
        console.error('获取版本数据错误:', error);
        throw error;
    }
}

// 获取云端数据（最新版本）
async function fetchCloudData() {
    try {
        // 首先检查令牌是否过期
        if (isTokenExpired()) {
            // 如果过期，清除认证状态并抛出错误
            clearAuthState();
            throw new Error('登录已过期，请重新登录');
        }

        // 缓存已禁用，每次都从服务器获取最新数据
        console.log('从服务器获取最新云端数据');

        const response = await fetchWithRetry(`${SyncConfig.API_URL}/data/${syncState.userId}`, {
            method: 'GET',
            headers: {
                'Authorization': syncState.token
            }
        });

        if (!response.ok) {
            // 处理特定的认证错误
            if (response.status === 401 || response.status === 403) {
                clearAuthState();
                updateSyncUI();
                throw new Error('登录状态已失效，请重新登录');
            }

            const errorData = await response.json();
            throw new Error(errorData.error || '获取云端数据失败');
        }

        const data = await response.json();

        // 缓存已禁用，不再缓存数据
        console.log('成功获取云端数据');

        return data;
    } catch (error) {
        console.error('获取云端数据错误:', error);
        throw error;
    }
}

// 计算数据大小并格式化显示
function calculateDataSize(dataString) {
    try {
        const bytes = new Blob([dataString]).size;
        return formatDataSize(bytes);
    } catch (e) {
        console.error('计算数据大小失败', e);
        return '未知大小';
    }
}

// 格式化数据大小（字节数）
function formatDataSize(bytes) {
    try {
        if (bytes < 1024) {
            return `${bytes} 字节`;
        } else if (bytes < 1024 * 1024) {
            return `${(bytes / 1024).toFixed(2)} KB`;
        } else {
            return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
        }
    } catch (e) {
        console.error('格式化数据大小失败', e);
        return '未知大小';
    }
}

// 新增：数据哈希计算，用于增量同步
async function calculateDataHash(data) {
    try {
        const dataString = typeof data === 'string' ? data : JSON.stringify(data);
        const encoder = new TextEncoder();
        const dataBuffer = encoder.encode(dataString);
        const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    } catch (e) {
        console.error('计算数据哈希失败', e);
        return null;
    }
}

// 数据处理（移除压缩功能，直接返回JSON字符串）
function compressData(data) {
    try {
        const dataString = typeof data === 'string' ? data : JSON.stringify(data);

        // 不再进行压缩，直接返回数据
        return {
            compressed: false,
            data: dataString,
            originalSize: dataString.length,
            compressedSize: dataString.length,
            ratio: 0
        };
    } catch (e) {
        console.error('数据处理失败', e);
        return {
            compressed: false,
            data: typeof data === 'string' ? data : JSON.stringify(data),
            originalSize: 0,
            compressedSize: 0,
            ratio: 0
        };
    }
}

// 保存数据到云端
async function saveCloudData(data) {
    try {
        // 首先检查令牌是否过期
        if (isTokenExpired()) {
            // 如果过期，清除认证状态并抛出错误
            clearAuthState();
            throw new Error('登录已过期，请重新登录');
        }

        // 智能压缩数据
        const compressionResult = compressData(data);
        const dataString = compressionResult.data;
        const dataSize = calculateDataSize(dataString);

        // 不再显示压缩信息
        syncState.lastCompressionInfo = '';

        // 生成上传描述（使用英文避免HTTP头部字符编码问题）
        const uploadDescription = `Manual Upload - ${new Date().toISOString()}`;

        const response = await fetchWithRetry(`${SyncConfig.API_URL}/data/${syncState.userId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': syncState.token,
                'X-Compressed': 'false',
                'X-Description': uploadDescription
            },
            body: dataString
        });

        if (!response.ok) {
            // 处理特定的认证错误
            if (response.status === 401 || response.status === 403) {
                clearAuthState();
                updateSyncUI();
                throw new Error('登录状态已失效，请重新登录');
            }

            const errorData = await response.json();
            throw new Error(errorData.error || '保存到云端失败');
        }

        // 保存数据大小信息，以便在上传完成后显示
        syncState.lastUploadSize = dataSize;
        syncState.lastSyncSuccess = true;

        // 缓存已禁用，无需清除缓存
        console.log('上传成功');

        return await response.json();
    } catch (error) {
        console.error('保存到云端错误:', error);
        syncState.lastSyncSuccess = false;
        throw error;
    }
}

// ================ 函数钩子 ================

// 钩住保存函数
function hookSaveFunction() {
    try {
        // 保存原始的saveAppData函数以便以后调用
        const originalSaveAppData = window.saveAppData || function() {};
        
        // 重写saveAppData函数
        window.saveAppData = function(customData, uiMode) {
            // 先调用原始函数
            const result = originalSaveAppData.call(this, customData, uiMode);
            
            // 标记有未同步的更改
            if (syncState.isLoggedIn) {
                syncState.pendingChanges = true;
                updateSyncUI();
                
                // 删除自动同步相关的代码
            }
            
            return result;
        };
        
        console.log('已钩住数据保存函数');
    } catch (e) {
        handleError('钩住数据保存函数失败', e);
    }
}

// ================ 工具函数 ================

// 自动关闭弹窗提示
function showAutoCloseAlert(message, duration = 2000) {
    // 创建自定义弹窗
    const alertDiv = document.createElement('div');
    alertDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        max-width: 300px;
        word-wrap: break-word;
        animation: slideIn 0.3s ease-out;
    `;

    // 添加动画样式
    if (!document.getElementById('auto-close-alert-style')) {
        const style = document.createElement('style');
        style.id = 'auto-close-alert-style';
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    alertDiv.textContent = message;
    document.body.appendChild(alertDiv);

    // 自动关闭
    setTimeout(() => {
        alertDiv.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 300);
    }, duration);

    console.log(`提示消息: ${message} (${duration}ms后自动关闭)`);
}

// 设置按钮加载状态
function setButtonLoading(buttonId, isLoading, loadingText) {
    const button = document.getElementById(buttonId);
    if (!button) return;

    if (isLoading) {
        button.disabled = true;
        button.innerHTML = loadingText.includes('<i') ? loadingText : `<i class="fas fa-spinner fa-spin"></i> ${loadingText}`;
    } else {
        button.disabled = false;
        button.innerHTML = loadingText;
    }
}

// 增强的网络请求函数，支持重试和进度
async function fetchWithRetry(url, options = {}, retryCount = SyncConfig.NETWORK.RETRY_COUNT) {
    const timeout = options.timeout || SyncConfig.NETWORK.TIMEOUT;

    for (let attempt = 0; attempt <= retryCount; attempt++) {
        let timeoutId = null;
        try {
            // 检查网络状态
            if (!navigator.onLine) {
                syncState.networkStatus = 'offline';
                throw new Error('网络连接不可用');
            }

            syncState.networkStatus = 'online';
            syncState.retryCount = attempt;

            const controller = new AbortController();
            timeoutId = setTimeout(() => controller.abort(), timeout);

            const response = await fetch(url, {
                ...options,
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            // 重置重试计数
            syncState.retryCount = 0;
            return response;

        } catch (error) {
            if (timeoutId) {
                clearTimeout(timeoutId);
            }

            // 如果是最后一次尝试，抛出错误
            if (attempt === retryCount) {
                if (error.name === 'AbortError') {
                    throw new Error('请求超时，请检查网络连接');
                }
                throw error;
            }

            // 等待后重试
            const delay = SyncConfig.NETWORK.RETRY_DELAY * Math.pow(2, attempt); // 指数退避
            console.log(`请求失败，${delay}ms后重试 (${attempt + 1}/${retryCount + 1}):`, error.message);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
}

// 兼容性：保留原函数名
async function fetchWithTimeout(url, options = {}, timeout = SyncConfig.NETWORK.TIMEOUT) {
    return fetchWithRetry(url, { ...options, timeout }, 0); // 不重试的版本
}

// 添加新函数：检查令牌是否过期
function isTokenExpired() {
    if (!syncState.tokenCreatedAt) {
        return false; // 如果没有创建时间，假设没有过期
    }
    
    const now = Date.now();
    const expiryTime = syncState.tokenCreatedAt + (SyncConfig.TOKEN_EXPIRY_DAYS * 24 * 60 * 60 * 1000);
    
    return now > expiryTime;
}

// 统一错误处理
function handleError(context, error, showAlert = false) {
    console.error(`${context}:`, error);
    
    // 检查是否是登录过期的错误
    const isAuthError = error.message && (
        error.message.includes('登录已过期') || 
        error.message.includes('登录状态已失效') ||
        error.message.includes('认证失败') ||
        error.message.includes('token')
    );
    
    if (isAuthError) {
        // 确保清除认证状态
        clearAuthState();
        updateSyncUI();
    }
    
    if (showAlert) {
        alert(`${context}: ${error.message}`);
    }
}

// 停止自动同步，确保不会再次启动
function stopAutoSync() {
    if (syncState.autoSyncTimer) {
        clearInterval(syncState.autoSyncTimer);
        syncState.autoSyncTimer = null;
        console.log('自动同步已停止');
    }
    
    // 清除活动检查计时器
    if (syncState.activityCheckTimer) {
        clearInterval(syncState.activityCheckTimer);
        syncState.activityCheckTimer = null;
    }
    
    // 确保自动同步设置为禁用
    syncState.autoSyncEnabled = false;
}

// 移除自动同步相关的函数或确保它们不会被调用
function initAutoSync() {
    // 什么都不做，确保不会启用自动同步
    console.log('自动同步已禁用');
}

// 移除用户活动跟踪相关的函数或确保它们不会被调用
function initUserActivityTracking() {
    // 什么都不做，不再跟踪用户活动
}

// 移除scheduleInactivitySync函数或确保它不会被调用
function scheduleInactivitySync() {
    // 什么都不做，不再安排非活动状态下的同步
}

// 移除updateUserActivity函数或确保它不会被调用
function updateUserActivity() {
    // 什么都不做，不再更新用户活动时间
}

// 移除autoSyncUpload函数或修改它，确保不会自动上传数据
function autoSyncUpload() {
    console.log('上传功能已禁用');
    return Promise.resolve();
}

// 移除autoSyncDownload函数或修改它，确保不会自动下载数据
function autoSyncDownload() {
    console.log('下载功能已禁用');
    return Promise.resolve();
}

// 保留但简化getCompressedReviewData函数
function getCompressedReviewData() {
    try {
        const reviewDataStr = localStorage.getItem('reviewData');
        if (!reviewDataStr) return null;

        const reviewData = JSON.parse(reviewDataStr);
        return {
            t: reviewData.templates ? reviewData.templates.map(t => ({
                i: t.id,
                n: t.name,
                t: t.type,
                it: t.items
            })) : [],
            r: reviewData.reviews ? reviewData.reviews.map(r => ({
                i: r.id,
                t: r.templateId,
                d: r.date,
                it: r.items
            })) : [],
            s: reviewData.statistics || {}
        };
    } catch (e) {
        console.error('读取或压缩复盘数据失败', e);
        return null;
    }
}

// ================ 新增：网络和性能优化功能 ================

// 初始化网络状态监听
function initNetworkMonitoring() {
    // 监听网络状态变化
    window.addEventListener('online', () => {
        syncState.networkStatus = 'online';
        updateSyncUI();
        console.log('网络已连接');
    });

    window.addEventListener('offline', () => {
        syncState.networkStatus = 'offline';
        updateSyncUI();
        console.log('网络已断开');
    });

    // 初始化网络状态
    syncState.networkStatus = navigator.onLine ? 'online' : 'offline';
}

// 清理所有同步缓存（保留函数以兼容旧代码）
function clearSyncCache() {
    console.log('缓存功能已禁用，无需清理');
}

// 清理当前用户的同步缓存（保留函数以兼容旧代码）
function clearUserSyncCache() {
    console.log('缓存功能已禁用，无需清理');
}

// 获取同步统计信息
function getSyncStats() {
    return {
        isLoggedIn: syncState.isLoggedIn,
        username: syncState.username,
        lastSync: syncState.lastSync,
        networkStatus: syncState.networkStatus,
        pendingChanges: syncState.pendingChanges,
        lastSyncSuccess: syncState.lastSyncSuccess,
        retryCount: syncState.retryCount,
        compressionEnabled: syncState.compressionEnabled,
        lastUploadSize: syncState.lastUploadSize,
        lastDataHash: syncState.lastDataHash,
        cacheEnabled: false  // 缓存已禁用
    };
}

// 强制同步（忽略哈希检查）
async function forceSynchronizeUpload() {
    const originalHash = syncState.lastDataHash;
    syncState.lastDataHash = null; // 临时清除哈希以强制上传

    try {
        await synchronizeUpload();
    } finally {
        // 如果上传失败，恢复原哈希
        if (!syncState.lastSyncSuccess && originalHash) {
            syncState.lastDataHash = originalHash;
        }
    }
}

// 导出同步功能到全局
window.SyncOptimized = {
    getSyncStats,
    clearSyncCache,
    clearUserSyncCache,
    forceSynchronizeUpload,
    calculateDataHash,
    compressData
};

// 保留但简化decompressAndSaveReviewData函数
function decompressAndSaveReviewData(compressedData) {
    try {
        const decompressedReviewData = {
            templates: compressedData.t ? compressedData.t.map(t => ({
                id: t.i,
                name: t.n,
                type: t.t,
                items: t.it
            })) : [],
            reviews: compressedData.r ? compressedData.r.map(r => ({
                id: r.i,
                templateId: r.t,
                date: r.d,
                items: r.it
            })) : [],
            statistics: compressedData.s || {
                totalCount: 0,
                dailyCount: 0,
                weeklyCount: 0,
                monthlyCount: 0,
                streakCount: 0
            }
        };
        
        localStorage.setItem('reviewData', JSON.stringify(decompressedReviewData));
        console.log('复盘数据已成功解压并保存');
    } catch (e) {
        console.error('解压复盘数据失败', e);
    }
}