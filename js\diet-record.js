// 饮食记录管理系统
const DietRecord = {
    // 存储键名 - 合并到主系统
    STORAGE_KEY: 'savingsData',

    // 数据结构
    data: {
        dietRecords: [], // 饮食记录数组
        foodHistory: [], // 食物历史记录
        foodLibrary: [], // 食品库
        settings: {
            autoSetToday: true, // 自动设置为今天
            showNotifications: true // 显示通知
        }
    },
    
    // 初始化
    init() {
        console.log('初始化饮食记录系统...');
        this.loadData();
        this.bindEvents();
        this.setDefaultDate();
        this.updateDisplay();
        this.updateFoodHistory();
        this.updateFoodLibraryDisplay();
    },

    // 加载数据 - 从主系统数据中加载
    loadData() {
        try {
            const savedData = localStorage.getItem(this.STORAGE_KEY);
            if (savedData) {
                const mainData = JSON.parse(savedData);
                // 合并饮食记录数据到主系统数据结构中
                this.data.dietRecords = mainData.dietRecords || [];
                this.data.foodHistory = mainData.foodHistory || [];

                // 检查foodLibrary数据格式，确保是字符串数组
                let foodLibrary = mainData.foodLibrary || this.getDefaultFoodLibrary();
                if (Array.isArray(foodLibrary) && foodLibrary.length > 0) {
                    // 如果是旧的对象格式，转换为字符串数组
                    if (typeof foodLibrary[0] === 'object') {
                        foodLibrary = this.getDefaultFoodLibrary(); // 重置为空数组
                    }
                }
                this.data.foodLibrary = foodLibrary;
                console.log('饮食记录数据加载成功');
            } else {
                // 首次使用，初始化默认食品库
                this.data.foodLibrary = this.getDefaultFoodLibrary();
            }
        } catch (error) {
            console.error('加载饮食记录数据失败:', error);
        }
    },

    // 获取默认食品库（空的，全部自定义）
    getDefaultFoodLibrary() {
        return [];
    },
    
    // 保存数据 - 合并到主系统数据中
    saveData() {
        try {
            // 获取主系统数据
            const savedData = localStorage.getItem(this.STORAGE_KEY);
            let mainData = savedData ? JSON.parse(savedData) : {};

            // 更新饮食记录相关数据
            mainData.dietRecords = this.data.dietRecords;
            mainData.foodHistory = this.data.foodHistory;
            mainData.foodLibrary = this.data.foodLibrary;

            // 保存回主系统
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(mainData));
            console.log('饮食记录数据保存成功');
        } catch (error) {
            console.error('保存饮食记录数据失败:', error);
        }
    },
    
    // 绑定事件
    bindEvents() {
        // 表单提交事件
        const dietForm = document.getElementById('dietForm');
        if (dietForm) {
            dietForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.addRecord();
            });
        }
        
        // 视图切换事件
        const viewButtons = document.querySelectorAll('input[name="viewMode"]');
        viewButtons.forEach(button => {
            button.addEventListener('change', () => {
                this.updateDisplay();
            });
        });

        // 不再监听表单字段变化来退出编辑模式
        // 编辑模式应该保持到用户提交或手动取消

        // 食品库相关事件
        this.bindFoodLibraryEvents();
    },

    // 绑定食品库事件
    bindFoodLibraryEvents() {
        // 添加食品
        const addFoodBtn = document.getElementById('addFoodBtn');
        if (addFoodBtn) {
            addFoodBtn.addEventListener('click', () => {
                this.addFood();
            });
        }

        // 回车键添加食品
        const newFoodName = document.getElementById('newFoodName');
        if (newFoodName) {
            newFoodName.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.addFood();
                }
            });
        }
    },
    
    // 设置默认日期为今天（北京时间）
    setDefaultDate() {
        const dateInput = document.getElementById('dietDate');
        if (dateInput && this.data.settings.autoSetToday) {
            // 获取北京时间
            const today = this.getBeiJingTime().split('T')[0];
            dateInput.value = today;
        }
    },
    
    // 添加饮食记录
    addRecord() {
        const date = document.getElementById('dietDate').value;
        const mealTime = document.getElementById('mealTime').value;
        const foodName = document.getElementById('foodName').value.trim();
        const foodAmount = document.getElementById('foodAmount').value.trim();
        const foodNote = document.getElementById('foodNote').value.trim();

        // 验证必填字段
        if (!date || !foodName || !foodAmount) {
            alert('请填写完整的饮食信息');
            return;
        }

        // 检查是否是编辑模式
        if (this.editingRecordId) {
            // 编辑模式：删除原记录
            this.data.dietRecords = this.data.dietRecords.filter(r => r.id !== this.editingRecordId);
        }

        // 创建记录对象
        const record = {
            id: this.editingRecordId || Date.now().toString(),
            date: date,
            mealTime: mealTime,
            foodName: foodName,
            foodAmount: foodAmount,
            note: foodNote,
            timestamp: this.getBeiJingTime()
        };

        // 添加到数据中
        this.data.dietRecords.unshift(record);

        // 清除编辑状态
        this.editingRecordId = null;

        // 更新食物历史记录
        this.addToFoodHistory(foodName, foodAmount);

        // 保存数据
        this.saveData();

        // 清空表单
        this.clearForm();

        // 重置按钮状态
        this.updateSubmitButton(false);

        // 更新显示
        this.updateDisplay();
        this.updateFoodHistory();
        this.updateFoodLibraryDisplay();
    },
    
    // 清空表单
    clearForm() {
        document.getElementById('foodName').value = '';
        document.getElementById('foodAmount').value = '';
        document.getElementById('foodNote').value = '';
        // 重新设置为当前日期
        this.setDefaultDate();
        // 重置时间段为上午
        document.getElementById('mealTime').value = 'morning';
    },

    // 更新提交按钮状态
    updateSubmitButton(isEditing = false) {
        const submitBtn = document.getElementById('submitBtn');
        const submitIcon = document.getElementById('submitIcon');
        const submitText = document.getElementById('submitText');
        const cancelBtn = document.getElementById('cancelBtn');

        if (isEditing) {
            submitIcon.className = 'fas fa-save me-2';
            submitText.textContent = '更新记录';
            submitBtn.className = 'btn btn-warning w-100';
            if (cancelBtn) cancelBtn.style.display = 'block';
        } else {
            submitIcon.className = 'fas fa-plus me-2';
            submitText.textContent = '添加记录';
            submitBtn.className = 'btn btn-primary w-100';
            if (cancelBtn) cancelBtn.style.display = 'none';
        }
    },

    // 取消编辑
    cancelEdit() {
        this.editingRecordId = null;
        this.clearForm();
        this.updateSubmitButton(false);
    },

    // 获取北京时间
    getBeiJingTime() {
        const now = new Date();
        const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
        const beijing = new Date(utc + (8 * 3600000));
        return beijing.toISOString();
    },

    // 添加到食物历史记录
    addToFoodHistory(foodName, foodAmount) {
        const historyItem = { foodName, foodAmount };

        // 检查是否已存在相同的食物和分量组合
        const existingIndex = this.data.foodHistory.findIndex(item =>
            item.foodName === foodName && item.foodAmount === foodAmount
        );

        if (existingIndex !== -1) {
            // 如果存在，移到最前面
            this.data.foodHistory.splice(existingIndex, 1);
        }

        // 添加到最前面
        this.data.foodHistory.unshift(historyItem);

        // 限制历史记录数量（最多保留20条）
        if (this.data.foodHistory.length > 20) {
            this.data.foodHistory = this.data.foodHistory.slice(0, 20);
        }
    },

    // 更新食物历史下拉列表
    updateFoodHistory() {
        const datalist = document.getElementById('foodHistory');
        if (datalist) {
            datalist.innerHTML = '';

            // 添加食品库中的食物
            this.data.foodLibrary.forEach(foodName => {
                const option = document.createElement('option');
                option.value = foodName;
                option.setAttribute('data-source', 'library');
                datalist.appendChild(option);
            });

            // 添加历史记录
            this.data.foodHistory.forEach(item => {
                const option = document.createElement('option');
                option.value = `${item.foodName} (${item.foodAmount})`;
                option.setAttribute('data-source', 'history');
                datalist.appendChild(option);
            });
        }
    },
    
    // 删除记录
    deleteRecord(recordId) {
        if (confirm('确定要删除这条饮食记录吗？')) {
            this.data.dietRecords = this.data.dietRecords.filter(record => record.id !== recordId);
            this.saveData();
            this.updateDisplay();
        }
    },
    
    // 编辑记录
    editRecord(recordId) {
        const record = this.data.dietRecords.find(r => r.id === recordId);
        if (record) {
            // 填充表单
            document.getElementById('dietDate').value = record.date;
            document.getElementById('mealTime').value = record.mealTime;
            document.getElementById('foodName').value = record.foodName;
            document.getElementById('foodAmount').value = record.foodAmount;
            document.getElementById('foodNote').value = record.note || '';

            // 标记正在编辑的记录ID，而不是立即删除
            this.editingRecordId = recordId;

            // 更新按钮状态为编辑模式
            this.updateSubmitButton(true);

            // 滚动到表单
            document.getElementById('dietForm').scrollIntoView({ behavior: 'smooth' });
        }
    },
    
    // 更新显示
    updateDisplay() {
        const container = document.getElementById('dietRecords');
        if (!container) return;

        const viewMode = document.querySelector('input[name="viewMode"]:checked').id;
        let filteredRecords = [];

        // 使用北京时间
        const todayStr = this.getBeiJingTime().split('T')[0];
        const today = new Date(this.getBeiJingTime());

        switch (viewMode) {
            case 'viewToday':
                filteredRecords = this.data.dietRecords.filter(record => record.date === todayStr);
                break;
            case 'viewWeek':
                const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                const weekAgoStr = weekAgo.toISOString().split('T')[0];
                filteredRecords = this.data.dietRecords.filter(record => record.date >= weekAgoStr);
                break;
            case 'viewAll':
            default:
                filteredRecords = this.data.dietRecords;
                break;
        }

        if (filteredRecords.length === 0) {
            container.innerHTML = this.getEmptyStateHTML();
            return;
        }

        // 按日期分组
        const groupedRecords = this.groupRecordsByDate(filteredRecords);
        container.innerHTML = this.renderGroupedRecords(groupedRecords);
    },
    
    // 按日期分组记录
    groupRecordsByDate(records) {
        const groups = {};
        records.forEach(record => {
            if (!groups[record.date]) {
                groups[record.date] = [];
            }
            groups[record.date].push(record);
        });
        
        // 按日期排序（最新的在前）
        const sortedDates = Object.keys(groups).sort((a, b) => new Date(b) - new Date(a));
        const sortedGroups = {};
        sortedDates.forEach(date => {
            // 按时间段排序：上午、下午、晚上
            const timeOrder = { 'morning': 1, 'afternoon': 2, 'evening': 3 };
            groups[date].sort((a, b) => timeOrder[a.mealTime] - timeOrder[b.mealTime]);
            sortedGroups[date] = groups[date];
        });
        
        return sortedGroups;
    },
    
    // 渲染分组记录
    renderGroupedRecords(groupedRecords) {
        let html = '';

        Object.keys(groupedRecords).forEach(date => {
            const records = groupedRecords[date];
            const formattedDate = this.formatDate(date);

            // 按时间段分组
            const mealGroups = {
                morning: records.filter(r => r.mealTime === 'morning'),
                afternoon: records.filter(r => r.mealTime === 'afternoon'),
                evening: records.filter(r => r.mealTime === 'evening')
            };

            html += `
                <div class="date-group">
                    <div class="date-group-header">
                        <i class="fas fa-calendar-day me-2"></i>${formattedDate}
                        <span class="badge bg-primary ms-2">${records.length}条记录</span>
                    </div>
                    ${this.renderMealGroups(mealGroups)}
                </div>
            `;
        });

        return html;
    },

    // 渲染时间段分组
    renderMealGroups(mealGroups) {
        const mealTimeText = {
            'morning': '上午',
            'afternoon': '下午',
            'evening': '晚上'
        };

        let html = '';

        ['morning', 'afternoon', 'evening'].forEach(mealTime => {
            const records = mealGroups[mealTime];
            if (records.length > 0) {
                html += `
                    <div class="meal-group">
                        <div class="meal-group-header">
                            <span class="meal-time-badge ${mealTime}">
                                ${mealTimeText[mealTime]}
                            </span>
                            <span class="meal-count">${records.length}项</span>
                        </div>
                        <div class="meal-records">
                            ${records.map(record => this.renderSimpleRecord(record)).join('')}
                        </div>
                    </div>
                `;
            }
        });

        return html;
    },

    // 渲染简化的记录项
    renderSimpleRecord(record) {
        return `
            <div class="simple-record-item">
                <div class="record-content">
                    <span class="food-info">
                        <i class="fas fa-utensils me-2"></i>
                        <strong>${record.foodName}</strong> - ${record.foodAmount}
                    </span>
                    <span class="record-time">${this.formatTime(record.timestamp)}</span>
                </div>
                ${record.note ? `<div class="record-note">
                    <i class="fas fa-sticky-note me-2"></i>${record.note}
                </div>` : ''}
                <div class="record-actions">
                    <button class="btn btn-outline-primary btn-sm" onclick="DietRecord.editRecord('${record.id}')">
                        <i class="fas fa-edit me-1"></i>编辑
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="DietRecord.deleteRecord('${record.id}')">
                        <i class="fas fa-trash me-1"></i>删除
                    </button>
                </div>
            </div>
        `;
    },

    // 渲染单条记录
    renderRecord(record) {
        const mealTimeText = {
            'morning': '上午',
            'afternoon': '下午',
            'evening': '晚上'
        };
        
        return `
            <div class="diet-record-item">
                <div class="diet-record-header">
                    <div class="meal-time-badge ${record.mealTime}">
                        ${mealTimeText[record.mealTime]}
                    </div>
                    <small class="text-muted">${this.formatTime(record.timestamp)}</small>
                </div>
                <div class="diet-content">
                    <div class="food-item">
                        <span class="food-name">
                            <i class="fas fa-utensils me-2"></i>${record.foodName}
                        </span>
                        <span class="food-amount">${record.foodAmount}</span>
                    </div>
                    ${record.note ? `<div class="diet-note">
                        <i class="fas fa-sticky-note me-2"></i>${record.note}
                    </div>` : ''}
                </div>
                <div class="diet-actions">
                    <button class="btn btn-outline-primary btn-sm" onclick="DietRecord.editRecord('${record.id}')">
                        <i class="fas fa-edit me-1"></i>编辑
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="DietRecord.deleteRecord('${record.id}')">
                        <i class="fas fa-trash me-1"></i>删除
                    </button>
                </div>
            </div>
        `;
    },
    
    // 获取空状态HTML
    getEmptyStateHTML() {
        return `
            <div class="empty-state">
                <i class="fas fa-utensils"></i>
                <h5>暂无饮食记录</h5>
                <p>开始记录您的饮食，养成健康的生活习惯吧！</p>
                <button class="btn btn-primary" onclick="document.getElementById('foodName').focus()">
                    <i class="fas fa-plus me-2"></i>添加第一条记录
                </button>
            </div>
        `;
    },


    
    // 格式化日期
    formatDate(dateStr) {
        const date = new Date(dateStr);
        // 使用北京时间
        const today = new Date(this.getBeiJingTime());
        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);

        const todayStr = today.toISOString().split('T')[0];
        const yesterdayStr = yesterday.toISOString().split('T')[0];
        
        if (dateStr === todayStr) {
            return '今天';
        } else if (dateStr === yesterdayStr) {
            return '昨天';
        } else {
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            });
        }
    },
    
    // 格式化时间
    formatTime(timestamp) {
        const date = new Date(timestamp);
        // 确保显示北京时间
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            timeZone: 'Asia/Shanghai'
        });
    },
    


    // ===== 食品库管理方法 =====

    // 添加食品
    addFood() {
        const nameInput = document.getElementById('newFoodName');
        if (!nameInput) return;

        const name = nameInput.value.trim();
        if (!name) {
            alert('请输入食物名称');
            return;
        }

        // 检查是否已存在相同的食物
        if (this.data.foodLibrary.includes(name)) {
            alert('该食物已存在');
            return;
        }

        // 添加到食品库
        this.data.foodLibrary.push(name);

        // 保存数据
        this.saveData();

        // 清空输入框
        nameInput.value = '';

        // 更新显示
        this.updateFoodLibraryDisplay();
        this.updateFoodHistory();
    },

    // 删除食品
    deleteFood(foodName) {
        if (confirm('确定要删除这个食品吗？')) {
            this.data.foodLibrary = this.data.foodLibrary.filter(name => name !== foodName);
            this.saveData();
            this.updateFoodLibraryDisplay();
            this.updateFoodHistory();
        }
    },

    // 选择食品
    selectFood(foodName) {
        const foodNameInput = document.getElementById('foodName');
        if (foodNameInput) {
            foodNameInput.value = foodName;

            // 聚焦到分量字段，方便继续填写
            const foodAmountInput = document.getElementById('foodAmount');
            if (foodAmountInput) {
                foodAmountInput.focus();
            }
        }
    },

    // 更新食品库显示
    updateFoodLibraryDisplay() {
        const container = document.getElementById('foodCardsContainer');
        if (!container) return;

        if (this.data.foodLibrary.length === 0) {
            container.innerHTML = `
                <div class="food-library-empty">
                    <i class="fas fa-utensils me-2"></i>暂无食品，请添加常用食物
                </div>
            `;
            return;
        }

        container.innerHTML = this.data.foodLibrary.map(foodName => `
            <div class="food-card" onclick="DietRecord.selectFood('${foodName}')">
                <span>${foodName}</span>
                <button class="delete-btn" onclick="event.stopPropagation(); DietRecord.deleteFood('${foodName}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `).join('');
    }
};



// 导出到全局作用域
window.DietRecord = DietRecord;
