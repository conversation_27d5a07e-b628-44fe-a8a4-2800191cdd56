<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资源争夺</title>
    <!-- 添加网站图标 -->
    <link rel="icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <link rel="shortcut icon" href="assets/img/favicon.svg" type="image/svg+xml">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        .resource-card {
            transition: all 0.3s ease;
            border-left: 4px solid #007bff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        .resource-card:hover {
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .resource-card .card-body {
            padding: 1.25rem;
        }
        .resource-card .card-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            line-height: 1.4;
        }
        .status-badge {
            font-size: 0.75rem;
            padding: 0.35rem 0.65rem;
            border-radius: 20px;
            font-weight: 500;
            white-space: nowrap;
        }
        .resource-card .btn-group-sm .btn {
            padding: 0.25rem 0.4rem;
            font-size: 0.75rem;
            border-radius: 4px;
            margin-left: 2px;
        }
        .resource-card .btn-group-sm .btn:first-child {
            margin-left: 0;
        }
        .resource-card .btn-group-sm .btn i {
            font-size: 0.8rem;
        }
        .resource-card .badge {
            font-size: 0.7rem;
            padding: 0.3rem 0.6rem;
            margin-right: 0.4rem;
            border-radius: 12px;
        }
        .resource-card .card-text {
            margin-bottom: 0.75rem;
            line-height: 1.5;
        }
        .resource-card .text-muted {
            color: #6c757d !important;
        }
        .resource-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.75rem;
            gap: 1rem;
        }
        .resource-content {
            flex: 1;
            min-width: 0;
        }
        .resource-actions {
            flex-shrink: 0;
        }
        .priority-high {
            border-left-color: #dc3545;
            background: linear-gradient(135deg, #fff5f5 0%, #fff 100%);
        }
        .priority-medium {
            border-left-color: #ffc107;
            background: linear-gradient(135deg, #fffbf0 0%, #fff 100%);
        }
        .priority-low {
            border-left-color: #28a745;
            background: linear-gradient(135deg, #f8fff9 0%, #fff 100%);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .reflection-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .missed-card {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-3">
        <!-- 顶部导航 -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-clipboard-list me-2"></i>资源争夺</h2>
                    <div>
                        <button class="btn btn-outline-secondary" onclick="window.location.href='index.html'">
                            <i class="fas fa-arrow-left me-2"></i>返回主页
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 左侧控制面板 -->
            <div class="col-md-3">
                <!-- 统计信息 -->
                <div class="card stats-card mb-4">
                    <div class="card-body text-center">
                        <h5><i class="fas fa-chart-bar me-2"></i>今日统计</h5>
                        <div class="row">
                            <div class="col-6">
                                <div class="h4" id="todayTotal">0</div>
                                <small>总资源</small>
                            </div>
                            <div class="col-6">
                                <div class="h4" id="todaySuccess">0</div>
                                <small>已获得</small>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-6">
                                <div class="h4" id="todayMissed">0</div>
                                <small>已错失</small>
                            </div>
                            <div class="col-6">
                                <div class="h4" id="todayPending">0</div>
                                <small>争夺中</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 添加资源 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-plus me-2"></i>记录资源争夺
                    </div>
                    <div class="card-body">
                        <form id="resourceForm">
                            <div class="mb-3">
                                <label class="form-label">资源类型:</label>
                                <select class="form-select" id="resourceType">
                                    <option value="工作机会">工作机会</option>
                                    <option value="学习资源">学习资源</option>
                                    <option value="人脉关系">人脉关系</option>
                                    <option value="时间分配">时间分配</option>
                                    <option value="财务资源">财务资源</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">资源描述:</label>
                                <input class="form-control" id="resourceName" type="text" />
                            </div>
                            <div class="mb-3">
                                <label class="form-label">重要程度:</label>
                                <select class="form-select" id="resourcePriority">
                                    <option value="high">高 - 非常重要</option>
                                    <option value="medium" selected>中 - 比较重要</option>
                                    <option value="low">低 - 一般重要</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">当前状态:</label>
                                <select class="form-select" id="resourceStatus">
                                    <option value="待争夺">待争夺</option>
                                    <option value="争夺中" selected>争夺中</option>
                                    <option value="已获得">已获得</option>
                                    <option value="已错失">已错失</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">截止时间:</label>
                                <input class="form-control" id="resourceDeadline" type="datetime-local" />
                            </div>
                            <div class="mb-3">
                                <label class="form-label">备注:</label>
                                <textarea class="form-control" id="resourceNote" rows="2"></textarea>
                            </div>
                            <button class="btn btn-primary w-100" type="submit">
                                <i class="fas fa-plus me-2"></i>添加资源
                            </button>
                        </form>
                    </div>
                </div>

                <!-- 每日反思 -->
                <div class="card reflection-card">
                    <div class="card-header">
                        <i class="fas fa-lightbulb me-2"></i>每日反思
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">今日收获:</label>
                            <textarea class="form-control numbered-textarea" id="todayGains" rows="4"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">失误分析:</label>
                            <textarea class="form-control numbered-textarea" id="todayMistakes" rows="4"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">明日改进:</label>
                            <textarea class="form-control numbered-textarea" id="tomorrowPlan" rows="4"></textarea>
                        </div>
                        <button class="btn btn-light w-100" onclick="saveReflection()">
                            <i class="fas fa-save me-2"></i>保存反思
                        </button>
                    </div>
                </div>
            </div>

            <!-- 右侧资源列表 -->
            <div class="col-md-9">
                <!-- 筛选器 -->
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <select class="form-select" id="filterStatus">
                                    <option value="">全部状态</option>
                                    <option value="待争夺">待争夺</option>
                                    <option value="争夺中">争夺中</option>
                                    <option value="已获得">已获得</option>
                                    <option value="已错失">已错失</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterType">
                                    <option value="">全部类型</option>
                                    <option value="工作机会">工作机会</option>
                                    <option value="学习资源">学习资源</option>
                                    <option value="人脉关系">人脉关系</option>
                                    <option value="时间分配">时间分配</option>
                                    <option value="财务资源">财务资源</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterPriority">
                                    <option value="">全部优先级</option>
                                    <option value="high">高优先级</option>
                                    <option value="medium">中优先级</option>
                                    <option value="low">低优先级</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-primary w-100" onclick="applyFilters()">
                                    <i class="fas fa-filter me-2"></i>筛选
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 资源列表 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-list me-2"></i>资源争夺记录</span>
                        <button class="btn btn-outline-info btn-sm" onclick="toggleReflectionView()">
                            <i class="fas fa-lightbulb me-2"></i>查看反思
                        </button>
                    </div>
                    <div class="card-body">
                        <!-- 资源列表区域 -->
                        <div id="resourcesContainer">
                            <!-- 资源卡片将在这里动态显示 -->
                        </div>

                        <!-- 反思显示区域 (默认隐藏) -->
                        <div id="reflectionsContainer" style="display: none;">
                            <!-- 历史反思将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.min.js"></script>
    
    <script src="js/resource-practical.js"></script>
    
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            ResourcePractical.init();
        });
    </script>
</body>
</html>
