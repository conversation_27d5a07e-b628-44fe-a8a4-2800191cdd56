<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>赚钱目标追踪系统</title>
    <link rel="stylesheet" href="css/money-goal-tracker.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <link rel="shortcut icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <!-- 内联PNG图标，用于不支持SVG的浏览器 -->
    <link rel="icon" href="data:image/png;base64,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" type="image/png">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <style>
        /* 自定义样式：标题和备注并排显示 */
        .goal-name-container, .subgoal-name-container {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
            margin-bottom: 5px;
            width: 100%;
        }
        
        .goal-name, .subgoal-name {
            font-weight: bold;
            margin-right: 5px;
            white-space: nowrap;
        }
        
        .goal-notes-inline, .subgoal-notes-inline {
            display: inline-flex;
            align-items: center;
            background-color: rgba(var(--text-secondary-color-rgb, 120, 120, 120), 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.85em;
            max-width: 70%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 1.2;
        }
        
        .goal-notes-inline i, .subgoal-notes-inline i {
            display: none;
        }
        
        /* 金色闪光进度条效果 */
        @keyframes goldGlowing {
            0% { 
                background-color: #FFDF00;
                box-shadow: 0 0 5px #FFDF00;
            }
            50% { 
                background-color: #FFC000;
                box-shadow: 0 0 15px #FFC000;
            }
            100% { 
                background-color: #FFDF00;
                box-shadow: 0 0 5px #FFDF00;
            }
        }
        
        /* 红色闪光进度条效果 */
        @keyframes redGlowing {
            0% { 
                background-color: #FF5252;
                box-shadow: 0 0 5px #FF5252;
            }
            50% { 
                background-color: #FF0000;
                box-shadow: 0 0 15px #FF0000;
            }
            100% { 
                background-color: #FF5252;
                box-shadow: 0 0 5px #FF5252;
            }
        }
        
        .progress-bar {
            animation: goldGlowing 1.5s infinite ease-in-out !important;
            background-image: linear-gradient(45deg, rgba(255,255,255,0.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.15) 50%, rgba(255,255,255,0.15) 75%, transparent 75%, transparent) !important;
            background-size: 1rem 1rem !important;
        }
        
        .time-progress-bar {
            animation: redGlowing 1.5s infinite ease-in-out !important;
            background-image: linear-gradient(45deg, rgba(255,255,255,0.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.15) 50%, rgba(255,255,255,0.15) 75%, transparent 75%, transparent) !important;
            background-size: 1rem 1rem !important;
        }
        
        /* 子目标进度条样式 */
        .subgoal-progress-bar {
            animation: goldGlowing 1.5s infinite ease-in-out !important;
            background-image: linear-gradient(45deg, rgba(255,255,255,0.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.15) 50%, rgba(255,255,255,0.15) 75%, transparent 75%, transparent) !important;
            background-size: 1rem 1rem !important;
            background-color: #FFDF00 !important; /* 确保有默认背景色 */
        }
        
        /* 增强子目标进度条选择器 */
        .subgoal-item .subgoal-progress .subgoal-progress-bar-bg .subgoal-progress-bar {
            animation: goldGlowing 1.5s infinite ease-in-out !important;
            background-image: linear-gradient(45deg, rgba(255,255,255,0.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.15) 50%, rgba(255,255,255,0.15) 75%, transparent 75%, transparent) !important;
            background-size: 1rem 1rem !important;
            background-color: #FFDF00 !important;
            box-shadow: 0 0 10px #FFDF00 !important;
        }
        
        /* 修复备注显示在同一行 */
        .goal-name-container {
            display: flex !important;
            flex-direction: row !important;
            align-items: center !important;
            flex-wrap: nowrap !important;
        }
        
        .subgoal-name-container {
            display: flex !important;
            flex-direction: row !important;
            align-items: center !important;
            flex-wrap: nowrap !important;
        }
        
        @media (max-width: 768px) {
            .goal-name-container, .subgoal-name-container {
                flex-direction: row; /* 确保在移动设备上也保持行内显示 */
                align-items: center;
                overflow: hidden;
            }
            
            .goal-notes-inline, .subgoal-notes-inline {
                margin-left: 8px !important;
                max-width: 60%;
            }
        }
        
        /* 修复进度文本导致布局问题 */
        .progress-text {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
            min-height: 22px;
            flex-wrap: nowrap;
            max-width: 100%;
            position: relative;
        }
        
        /* 确保金额文本不会挤压右侧按钮 */
        .progress-text .amount {
            font-weight: bold;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 0 1 auto;
            max-width: 70%;
        }
        
        /* 确保右侧按钮位置固定 */
        .goal-actions {
            display: flex;
            gap: 5px;
            position: absolute;
            right: 0;
            top: 0;
            z-index: 5;
        }
        
        /* 移动设备上的特殊处理 */
        @media (max-width: 768px) {
            .progress-text {
                padding-right: 60px; /* 为右侧按钮预留空间 */
            }

            .goal-actions {
                right: 0;
            }

            .amount {
                max-width: 60%;
            }
        }

        /* 额外的紧凑优化 */
        .app-container {
            padding: 15px 10px; /* 减少容器内边距 */
        }

        header {
            margin-bottom: 15px; /* 减少头部下边距 */
            padding-bottom: 10px; /* 减少头部内边距 */
        }

        h1 {
            font-size: 1.6rem; /* 减小标题字体 */
        }

        /* 优化空状态显示 */
        .empty-state {
            padding: 30px 0; /* 减少空状态内边距 */
        }

        .empty-state i {
            margin-bottom: 10px; /* 减少图标下边距 */
        }
    </style>
</head>
<body>
    <div class="app-container">
        <header>
            <h1><i class="fas fa-chart-line"></i> 赚钱目标追踪</h1>
            <div class="header-actions">
                <button id="add-goal-btn" class="primary-btn">
                    <i class="fas fa-plus"></i> 添加目标
                </button>
                <button class="secondary-btn" onclick="window.location.href='index.html'">
                    <i class="fas fa-home"></i> 返回
                </button>
            </div>
        </header>
        
        <main>
            <div class="dashboard">
                <div class="summary-cards">
                    <div class="summary-card">
                        <i class="fas fa-tasks fa-lg" style="color: var(--primary-color);"></i>
                        <h3>总目标</h3>
                        <p id="total-goals">0</p>
                    </div>
                    <div class="summary-card">
                        <i class="fas fa-spinner fa-lg" style="color: var(--secondary-accent);"></i>
                        <h3>进行中</h3>
                        <p id="in-progress">0</p>
                    </div>
                    <div class="summary-card">
                        <i class="fas fa-check-circle fa-lg" style="color: var(--success-color);"></i>
                        <h3>已完成</h3>
                        <p id="completed">0</p>
                    </div>
                    <div class="summary-card">
                        <i class="fas fa-chart-pie fa-lg" style="color: var(--accent-color);"></i>
                        <h3>总进度</h3>
                        <p id="overall-progress">0%</p>
                    </div>
                </div>
            </div>

            <div class="goals-container" id="goals-container">
                <!-- Goals will be added here dynamically -->
                <div class="empty-state" id="empty-state">
                    <i class="fas fa-rocket fa-3x"></i>
                    <p>还没有设置目标，点击"添加目标"开始你的赚钱之旅吧！</p>
                </div>
            </div>
        </main>

        <!-- Template for a goal card (will be cloned by JavaScript) -->
        <template id="goal-template">
            <div class="goal-card">
                <div class="goal-header">
                    <div class="goal-info">
                        <div class="goal-name-container">
                            <div class="goal-name"><i class="fas fa-bullseye"></i> <span class="goal-name-text">目标名称</span></div>
                            <div class="goal-notes-inline" style="display: none; margin-left: 8px; font-size: 0.9em; color: var(--text-secondary-color);">
                                <span class="goal-notes-content"></span>
                            </div>
                        </div>
                        <div class="goal-details">
                            <div class="goal-detail"><i class="far fa-calendar-alt"></i> 截止日期: <span class="goal-deadline">未设置</span></div>
                            <div class="goal-detail"><i class="fas fa-coins"></i> 目标金额: <span class="goal-target-amount">¥0</span></div>
                        </div>
                    </div>
                    <div class="goal-actions">
                        <button class="action-btn add-money-btn" title="添加资金"><i class="fas fa-plus-circle"></i></button>
                        <button class="action-btn edit-goal-btn" title="编辑目标"><i class="fas fa-edit"></i></button>
                        <button class="action-btn delete-goal-btn" title="删除目标"><i class="fas fa-trash-alt"></i></button>
                        <button class="action-btn collapse-btn" title="收起/展开"><i class="fas fa-chevron-up"></i></button>
                    </div>
                </div>
                
                <div class="progress-container">
                    <div class="progress-text">
                        <span>金额进度:</span>
                        <span><span class="current-amount amount">¥0</span> / <span class="target-amount amount">¥0</span> (<span class="progress-percentage percentage">0%</span>)</span>
                    </div>
                    <div class="progress-bar-bg">
                        <div class="progress-bar" style="width: 0%"></div>
                    </div>
                    
                    <div class="time-progress-container" style="margin-top: 10px;">
                        <div class="progress-text">
                            <span>时间进度:</span>
                            <span class="time-remaining">剩余 0 天 (0%)</span>
                        </div>
                        <div class="progress-bar-bg">
                            <div class="progress-bar time-progress-bar" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
                
                <div class="subgoals-container">
                    <!-- 子目标会在这里动态添加 -->
                </div>
                
                <div class="add-subgoal-container">
                    <button class="add-subgoal-btn">
                        <i class="fas fa-plus"></i> 添加子目标
                    </button>
                </div>
            </div>
        </template>
        
        <!-- Template for a subgoal item -->
        <template id="subgoal-template">
            <div class="subgoal-item">
                <div class="subgoal-info">
                    <div class="subgoal-name-container">
                        <div class="subgoal-name">子目标名称</div>
                        <div class="subgoal-notes-inline" style="display: none; margin-left: 8px; font-size: 0.9em; color: var(--text-secondary-color);">
                            <span class="subgoal-notes-content"></span>
                        </div>
                    </div>
                    
                    <div class="subgoal-deadline">截止日期: <span class="subgoal-deadline-text">未设置</span></div>
                    
                    <div class="progress-container">
                        <div class="progress-text">
                            <span>金额进度:</span>
                            <span class="subgoal-progress-text">¥0 / ¥0 (0%)</span>
                        </div>
                        <div class="progress-bar-bg">
                            <div class="subgoal-progress-bar" style="width: 0%"></div>
                        </div>
                        
                        <div class="time-progress-container" style="margin-top: 10px;">
                            <div class="progress-text">
                                <span>时间进度:</span>
                                <span class="subgoal-time-remaining">剩余 0 天 (0%)</span>
                            </div>
                            <div class="progress-bar-bg">
                                <div class="progress-bar time-progress-bar subgoal-time-progress-bar" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="subgoal-actions">
                    <button class="action-btn add-money-subgoal-btn" title="添加资金"><i class="fas fa-plus-circle"></i></button>
                    <button class="action-btn edit-subgoal-btn" title="编辑子目标"><i class="fas fa-edit"></i></button>
                    <button class="action-btn delete-subgoal-btn" title="删除子目标"><i class="fas fa-trash-alt"></i></button>
                </div>
            </div>
        </template>

        <!-- Unified modal for adding/editing goals and subgoals -->
        <div class="modal" id="goal-modal">
            <div class="modal-content">
                <span class="close-btn" id="close-modal">&times;</span>
                <h2 id="modal-title"><i class="fas fa-bullseye"></i> 添加新目标</h2>
                <form id="goal-form">
                    <input type="hidden" id="goal-id">
                    <input type="hidden" id="parent-goal-id">
                    <input type="hidden" id="is-subgoal" value="false">
                    <div class="form-group">
                        <label for="goal-name"><i class="fas fa-tag"></i> 目标名称</label>
                        <input type="text" id="goal-name" placeholder="例如：用AI赚钱" required>
                    </div>
                    <div class="form-group">
                        <label for="goal-amount"><i class="fas fa-coins"></i> 目标金额</label>
                        <input type="number" id="goal-amount" placeholder="例如：5000000" required>
                    </div>
                    <div class="form-group" style="display: none;">
                        <label for="current-amount"><i class="fas fa-piggy-bank"></i> 当前金额</label>
                        <input type="number" id="current-amount" placeholder="0" value="0" required>
                    </div>
                    <div class="form-group">
                        <label for="goal-deadline"><i class="far fa-calendar-alt"></i> 截止日期（可选）</label>
                        <input type="date" id="goal-deadline">
                    </div>
                    <div class="form-group">
                        <label for="goal-notes"><i class="fas fa-sticky-note"></i> 备注（可选）</label>
                        <textarea id="goal-notes" placeholder="添加备注信息..." rows="3"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="secondary-btn" id="cancel-btn">
                            <i class="fas fa-times"></i> 取消
                        </button>
                        <button type="submit" class="primary-btn" id="save-goal-btn">
                            <i class="fas fa-save"></i> 保存
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Confirmation modal -->
        <div class="modal" id="confirm-modal">
            <div class="modal-content confirm-content">
                <h2><i class="fas fa-exclamation-triangle"></i> 确认删除</h2>
                <p id="confirm-message">确定要删除这个目标吗？所有相关的子目标也将被删除。</p>
                <div class="form-actions">
                    <button class="secondary-btn" id="confirm-no">
                        <i class="fas fa-times"></i> 取消
                    </button>
                    <button class="danger-btn" id="confirm-yes">
                        <i class="fas fa-trash-alt"></i> 确定删除
                    </button>
                </div>
            </div>
        </div>

        <!-- Money input modal -->
        <div class="modal" id="money-input-modal">
            <div class="modal-content money-input-content">
                <span class="close-btn" id="close-money-modal">&times;</span>
                <h2 id="money-modal-title"><i class="fas fa-hand-holding-usd"></i> 添加资金</h2>
                <input type="hidden" id="money-goal-id">
                <input type="hidden" id="money-subgoal-id">
                <div class="money-input-field">
                    <input type="number" id="money-amount" placeholder="输入金额" required min="1">
                </div>
                <div class="money-input-actions">
                    <button class="secondary-btn" id="cancel-money-btn">
                        <i class="fas fa-times"></i> 取消
                    </button>
                    <button class="primary-btn" id="save-money-btn">
                        <i class="fas fa-check"></i> 确认
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/money-goal-tracker.js"></script>
</body>
</html>
