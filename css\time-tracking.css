/* Time tracking styles - 紧凑优化版 */

.time-tracking-container {
    background: #fff;
    border-radius: 6px;
    padding: 0.6rem; /* 减少内边距 */
    margin-top: 0.4rem; /* 减少顶部边距 */
    box-shadow: 0 1px 2px rgba(0,0,0,0.08);
}

.time-tracking-container h3 {
    font-size: 1.1rem; /* 减小标题字体 */
    margin-bottom: 0.4rem; /* 减少下边距 */
    font-weight: 600;
    color: #2c3e50;
}

/* 修改事件卡片容器为更紧凑的网格布局 */
#events-container {
    margin-top: 0.3rem; /* 减少顶部边距 */
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 改为三列布局 */
    gap: 8px; /* 减少间距 */
}

.event-card {
    background: #f8f9fa;
    border-radius: 4px; /* 减小圆角 */
    padding: 0.4rem 0.5rem; /* 大幅减少内边距 */
    box-shadow: 0 1px 2px rgba(0,0,0,0.04);
    border: 1px solid #e9ecef;
    position: relative;
    transition: all 0.2s ease;
    min-height: auto; /* 移除最小高度限制 */
}

/* 添加卡片悬停效果 */
.event-card:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.08);
    transform: translateY(-1px);
}

.event-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem; /* 减少下边距 */
    flex-wrap: wrap; /* 允许换行 */
    gap: 0.2rem;
}

.event-name {
    font-weight: 500;
    color: #2c3e50;
    font-size: 0.9rem; /* 减小字体 */
    line-height: 1.2;
    flex: 1;
    min-width: 0; /* 允许文本截断 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.time-input {
    width: 70px; /* 减小宽度 */
    padding: 0.2rem 0.3rem; /* 减少内边距 */
    border: 1px solid #ced4da;
    border-radius: 2px; /* 减小圆角 */
    margin-right: 0.2rem; /* 减少右边距 */
    font-size: 0.8rem; /* 减小字体 */
    height: 26px; /* 固定高度 */
}

.time-total {
    color: #495057;
    font-size: 0.8rem; /* 减小字体 */
    margin-top: 0.1rem; /* 减少顶部边距 */
    font-weight: 500;
    text-align: center;
    background: #e9ecef;
    padding: 0.15rem 0.3rem;
    border-radius: 3px;
    display: inline-block;
}

.add-event-btn {
    margin-bottom: 0.4rem; /* 减少下边距 */
    padding: 0.25rem 0.5rem; /* 减少内边距 */
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.8rem; /* 减小字体 */
    display: flex;
    align-items: center;
    gap: 0.2rem;
    height: 28px; /* 固定高度 */
}

.add-event-btn:hover {
    background-color: #0056b3;
}

/* 优化按钮显示逻辑 - 始终显示但透明度较低 */
.event-card .btn-success,
.event-card .btn-danger {
    opacity: 0.7; /* 默认半透明显示 */
    transform: translateY(0);
    transition: opacity 0.2s ease;
}

/* 鼠标悬停时完全显示按钮 */
.event-card:hover .btn-success,
.event-card:hover .btn-danger {
    opacity: 1;
}

.event-card .btn {
    padding: 0.15rem 0.3rem; /* 减少内边距 */
    font-size: 0.75rem; /* 减小字体 */
    margin-left: 0.2rem; /* 减少左边距 */
    display: flex;
    align-items: center;
    gap: 0.1rem;
    height: 24px; /* 固定高度 */
    min-width: auto;
}

.event-card .btn-success {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.event-card .btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

/* 添加统计信息显示 */
.time-stats {
    background: #f1f3f4;
    border-radius: 4px;
    padding: 0.4rem 0.6rem;
    margin-bottom: 0.4rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #495057;
}

.time-stats .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.1rem;
}

.time-stats .stat-label {
    font-size: 0.7rem;
    color: #6c757d;
}

.time-stats .stat-value {
    font-weight: 600;
    color: #2c3e50;
}

/* 空状态提示 */
.empty-state {
    text-align: center;
    padding: 2rem 1rem;
    color: #6c757d;
    font-size: 0.9rem;
}

.empty-state i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #adb5bd;
}

/* Responsive adjustments - 紧凑优化 */
@media (max-width: 992px) {
    #events-container {
        grid-template-columns: repeat(2, 1fr); /* 中等屏幕两列 */
    }
}

@media (max-width: 768px) {
    #events-container {
        grid-template-columns: 1fr; /* 小屏幕一列 */
        gap: 6px; /* 进一步减少间距 */
    }

    .time-tracking-container {
        padding: 0.4rem; /* 移动端进一步减少内边距 */
    }

    .event-header {
        flex-direction: row; /* 保持水平布局 */
        align-items: center;
        flex-wrap: wrap;
    }

    .event-name {
        margin-bottom: 0;
        font-size: 0.85rem;
        max-width: 100%;
    }

    .time-input {
        width: 60px; /* 移动端更小的输入框 */
        margin-bottom: 0;
    }

    .event-card .btn {
        margin-left: 0.1rem;
        margin-right: 0.1rem;
        padding: 0.1rem 0.2rem;
        font-size: 0.7rem;
        height: 22px;
    }

    .time-stats {
        flex-direction: column;
        gap: 0.3rem;
        padding: 0.3rem;
    }

    .time-stats .stat-item {
        flex-direction: row;
        gap: 0.3rem;
    }
}
 