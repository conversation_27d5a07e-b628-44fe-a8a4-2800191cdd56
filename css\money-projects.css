/* 赚钱项目记录样式 */

/* 分类管理样式 */
.category-add-section {
    margin-bottom: 0.75rem;
}

.categories-container {
    max-height: 200px;
    overflow-y: auto;
    min-height: 0;
}

.category-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.4rem 0.6rem;
    margin-bottom: 0.4rem;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.category-item:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.category-name {
    font-weight: 500;
    color: #495057;
    cursor: pointer;
    transition: color 0.2s ease;
    user-select: none;
}

.category-name:hover {
    color: #0d6efd;
}

.delete-category-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.25rem;
}



/* 项目卡片样式 */
.project-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    margin-bottom: 0.75rem !important;
}

.project-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.project-card .card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    overflow: hidden;
}

.project-card .card-body {
    padding: 0.875rem;
}

/* 小项目卡片样式 */
.project-card-small {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    height: 100%;
}

.project-card-small:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.project-card-small .card-body {
    padding: 0.5rem !important;
}

.project-card-small .card-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #212529;
}

.project-card-small .badge {
    font-size: 0.65rem;
    padding: 0.2em 0.4em;
}

.project-card .card-title {
    color: #212529;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.4rem;
}

.project-card .badge {
    font-size: 0.7rem;
    padding: 0.25em 0.5em;
}

/* 统计卡片样式 */
.card .card-body h5 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.card .card-body h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

/* 筛选和排序区域样式 */
.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-select, .form-control {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus, .form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 项目状态徽章颜色 */
.badge.bg-secondary {
    background-color: #6c757d !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
}

.badge.bg-success {
    background-color: #198754 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

/* 按钮样式优化 */
.btn-outline-primary:hover {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
}

/* 空状态样式 */
.text-center.text-muted {
    color: #6c757d !important;
}

.text-center.text-muted i {
    opacity: 0.5;
}

/* 模态框样式优化 */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-title {
    font-weight: 600;
    color: #212529;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    text-decoration: none;
}

.navbar-brand:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

.navbar-text {
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .card-body {
        padding: 1rem;
    }

    .project-card .card-body {
        padding: 1rem;
    }

    .btn-sm {
        padding: 0.2rem 0.4rem;
        font-size: 0.8rem;
    }

    .row.mb-4 .col-md-3 {
        margin-bottom: 1rem;
    }

    /* 优化后的卡片响应式 */
    .project-data-grid {
        grid-template-columns: 1fr 1fr;
        gap: 0.75rem;
    }

    .revenue-item {
        grid-column: 1 / -1;
        border-left: none;
        border-top: 1px solid #e9ecef;
        padding-left: 0;
        padding-top: 0.5rem;
        margin-top: 0.5rem;
    }

    .card-footer-custom {
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
    }

    .time-info-horizontal {
        margin-right: 0;
        margin-bottom: 0.25rem;
    }

    .action-buttons {
        align-self: flex-end;
    }

    .project-title {
        font-size: 0.9rem;
    }

    .priority-stars {
        font-size: 0.7rem;
    }
}

@media (max-width: 576px) {
    .project-card .d-flex.justify-content-between {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .project-card .badge {
        margin-top: 0.5rem;
        align-self: flex-end;
    }
    
    .project-card .btn-group {
        margin-top: 0.5rem;
        width: 100%;
    }
    
    .project-card .btn-group .btn {
        flex: 1;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.project-card {
    animation: fadeIn 0.3s ease-out;
}

.category-item {
    animation: fadeIn 0.2s ease-out;
}

/* 滚动条样式 */
.categories-container::-webkit-scrollbar {
    width: 6px;
}

.categories-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.categories-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.categories-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 表单验证样式 */
.form-control.is-invalid {
    border-color: #dc3545;
}

.form-control.is-valid {
    border-color: #198754;
}

/* 加载状态样式 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 可点击数量样式 */
.clickable-quantity {
    transition: color 0.2s ease;
}

.clickable-quantity:hover {
    color: #0d6efd !important;
    text-decoration: underline !important;
}

/* 优先级星级样式 */
.priority-stars {
    color: #ffc107;
    text-shadow: 0 0 2px rgba(255, 193, 7, 0.5);
    user-select: none;
    font-size: 0.75rem;
    margin-left: 0.5rem;
}

/* 优化后的项目卡片样式 */
.project-card-optimized {
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    transition: all 0.3s ease;
    overflow: hidden;
    background: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    min-height: 200px; /* 设置最小高度确保卡片一致 */
    display: flex;
    flex-direction: column;
}

.project-card-optimized:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #c0c0c0;
}

.project-card-optimized.selected {
    border-color: #0d6efd;
    box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.1);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
}

/* 卡片头部样式 */
.card-header-custom {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    padding: 0.6rem 1rem 0.4rem;
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.15rem;
}

.project-title-section {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
}

.project-checkbox {
    margin-right: 0.5rem;
    margin-top: 0;
    transform: scale(1.1);
}

.project-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    margin-right: 0.5rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.status-badge {
    font-size: 0.7rem;
    padding: 0.25em 0.6em;
    border-radius: 12px;
    font-weight: 500;
}

.project-category {
    color: #6c757d;
    font-size: 0.75rem;
    margin-top: 0.1rem;
}

.project-category i {
    margin-right: 0.25rem;
    color: #adb5bd;
}

/* 卡片主体样式 */
.card-body-custom {
    padding: 0.5rem 1rem 0.25rem;
}

.pause-reason-alert {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 0.5rem;
    margin-bottom: 0.75rem;
    color: #856404;
    font-size: 0.8rem;
}

.pause-reason-alert i {
    margin-right: 0.5rem;
    color: #f39c12;
}

/* 数据网格样式 */
.project-data-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 0.25rem;
}

.data-item {
    text-align: center;
}

.data-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.data-value {
    font-weight: 600;
    font-size: 0.95rem;
}

.price-value {
    color: #28a745;
}

.progress-value {
    color: #495057;
}

.current-quantity {
    color: #0d6efd;
    cursor: pointer;
    text-decoration: underline;
    text-decoration-style: dotted;
}

.current-quantity:hover {
    color: #0056b3;
    text-decoration-style: solid;
}

.quantity-separator {
    color: #adb5bd;
    margin: 0 0.25rem;
}

.target-quantity {
    color: #6c757d;
}

.million-target-value {
    color: #dc3545;
    font-weight: 700;
    cursor: help;
}

.revenue-item {
    border-left: 2px solid #e9ecef;
    padding-left: 0.75rem;
}

.revenue-value {
    display: flex;
    flex-direction: column;
    gap: 0.1rem;
}

.current-revenue {
    color: #28a745;
    font-weight: 700;
}

.target-revenue {
    color: #6c757d;
    font-size: 0.7rem;
    font-weight: 400;
}

/* 统计信息中的目标收益模糊特效 */
#targetRevenue {
    cursor: pointer;
    transition: filter 0.3s ease;
}

#targetRevenue.blurred {
    filter: blur(4px);
    user-select: none;
}

#targetRevenue:hover {
    opacity: 0.8;
}

.project-note {
    background: #f8f9fa;
    border-left: 3px solid #6c757d;
    padding: 0.3rem 0.5rem;
    margin-top: 0.15rem;
    margin-bottom: 0;
    font-size: 0.8rem;
    color: #6c757d;
    max-height: 3rem; /* 限制最大高度 */
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 最多显示2行 */
    -webkit-box-orient: vertical;
    line-height: 1.4;
    border-radius: 0 4px 4px 0;
}

/* 确保卡片内容区域合理分布 */
.project-card-optimized .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.project-card-optimized .card-footer-custom {
    margin-top: auto; /* 将底部信息推到底部 */
}

/* 卡片底部样式 */
.card-footer-custom {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 0.25rem 0.75rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.time-info-horizontal {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.7rem;
    color: #6c757d;
    flex: 1;
    margin-right: 0.75rem;
}

.created-time {
    font-weight: 500;
}

.remaining-days {
    font-weight: 600;
    color: #495057;
    background: #e9ecef;
    padding: 0.15rem 0.5rem;
    border-radius: 12px;
    font-size: 0.65rem;
}

.deadline-time {
    display: none;
}

.time-spacer {
    display: none;
}

.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.action-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.action-btn:hover {
    transform: translateY(-1px);
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: #212529;
    color: #fff;
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
}

/* 成功/错误消息样式 */
.alert {
    border-radius: 0.5rem;
    border: none;
    font-weight: 500;
}

.alert-success {
    background-color: #d1edff;
    color: #0c5460;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* 打印样式 */
@media print {
    .btn, .modal, .navbar {
        display: none !important;
    }
    
    .project-card {
        break-inside: avoid;
        margin-bottom: 1rem;
    }
    
    .card {
        border: 1px solid #000 !important;
    }
}
