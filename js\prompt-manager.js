// AI提示词管理系统 JavaScript

// 配置
const PromptConfig = {
    STORAGE_KEY: 'promptManagerData',
    VERSION: '1.0.0'
};

// 数据结构
let promptData = {
    prompts: [],
    tags: new Set(),
    lastId: 0
};

// 当前状态
let currentEditingId = null;
let searchQuery = '';
let activeTagFilter = '';

// =============== 初始化函数 ===============
document.addEventListener('DOMContentLoaded', function() {
    initPromptManager();
});

function initPromptManager() {
    console.log('初始化AI提示词管理系统');

    // 清理旧数据（如果需要）
    cleanupOldData();

    // 加载数据
    loadPromptData();

    // 添加事件监听器
    attachEventListeners();

    // 渲染界面
    renderPromptsList();
    updateTagFilter();
}

// 清理旧的测试数据
function cleanupOldData() {
    try {
        const savedData = localStorage.getItem(PromptConfig.STORAGE_KEY);
        if (savedData) {
            const parsed = JSON.parse(savedData);
            // 如果存在内置的测试数据，清理掉
            if (parsed.prompts && parsed.prompts.length > 0) {
                const hasTestData = parsed.prompts.some(prompt =>
                    prompt.title === "代码解释器" ||
                    prompt.title === "文档总结" ||
                    prompt.title === "英文翻译" ||
                    prompt.title === "写作助手" ||
                    prompt.title === "问题分析"
                );

                if (hasTestData) {
                    // 清空数据，让用户从干净的状态开始
                    localStorage.removeItem(PromptConfig.STORAGE_KEY);
                    console.log('已清理内置测试数据');
                }
            }
        }
    } catch (error) {
        console.error('清理旧数据失败:', error);
        // 如果出错，直接清空
        localStorage.removeItem(PromptConfig.STORAGE_KEY);
    }
}

// =============== 数据管理 ===============
function loadPromptData() {
    try {
        const savedData = localStorage.getItem(PromptConfig.STORAGE_KEY);
        if (savedData) {
            const parsed = JSON.parse(savedData);
            promptData = {
                prompts: parsed.prompts || [],
                tags: new Set(parsed.tags || []),
                lastId: parsed.lastId || 0
            };
            console.log('提示词数据加载成功', promptData);
        }
    } catch (error) {
        console.error('加载提示词数据失败', error);
        promptData = {
            prompts: [],
            tags: new Set(),
            lastId: 0
        };
    }
}



function savePromptData() {
    try {
        const dataToSave = {
            prompts: promptData.prompts,
            tags: Array.from(promptData.tags),
            lastId: promptData.lastId,
            version: PromptConfig.VERSION,
            lastModified: new Date().toISOString()
        };
        localStorage.setItem(PromptConfig.STORAGE_KEY, JSON.stringify(dataToSave));
        console.log('提示词数据保存成功');
    } catch (error) {
        console.error('保存提示词数据失败', error);
    }
}

// =============== 事件监听器 ===============
function attachEventListeners() {
    // 添加提示词表单
    document.getElementById('promptForm').addEventListener('submit', handleAddPrompt);
    
    // 搜索输入
    document.getElementById('searchInput').addEventListener('input', handleSearch);
    
    // 标签筛选
    document.getElementById('tagFilter').addEventListener('change', handleTagFilter);
}

// =============== 提示词操作 ===============
function handleAddPrompt(event) {
    event.preventDefault();
    
    const title = document.getElementById('promptTitle').value.trim();
    const content = document.getElementById('promptContent').value.trim();
    const tagsInput = document.getElementById('promptTags').value.trim();
    
    if (!title || !content) {
        showNotification('请填写标题和内容', 'warning');
        return;
    }
    
    // 处理标签
    const tags = tagsInput ? tagsInput.split(',').map(tag => tag.trim()).filter(tag => tag) : [];
    
    // 创建新提示词
    const newPrompt = {
        id: ++promptData.lastId,
        title: title,
        content: content,
        tags: tags,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
    
    // 添加到数据
    promptData.prompts.unshift(newPrompt); // 添加到开头，最新的在前面
    
    // 更新标签集合
    tags.forEach(tag => promptData.tags.add(tag));
    
    // 保存数据
    savePromptData();
    
    // 更新界面
    renderPromptsList();
    updateTagFilter();
    
    // 清空表单
    document.getElementById('promptForm').reset();
    
    showNotification('提示词添加成功', 'success');
}

function editPrompt(id) {
    const prompt = promptData.prompts.find(p => p.id === id);
    if (!prompt) return;
    
    currentEditingId = id;
    
    // 填充编辑表单
    document.getElementById('editPromptId').value = id;
    document.getElementById('editPromptTitle').value = prompt.title;
    document.getElementById('editPromptContent').value = prompt.content;
    document.getElementById('editPromptTags').value = prompt.tags.join(', ');
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('editPromptModal'));
    modal.show();
}

function saveEditedPrompt() {
    const id = parseInt(document.getElementById('editPromptId').value);
    const title = document.getElementById('editPromptTitle').value.trim();
    const content = document.getElementById('editPromptContent').value.trim();
    const tagsInput = document.getElementById('editPromptTags').value.trim();
    
    if (!title || !content) {
        showNotification('请填写标题和内容', 'warning');
        return;
    }
    
    const prompt = promptData.prompts.find(p => p.id === id);
    if (!prompt) return;
    
    // 处理标签
    const newTags = tagsInput ? tagsInput.split(',').map(tag => tag.trim()).filter(tag => tag) : [];
    
    // 更新提示词
    prompt.title = title;
    prompt.content = content;
    prompt.tags = newTags;
    prompt.updatedAt = new Date().toISOString();
    
    // 重新构建标签集合
    promptData.tags.clear();
    promptData.prompts.forEach(p => {
        p.tags.forEach(tag => promptData.tags.add(tag));
    });
    
    // 保存数据
    savePromptData();
    
    // 更新界面
    renderPromptsList();
    updateTagFilter();
    
    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('editPromptModal'));
    modal.hide();
    
    showNotification('提示词修改成功', 'success');
}

function deletePrompt(id) {
    if (!confirm('确定要删除这个提示词吗？')) return;
    
    // 从数组中移除
    promptData.prompts = promptData.prompts.filter(p => p.id !== id);
    
    // 重新构建标签集合
    promptData.tags.clear();
    promptData.prompts.forEach(p => {
        p.tags.forEach(tag => promptData.tags.add(tag));
    });
    
    // 保存数据
    savePromptData();
    
    // 更新界面
    renderPromptsList();
    updateTagFilter();
}

function copyPrompt(id) {
    const prompt = promptData.prompts.find(p => p.id === id);
    if (!prompt) return;

    // 复制到剪贴板
    navigator.clipboard.writeText(prompt.content).then(() => {
        showNotification('提示词内容已复制到剪贴板', 'success');
    }).catch(err => {
        console.error('复制失败:', err);
        // 降级方案：使用传统方法复制
        fallbackCopyToClipboard(prompt.content);
    });
}



// 降级复制方案
function fallbackCopyToClipboard(text) {
    try {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
            showNotification('内容已复制到剪贴板', 'success');
        } else {
            showNotification('复制失败，请手动复制', 'error');
        }
    } catch (err) {
        console.error('降级复制也失败:', err);
        showNotification('复制失败，请手动复制', 'error');
    }
}

// =============== 搜索和筛选 ===============
function handleSearch(event) {
    searchQuery = event.target.value.toLowerCase();
    renderPromptsList();
}

function handleTagFilter(event) {
    activeTagFilter = event.target.value;
    renderPromptsList();
}

function clearFilters() {
    searchQuery = '';
    activeTagFilter = '';
    document.getElementById('searchInput').value = '';
    document.getElementById('tagFilter').value = '';
    renderPromptsList();
}

function getFilteredPrompts() {
    let filtered = promptData.prompts;
    
    // 搜索筛选
    if (searchQuery) {
        filtered = filtered.filter(prompt => 
            prompt.title.toLowerCase().includes(searchQuery) ||
            prompt.content.toLowerCase().includes(searchQuery)
        );
    }
    
    // 标签筛选
    if (activeTagFilter) {
        filtered = filtered.filter(prompt => 
            prompt.tags.includes(activeTagFilter)
        );
    }
    
    return filtered;
}

// =============== 界面渲染 ===============
function renderPromptsList() {
    const container = document.getElementById('promptsList');
    const filteredPrompts = getFilteredPrompts();
    const usageTips = document.getElementById('usage-tips');

    if (filteredPrompts.length === 0) {
        // 显示使用提示（仅在没有搜索/筛选时）
        if (usageTips && !searchQuery && !activeTagFilter && promptData.prompts.length === 0) {
            usageTips.style.display = 'block';
        }

        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-robot"></i>
                <h5>暂无提示词</h5>
                <p class="text-muted">${searchQuery || activeTagFilter ? '没有找到匹配的提示词' : '开始添加你的第一个AI提示词吧'}</p>
            </div>
        `;
        return;
    }

    // 隐藏使用提示
    if (usageTips) {
        usageTips.style.display = 'none';
    }
    
    container.innerHTML = filteredPrompts.map(prompt => `
        <div class="prompt-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="prompt-title">${escapeHtml(prompt.title)}</div>
                    <small class="text-muted">${formatDate(prompt.createdAt)}</small>
                </div>
                <div class="prompt-content">${escapeHtml(prompt.content)}</div>
                ${prompt.tags.length > 0 ? `
                    <div class="prompt-tags">
                        ${prompt.tags.map(tag => `<span class="prompt-tag">${escapeHtml(tag)}</span>`).join('')}
                    </div>
                ` : ''}
                <div class="prompt-actions">
                    <button class="btn btn-primary btn-sm" onclick="copyPrompt(${prompt.id})" title="复制内容">
                        <i class="fas fa-copy"></i> 复制
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="editPrompt(${prompt.id})" title="编辑">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="deletePrompt(${prompt.id})" title="删除">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}



function updateTagFilter() {
    const select = document.getElementById('tagFilter');
    const currentValue = select.value;
    
    select.innerHTML = '<option value="">所有标签</option>';
    
    Array.from(promptData.tags).sort().forEach(tag => {
        const option = document.createElement('option');
        option.value = tag;
        option.textContent = tag;
        if (tag === currentValue) {
            option.selected = true;
        }
        select.appendChild(option);
    });
}

// =============== 工具函数 ===============
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function formatDate(dateString) {
    try {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffDays === 0) {
            return '今天';
        } else if (diffDays === 1) {
            return '昨天';
        } else if (diffDays < 7) {
            return `${diffDays}天前`;
        } else if (diffDays < 30) {
            const weeks = Math.floor(diffDays / 7);
            return `${weeks}周前`;
        } else {
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }
    } catch (error) {
        return '未知';
    }
}

function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : type === 'warning' ? 'warning' : 'info'} position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 10000;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
    `;

    document.body.appendChild(notification);

    // 自动移除通知
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, type === 'success' ? 2000 : 5000);
}

// =============== 快捷键支持 ===============
document.addEventListener('keydown', function(event) {
    // Ctrl+S 保存当前编辑的提示词
    if (event.ctrlKey && event.key === 's') {
        event.preventDefault();
        const modal = document.getElementById('editPromptModal');
        if (modal && modal.classList.contains('show')) {
            saveEditedPrompt();
        } else {
            // 如果没有在编辑，尝试保存新提示词
            const form = document.getElementById('promptForm');
            if (form) {
                form.dispatchEvent(new Event('submit'));
            }
        }
    }

    // Escape 关闭模态框
    if (event.key === 'Escape') {
        const modal = document.getElementById('editPromptModal');
        if (modal && modal.classList.contains('show')) {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
        }
    }

    // Ctrl+F 聚焦搜索框
    if (event.ctrlKey && event.key === 'f') {
        event.preventDefault();
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }
});


