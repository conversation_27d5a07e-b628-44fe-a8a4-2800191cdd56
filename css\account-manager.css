/* 账号管理系统专用样式 */

:root {
    --primary-color: #1e3a8a;
    --primary-light: #3b82f6;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --border-color: #e2e8f0;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* 价值范围输入框样式 */
.value-range-inputs {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.value-range-inputs input {
    flex: 1;
    min-width: 0;
    font-size: 0.875rem;
}

.value-separator {
    color: var(--secondary-color);
    font-weight: 500;
    padding: 0 0.25rem;
    white-space: nowrap;
    font-size: 0.875rem;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .value-range-inputs {
        flex-direction: column;
        gap: 0.125rem;
    }

    .value-separator {
        display: none;
    }

    .value-range-inputs input {
        font-size: 0.75rem;
    }
}

/* 全局样式 */
body {
    background-color: #f1f5f9;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 导航栏 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

/* 统计面板 - 紧凑优化 */
.stats-panel {
    background: white;
    border-radius: var(--border-radius);
    padding: 0.5rem;
    box-shadow: var(--shadow);
    margin-bottom: 0.5rem;
}

.stat-card {
    display: flex;
    align-items: center;
    padding: 0.4rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--secondary-color);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-card.active {
    border-left-color: var(--success-color);
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.stat-card.pending {
    border-left-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.stat-card.rented {
    border-left-color: var(--warning-color);
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.stat-card.sold {
    border-left-color: var(--danger-color);
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
}

.stat-card.expired {
    border-left-color: var(--secondary-color);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.stat-card.disabled {
    opacity: 0.5;
    filter: grayscale(50%);
}

.stat-card.disabled:hover {
    transform: none;
    box-shadow: var(--shadow);
}

.stat-icon {
    font-size: 1.4rem;
    margin-right: 0.6rem;
    color: var(--primary-color);
    width: 40px;
    text-align: center;
}

.stat-info h3 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--dark-color);
}

.stat-info p {
    margin: 0;
    color: var(--secondary-color);
    font-size: 0.75rem;
}

/* 工具栏 - 紧凑优化 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 0.4rem 0.8rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 0.5rem;
}

.toolbar-left {
    display: flex;
    gap: 0.3rem;
}

.toolbar-right .search-box {
    position: relative;
    width: 300px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.search-box .form-control {
    padding-right: 2.5rem;
    flex: 1;
}

.search-box .search-icon {
    position: absolute;
    right: 70px; /* 为清除按钮留出空间 */
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-color);
    pointer-events: none;
    z-index: 1;
}

/* 筛选面板 - 紧凑优化 */
.filter-panel {
    background: white;
    padding: 0.4rem 0.8rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 0.5rem;
}

.filter-group {
    display: flex;
    align-items: center;
    margin-bottom: 0.3rem;
}

.filter-group:last-child {
    margin-bottom: 0;
}

.filter-group label {
    font-weight: 600;
    margin-right: 0.6rem;
    min-width: 60px;
    color: var(--dark-color);
    font-size: 0.85rem;
}

.filter-buttons {
    display: flex;
    gap: 0.3rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border-color);
    background: white;
    color: var(--secondary-color);
    border-radius: 15px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: var(--transition);
}

.filter-btn:hover {
    background: var(--light-color);
    border-color: var(--primary-light);
}

.filter-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 筛选器选项样式 */
.filter-options {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.form-check.form-switch {
    margin-bottom: 0;
}

.form-check-label {
    font-size: 0.85rem;
    color: var(--dark-color);
    margin-left: 0.5rem;
}

/* 账号表格 */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    table-layout: fixed;
    width: 100%;
    margin-bottom: 0;
}

.table-responsive {
    overflow-x: auto;
}

/* 表格紧凑布局 */
.table th,
.table td {
    border-left: none;
    border-right: none;
}

.table th:first-child,
.table td:first-child {
    padding-left: 0.5rem;
}

.table th:last-child,
.table td:last-child {
    padding-right: 0.5rem;
}

.table th {
    background: var(--light-color);
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    color: var(--dark-color);
    padding: 0;
    font-size: 0.85rem;
    vertical-align: middle;
    text-align: center;
    line-height: 1;
    height: 40px;
    position: relative;
}

.table th > * {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0 0.3rem;
    margin: 0;
    line-height: 1;
    font-size: inherit;
    font-weight: inherit;
}

/* 表头特殊对齐 */
.table th:first-child > *,
.table th:nth-child(2) > *,
.table th:nth-child(3) > * {
    justify-content: flex-start;
}

.table th:nth-child(6) > * {
    justify-content: flex-end;
}

.table td {
    padding: 0.3rem 0.2rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    line-height: 1.2;
    word-wrap: break-word;
    overflow-wrap: break-word;
    height: 45px;
}

.table tbody tr:hover {
    background-color: rgba(59, 130, 246, 0.05);
}

.account-row.status-active {
    border-left: 4px solid var(--success-color);
}

.account-row.status-pending {
    border-left: 4px solid #3b82f6;
}

.account-row.status-rented {
    border-left: 4px solid var(--warning-color);
}

.account-row.status-sold {
    border-left: 4px solid var(--danger-color);
}

.account-row.status-expired {
    border-left: 4px solid var(--secondary-color);
}

/* 过期账号半透明显示 */
.account-row.status-expired.faded {
    opacity: 0.6;
    background-color: rgba(248, 250, 252, 0.8);
}

.account-row.status-expired.faded:hover {
    opacity: 0.8;
    background-color: rgba(59, 130, 246, 0.05);
}

/* 平台信息样式 */
.platform-info {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    text-align: left;
}

.platform-icon {
    font-size: 1.1rem;
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.platform-name {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 0.9rem;
    line-height: 1.2;
}

.account-type {
    font-size: 0.75rem;
    color: var(--secondary-color);
    display: block;
    line-height: 1.2;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1;
}

.status-active {
    background: #dcfce7;
    color: #166534;
}

.status-pending {
    background: #dbeafe;
    color: #1e40af;
}

.status-rented {
    background: #fef3c7;
    color: #92400e;
}

.status-sold {
    background: #fecaca;
    color: #991b1b;
}

.status-expired {
    background: #f1f5f9;
    color: #475569;
}

/* 表格内容样式 */
.account-info {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--dark-color);
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 0.2rem;
    white-space: nowrap;
    padding: 0;
}

.account-username {
    font-weight: 600;
    margin-bottom: 0;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.password-field {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.2rem;
    padding: 0;
}

.password-text {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    color: var(--dark-color);
    line-height: 1.2;
    margin: 0;
}

.password-toggle {
    background: none;
    border: none;
    color: var(--secondary-color);
    cursor: pointer;
    padding: 0.2rem;
    font-size: 0.8rem;
}

/* 操作按钮样式 */
.action-buttons {
    display: flex;
    gap: 0.25rem;
    justify-content: center;
    align-items: center;
}

.table-meta {
    font-size: 0.75rem;
    color: var(--secondary-color);
}

/* 模态框样式 */
.modal-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-body {
    padding: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .toolbar {
        flex-direction: column;
        gap: 1rem;
    }

    .toolbar-right .search-box {
        width: 100%;
    }

    .filter-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .filter-group label {
        min-width: auto;
    }

    .stat-card {
        margin-bottom: 1rem;
    }

    /* 移动端表格优化 */
    .table-responsive {
        font-size: 0.75rem;
        border-radius: 8px;
        overflow-x: auto;
    }

    .account-username {
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .password-text {
        font-size: 0.75rem;
        max-width: 60px;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* 移动端卡片式布局 */
    .account-row {
        display: block !important;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        margin-bottom: 0.75rem;
        padding: 0.75rem;
        background: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .account-row td {
        display: block !important;
        border: none !important;
        padding: 0.25rem 0 !important;
        text-align: left !important;
    }

    .account-row td:before {
        content: attr(data-label) ': ';
        font-weight: 600;
        color: #495057;
        display: inline-block;
        width: 80px;
        flex-shrink: 0;
    }

    .account-row td:first-child:before {
        content: '';
    }

    .account-row .action-buttons {
        margin-top: 0.5rem;
        justify-content: flex-end;
    }

    /* 隐藏表头 */
    .table thead {
        display: none;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.account-card {
    animation: fadeInUp 0.3s ease-out;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 2rem;
    color: var(--secondary-color);
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--secondary-color);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--border-color);
}

/* 紧凑模式额外优化 */
.btn {
    padding: 0.3rem 0.6rem;
    font-size: 0.8rem;
}

/* 工具栏按钮样式 */
.toolbar-left .btn {
    margin-right: 0.2rem;
    font-size: 0.8rem;
}

.toolbar-left .btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
}

.toolbar-left .btn-info:hover {
    background-color: #0891b2;
    border-color: #0891b2;
}

.toolbar-left .btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.toolbar-left .btn-warning:hover {
    background-color: #d97706;
    border-color: #d97706;
}

/* 简化批量导入样式 */
.import-template {
    background: var(--light-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.import-result {
    background: var(--light-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.btn-sm {
    padding: 0.2rem 0.4rem;
    font-size: 0.7rem;
}

.form-control, .form-select {
    padding: 0.4rem 0.6rem;
    font-size: 0.85rem;
}

.form-label {
    font-size: 0.85rem;
    margin-bottom: 0.3rem;
}

.mb-3 {
    margin-bottom: 0.8rem !important;
}

.container-fluid {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

/* 减少行间距 */
.row.mb-4 {
    margin-bottom: 0.8rem !important;
}



/* 确保表格内容左对齐的特殊情况 */
.table td:first-child,
.table td:nth-child(2),
.table td:nth-child(3) {
    text-align: left;
}

/* 价值列右对齐 */
.table td:nth-child(6) {
    text-align: right;
}
