<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>饮食记录 - ST计划</title>
    <!-- 添加网站图标 -->
    <link rel="icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <link rel="shortcut icon" href="assets/img/favicon.svg" type="image/svg+xml">
    
    <!-- 样式文件 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    <link rel="stylesheet" type="text/css" href="css/main.css">
    <link rel="stylesheet" type="text/css" href="css/diet-record.css">
    
    <!-- 密码验证层样式 -->
    <style>
        #password-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.95);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            flex-direction: column;
        }
        .password-container {
            width: 320px;
            max-width: 90%;
            background-color: #fff;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .password-container h3 {
            margin-bottom: 20px;
            color: #333;
        }
        .password-container .form-group {
            margin-bottom: 20px;
        }
        .password-error {
            color: #dc3545;
            margin-top: 10px;
            display: none;
        }
    </style>
</head>

<body class="diet-record-page">
    <!-- 密码验证层 -->
    <div id="password-overlay" style="display: none">
        <div class="password-container">
            <h3>ST计划访问验证</h3>
            <div class="form-group">
                <input type="password" id="access-password" class="form-control" placeholder="请输入访问密码">
            </div>
            <button id="password-submit" class="btn btn-primary w-100">确认</button>
            <div id="password-error" class="password-error">密码错误，请重试</div>
            <div class="form-check mt-3">
                <input class="form-check-input" type="checkbox" id="remember-password">
                <label class="form-check-label" for="remember-password">
                    在此设备上记住我
                </label>
            </div>
        </div>
    </div>

    <div class="container-fluid mt-3" id="main-content" style="display: none">
        <!-- 页面头部 -->
        <div class="row mb-4">
            <div class="col-6">
                <h2><i class="fas fa-utensils me-2"></i>饮食记录</h2>
            </div>
            <div class="col-6 text-end">
                <button class="btn btn-outline-secondary btn-sm" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left me-2"></i>返回主页
                </button>
            </div>
        </div>

        <div class="row">
            <!-- 左侧控制面板 -->
            <div class="col-md-3">
                <!-- 添加饮食记录 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-plus me-2"></i>添加饮食记录
                    </div>
                    <div class="card-body">
                        <form id="dietForm">
                            <div class="mb-3">
                                <label class="form-label">日期:</label>
                                <input class="form-control" id="dietDate" type="date" />
                            </div>
                            <div class="mb-3">
                                <label class="form-label">时间段:</label>
                                <select class="form-select" id="mealTime">
                                    <option value="morning">上午</option>
                                    <option value="afternoon">下午</option>
                                    <option value="evening">晚上</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">食物名称:</label>
                                <input class="form-control" id="foodName" type="text" placeholder="例如：米饭、鸡蛋、苹果" list="foodHistory" />
                                <datalist id="foodHistory">
                                    <!-- 历史食物选项将在这里动态添加 -->
                                </datalist>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">分量/数量:</label>
                                <input class="form-control" id="foodAmount" type="text" placeholder="例如：1碗、2个、100g" />
                            </div>

                            <div class="mb-3">
                                <label class="form-label">备注:</label>
                                <textarea class="form-control" id="foodNote" rows="2" placeholder="口感、心情等（可选）"></textarea>
                            </div>
                            <button class="btn btn-primary w-100" type="submit" id="submitBtn">
                                <i class="fas fa-plus me-2" id="submitIcon"></i><span id="submitText">添加记录</span>
                            </button>
                            <button class="btn btn-outline-secondary w-100 mt-2" type="button" id="cancelBtn" style="display: none;" onclick="DietRecord.cancelEdit()">
                                <i class="fas fa-times me-2"></i>取消编辑
                            </button>
                        </form>
                    </div>
                </div>

                <!-- 食品库 -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-book me-2"></i>食品库
                    </div>
                    <div class="card-body">
                        <!-- 添加食物 -->
                        <div class="food-add-section mb-3">
                            <div class="input-group input-group-sm">
                                <input type="text" class="form-control" id="newFoodName" placeholder="添加食物">
                                <button class="btn btn-success" type="button" id="addFoodBtn">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 食物卡片列表 -->
                        <div class="food-cards-container" id="foodCardsContainer">
                            <!-- 食物卡片将在这里动态显示 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧记录展示 -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-list me-2"></i>饮食记录列表</span>
                        <div class="btn-group btn-group-sm" role="group">
                            <input type="radio" class="btn-check" name="viewMode" id="viewToday" checked>
                            <label class="btn btn-outline-primary" for="viewToday">今天</label>

                            <input type="radio" class="btn-check" name="viewMode" id="viewWeek">
                            <label class="btn btn-outline-primary" for="viewWeek">本周</label>

                            <input type="radio" class="btn-check" name="viewMode" id="viewAll">
                            <label class="btn btn-outline-primary" for="viewAll">全部</label>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="dietRecords">
                            <!-- 饮食记录将在这里动态显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- 脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.min.js"></script>
    <script src="js/diet-record.js"></script>
    
    <script>
        // 密码验证功能
        document.addEventListener('DOMContentLoaded', function() {
            const passwordOverlay = document.getElementById('password-overlay');
            const passwordInput = document.getElementById('access-password');
            const passwordSubmit = document.getElementById('password-submit');
            const passwordError = document.getElementById('password-error');
            const rememberPassword = document.getElementById('remember-password');
            const mainContent = document.getElementById('main-content');
            
            // 默认密码
            const correctPassword = 'st6666';
            
            // 检查是否已经记住密码
            if (localStorage.getItem('st_authenticated') === 'true') {
                passwordOverlay.style.display = 'none';
                mainContent.style.display = 'block';
                // 初始化饮食记录功能
                if (typeof DietRecord !== 'undefined') {
                    DietRecord.init();
                }
            } else {
                passwordOverlay.style.display = 'flex';
                
                // 密码提交按钮事件
                passwordSubmit.addEventListener('click', function() {
                    verifyPassword();
                });
                
                // 按回车键也可以提交密码
                passwordInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        verifyPassword();
                    }
                });
            }
            
            // 验证密码函数
            function verifyPassword() {
                const inputPassword = passwordInput.value;
                
                if(inputPassword === correctPassword) {
                    // 密码正确
                    passwordOverlay.style.display = 'none';
                    mainContent.style.display = 'block';
                    
                    // 如果选择了记住密码，将验证状态保存到localStorage
                    if(rememberPassword.checked) {
                        localStorage.setItem('st_authenticated', 'true');
                    }
                    
                    // 初始化饮食记录功能
                    if (typeof DietRecord !== 'undefined') {
                        DietRecord.init();
                    }
                } else {
                    // 密码错误
                    passwordError.style.display = 'block';
                    passwordInput.value = '';
                    passwordInput.focus();
                }
            }
        });
    </script>
</body>
</html>
