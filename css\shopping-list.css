/* 购物清单样式 */

/* 确保Font Awesome图标正确显示 */
.fas, .far, .fab, .fa {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands" !important;
    font-weight: 900 !important;
    display: inline-block !important;
}

.fas::before, .far::before, .fab::before, .fa::before {
    display: inline-block !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

/* 分类管理样式 */
.categories-container {
    max-height: 200px;
    overflow-y: auto;
    /* 防止滚动条闪烁 */
    scrollbar-width: thin;
    scrollbar-color: #ccc transparent;
}

/* 自定义滚动条样式 (Webkit) */
.categories-container::-webkit-scrollbar {
    width: 6px;
}

.categories-container::-webkit-scrollbar-track {
    background: transparent;
}

.categories-container::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 3px;
}

.categories-container::-webkit-scrollbar-thumb:hover {
    background-color: #999;
}

.category-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 5px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.85rem;
    transition: background-color 0.2s ease, border-color 0.2s ease;
    /* 移除 transform 动画以防止布局闪烁 */
}

.category-item:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
    /* 移除 transform: translateX(2px); 以防止闪烁 */
}

.category-item.active {
    background-color: #3498db;
    color: white;
    border-color: #3498db;
}

.category-name {
    flex: 1;
    cursor: pointer;
}

.category-actions {
    display: flex;
    gap: 5px;
}

.category-actions .btn {
    padding: 2px 6px;
    font-size: 0.7rem;
    border: none;
}

.delete-category-btn {
    background-color: #e74c3c;
    color: white;
}

.delete-category-btn:hover {
    background-color: #c0392b;
}

/* 统计信息样式 */
.stats-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.stat-item {
    text-align: center;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.stat-label {
    font-size: 0.75rem;
    color: #6c757d;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
}

/* 购物项目样式 */
.shopping-items-container {
    max-height: 600px;
    overflow-y: auto;
}

.shopping-item {
    display: flex;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.shopping-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.shopping-item.completed {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-left: 4px solid #28a745;
    opacity: 0.8;
    transform: scale(0.98);
    transition: all 0.3s ease;
}

.shopping-item.completed .item-name {
    text-decoration: line-through;
    color: #155724;
    font-weight: 500;
}

.shopping-item.completed .item-checkbox {
    background-color: #28a745;
    border-color: #28a745;
}

.shopping-item.completed .item-checkbox::after {
    content: '✓';
    color: white;
    font-weight: bold;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.item-checkbox {
    margin-right: 15px;
    transform: scale(1.2);
}

.item-content {
    flex: 1;
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 15px;
    align-items: center;
}

.item-main {
    display: flex;
    flex-direction: column;
}

.item-name-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 4px;
}

.item-name {
    font-weight: 600;
    font-size: 0.95rem;
    color: #2c3e50;
    flex: 1;
    min-width: 0; /* 允许文本截断 */
}

.item-category {
    font-size: 0.75rem;
    background-color: #ecf0f1;
    padding: 4px 12px;
    border-radius: 14px;
    display: inline-block;
    white-space: nowrap;
    flex-shrink: 0;
    font-weight: 500;
    border: 1px solid transparent;
    transition: all 0.2s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .item-name-row {
        gap: 8px;
    }

    .item-category {
        font-size: 0.7rem;
        padding: 3px 8px;
    }

    .item-name {
        font-size: 0.9rem;
    }
}

.item-quantity {
    font-size: 0.85rem;
    color: #34495e;
    font-weight: 500;
}

.item-budget {
    font-size: 0.85rem;
    color: #27ae60;
    font-weight: 600;
}

.item-deadline {
    font-size: 0.8rem;
    color: #e67e22;
}

.item-deadline.urgent {
    color: #e74c3c;
    font-weight: 600;
}

.item-deadline.overdue {
    color: #c0392b;
    font-weight: 600;
    background-color: #fadbd8;
    padding: 2px 6px;
    border-radius: 4px;
}

.item-actions {
    display: flex;
    gap: 5px;
    margin-left: 15px;
}

.item-actions .btn {
    padding: 5px 8px;
    font-size: 0.75rem;
}

.item-note {
    grid-column: 1 / -1;
    margin-top: 8px;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 0.8rem;
    color: #6c757d;
    border-left: 3px solid #3498db;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #bdc3c7;
}

.empty-state h5 {
    margin-bottom: 10px;
    color: #7f8c8d;
}

.empty-state p {
    margin-bottom: 20px;
    font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .item-content {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .item-actions {
        margin-left: 0;
        margin-top: 10px;
        justify-content: flex-end;
    }
    
    .stats-container {
        grid-template-columns: 1fr;
    }
}

/* 过期提醒动画 */
@keyframes urgentPulse {
    0% { background-color: #fadbd8; }
    50% { background-color: #f1948a; }
    100% { background-color: #fadbd8; }
}

.shopping-item.overdue {
    animation: urgentPulse 2s infinite;
}

/* 完成状态动画 */
.shopping-item.just-completed {
    animation: completeAnimation 0.6s ease-in-out;
}

@keyframes completeAnimation {
    0% {
        transform: scale(1);
        background-color: #fff;
    }
    25% {
        transform: scale(1.05);
        background-color: #d4edda;
        box-shadow: 0 0 20px rgba(40, 167, 69, 0.3);
    }
    50% {
        transform: scale(1.02);
        background-color: #d4edda;
    }
    100% {
        transform: scale(0.98);
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    }
}

/* 取消完成状态动画 */
.shopping-item.just-uncompleted {
    animation: uncompleteAnimation 0.4s ease-in-out;
}

@keyframes uncompleteAnimation {
    0% {
        transform: scale(0.98);
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.02);
        background-color: #fff;
    }
    100% {
        transform: scale(1);
        background-color: #fff;
        opacity: 1;
    }
}
