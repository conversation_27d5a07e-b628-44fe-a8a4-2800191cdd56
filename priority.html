<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优先级管理 - ST计划系统</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📋</text></svg>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/main.css" rel="stylesheet">
    <link href="css/priority.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h1 class="header-title">
                            <i class="fas fa-sort-amount-down me-2"></i>
                            优先级管理
                        </h1>
                        <p class="header-subtitle">拖拽排序，从上到下优先级递减</p>
                    </div>
                    <div class="col-md-6 text-end">
                        <button class="btn btn-outline-light btn-sm" onclick="window.location.href='index.html'">
                            <i class="fas fa-home me-1"></i>返回主页
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="container mt-2">
            <div class="row">
                <!-- 主要内容区域 -->
                <div class="col-lg-10 mx-auto">
                    <!-- 添加新事项 -->
                    <div class="card mb-2">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-plus me-2"></i>添加新事项
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="addPriorityForm" class="row g-3">
                                <div class="col-md-8">
                                    <input type="text" class="form-control" id="priorityItemText"
                                           placeholder="输入待办事项，用空格分隔可批量添加..." required>
                                </div>
                                <div class="col-md-4">
                                    <div class="row g-2">
                                        <div class="col-3">
                                            <button type="submit" class="btn btn-primary w-100">
                                                <i class="fas fa-plus"></i><span class="btn-text ms-1">添加</span>
                                            </button>
                                        </div>
                                        <div class="col-3">
                                            <button type="button" class="btn btn-outline-success w-100" onclick="window.markAllCompleted && window.markAllCompleted()">
                                                <i class="fas fa-check-double"></i><span class="btn-text ms-1">全部完成</span>
                                            </button>
                                        </div>
                                        <div class="col-3">
                                            <button type="button" class="btn btn-outline-warning w-100" onclick="window.clearCompleted && window.clearCompleted()">
                                                <i class="fas fa-broom"></i><span class="btn-text ms-1">清除已完成</span>
                                            </button>
                                        </div>
                                        <div class="col-3">
                                            <button type="button" class="btn btn-outline-danger w-100" onclick="window.clearAll && window.clearAll()">
                                                <i class="fas fa-trash"></i><span class="btn-text ms-1">清空全部</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>

                            <!-- 批量导入预览 -->
                            <div id="batchPreview" style="display: none;" class="mt-3"></div>
                        </div>
                    </div>

                    <!-- 列表管理区域 -->
                    <div class="card mb-3">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-layer-group me-2"></i>列表管理
                            </h5>
                            <button class="btn btn-outline-primary btn-sm" onclick="window.priorityManager && window.priorityManager.addNewList()">
                                <i class="fas fa-plus me-1"></i>新建列表
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="listTabs" class="list-tabs">
                                <!-- 列表标签将在这里动态生成 -->
                            </div>
                        </div>
                    </div>

                    <!-- 优先级列表容器 -->
                    <div id="priorityListsContainer">
                        <!-- 多个优先级列表将在这里动态生成 -->
                    </div>


                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.min.js"></script>
    
    <!-- 优先级管理 JS -->
    <script src="js/priority.js"></script>
</body>
</html>
