<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购物清单 - 个人管理系统</title>
    <!-- 添加网站图标 -->
    <link rel="icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <link rel="shortcut icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <!-- 内联PNG图标，用于不支持SVG的浏览器 -->
    <link rel="icon" href="data:image/png;base64,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" type="image/png">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/shopping-list.css">
</head>
<body>
    <div class="container-fluid mt-3">
        <!-- 顶部导航 -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-shopping-cart me-2"></i>购物清单</h2>
                    <button class="btn btn-outline-secondary" onclick="window.location.href='index.html'">
                        <i class="fas fa-arrow-left me-2"></i>返回主页
                    </button>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 左侧控制面板 -->
            <div class="col-md-3">
                <!-- 分类管理 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-tags me-2"></i>分类管理
                    </div>
                    <div class="card-body">
                        <!-- 添加分类 -->
                        <div class="mb-3">
                            <div class="input-group input-group-sm">
                                <input type="text" class="form-control" id="newCategoryName" placeholder="添加分类">
                                <button class="btn btn-success" type="button" id="addCategoryBtn">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- 分类列表 -->
                        <div class="categories-container" id="categoriesContainer">
                            <!-- 分类将在这里动态显示 -->
                        </div>
                    </div>
                </div>

                <!-- 添加购物项目 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-plus me-2"></i>添加购物项目
                    </div>
                    <div class="card-body">
                        <form id="shoppingForm">
                            <div class="mb-3">
                                <label class="form-label">分类:</label>
                                <select class="form-select" id="itemCategory">
                                    <option value="">选择分类</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">物品名称:</label>
                                <input class="form-control" id="itemName" type="text" placeholder="例如：苹果、牛奶、面包" />
                            </div>
                            <div class="mb-3">
                                <label class="form-label">数量:</label>
                                <input class="form-control" id="itemQuantity" type="text" placeholder="例如：2个、1袋、500g" />
                            </div>
                            <div class="mb-3">
                                <label class="form-label">预算 (元):</label>
                                <input class="form-control" id="itemBudget" type="number" step="0.01" placeholder="0.00" />
                            </div>
                            <div class="mb-3">
                                <label class="form-label">截止日期:</label>
                                <input class="form-control" id="itemDeadline" type="date" />
                            </div>
                            <div class="mb-3">
                                <label class="form-label">备注:</label>
                                <textarea class="form-control" id="itemNote" rows="2" placeholder="品牌偏好、特殊要求等（可选）"></textarea>
                            </div>
                            <button class="btn btn-primary w-100" type="submit" id="submitBtn">
                                <i class="fas fa-plus me-2" id="submitIcon"></i><span id="submitText">添加项目</span>
                            </button>
                            <button class="btn btn-outline-secondary w-100 mt-2" type="button" id="cancelBtn" style="display: none;" onclick="ShoppingList.cancelEdit()">
                                <i class="fas fa-times me-2"></i>取消编辑
                            </button>
                        </form>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-bar me-2"></i>统计信息
                    </div>
                    <div class="card-body">
                        <div class="stats-container">
                            <div class="stat-item">
                                <div class="stat-label">总项目数</div>
                                <div class="stat-value" id="totalItems">0</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">已完成</div>
                                <div class="stat-value" id="completedItems">0</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">总预算</div>
                                <div class="stat-value" id="totalBudget">¥0</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">已花费</div>
                                <div class="stat-value" id="spentBudget">¥0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧购物清单显示区域 -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-list me-2"></i>购物清单
                        </div>
                        <div class="view-controls">
                            <div class="btn-group btn-group-sm" role="group">
                                <input type="radio" class="btn-check" name="viewFilter" id="allItems" checked>
                                <label class="btn btn-outline-primary" for="allItems">全部</label>
                                
                                <input type="radio" class="btn-check" name="viewFilter" id="pendingItems">
                                <label class="btn btn-outline-primary" for="pendingItems">待购买</label>
                                
                                <input type="radio" class="btn-check" name="viewFilter" id="completedItems">
                                <label class="btn btn-outline-primary" for="completedItems">已完成</label>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="shopping-items-container" id="shoppingItemsContainer">
                            <!-- 购物项目将在这里动态显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.min.js"></script>
    
    <!-- 购物清单功能脚本 -->
    <script src="js/shopping-list.js"></script>
    
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            ShoppingList.init();
        });
    </script>
</body>
</html>
