<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>AI提示词管理 - ST计划</title>
    <!-- 添加网站图标 -->
    <link rel="icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <link rel="shortcut icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <!-- 样式文件 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    <link rel="stylesheet" type="text/css" href="css/main.css">
    
    <style>
        body {
            overflow-x: hidden;
        }
        
        /* 紧凑设计样式 */
        .container {
            max-width: 1400px;
        }
        
        .card {
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .card-header {
            padding: 0.5rem 0.75rem;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .card-body {
            padding: 0.75rem;
        }
        
        /* 提示词卡片样式 */
        .prompt-card {
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            margin-bottom: 0.75rem;
            transition: all 0.2s ease;
        }
        
        .prompt-card:hover {
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0,123,255,0.15);
        }
        
        .prompt-card .card-body {
            padding: 0.6rem;
        }
        
        .prompt-title {
            font-size: 0.95rem;
            font-weight: 600;
            margin-bottom: 0.4rem;
            color: #333;
        }
        
        .prompt-content {
            font-size: 0.85rem;
            color: #666;
            line-height: 1.4;
            max-height: 3.6rem;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }
        
        .prompt-tags {
            margin-top: 0.5rem;
        }
        
        .prompt-tag {
            display: inline-block;
            background-color: #e9ecef;
            color: #495057;
            padding: 0.15rem 0.4rem;
            border-radius: 3px;
            font-size: 0.75rem;
            margin-right: 0.3rem;
            margin-bottom: 0.2rem;
        }
        
        .prompt-actions {
            margin-top: 0.5rem;
            display: flex;
            gap: 0.3rem;
        }
        
        .btn-sm {
            padding: 0.2rem 0.5rem;
            font-size: 0.8rem;
        }
        
        /* 表单样式 */
        .form-control, .form-select {
            font-size: 0.9rem;
            padding: 0.4rem 0.6rem;
        }
        
        .form-label {
            font-size: 0.85rem;
            font-weight: 500;
            margin-bottom: 0.3rem;
        }
        
        .mb-3 {
            margin-bottom: 0.75rem !important;
        }
        
        /* 搜索和筛选样式 */
        .search-filter-bar {
            background-color: #f8f9fa;
            padding: 0.75rem;
            border-radius: 6px;
            margin-bottom: 1rem;
        }
        
        /* 统计信息样式 */
        .stats-container {
            background-color: #f8f9fa;
            padding: 0.6rem;
            border-radius: 6px;
            margin-bottom: 1rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 0.3rem;
        }
        
        .stat-number {
            font-size: 1.2rem;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            font-size: 0.75rem;
            color: #666;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0.75rem;
            }

            /* 移动端布局调整 */
            .row {
                flex-direction: column;
            }

            .col-md-4,
            .col-md-8 {
                width: 100%;
                margin-bottom: 1rem;
            }

            .card {
                margin-bottom: 0.75rem;
                border-radius: 12px;
            }

            .card-header {
                padding: 0.75rem;
                border-radius: 12px 12px 0 0;
            }

            .card-body {
                padding: 0.75rem;
            }

            .prompt-card .card-body {
                padding: 0.75rem;
            }

            .form-control, .form-select {
                padding: 0.75rem;
                border-radius: 8px;
                min-height: 44px;
            }

            textarea.form-control {
                min-height: 120px;
            }

            .btn {
                font-size: 1rem;
                padding: 0.75rem 1rem;
                border-radius: 8px;
                min-height: 44px;
            }

            .btn-sm {
                font-size: 0.875rem;
                padding: 0.5rem 0.75rem;
                min-height: 36px;
            }

            .search-filter-bar {
                padding: 0.75rem;
                margin-bottom: 0.75rem;
                border-radius: 12px;
            }

            .search-filter-bar .row > div {
                width: 100%;
                margin-bottom: 0.75rem;
            }

            .search-filter-bar .row > div:last-child {
                margin-bottom: 0;
            }
        }
        
        /* 顶部导航按钮样式 */
        .top-nav-buttons {
            margin-bottom: 1rem;
        }
        
        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
        
        .empty-state i {
            font-size: 3rem;
            color: #ccc;
            margin-bottom: 1rem;
        }
    </style>
</head>

<body>
    <div class="container mt-3">
        <!-- 顶部导航按钮 -->
        <div class="row mb-3">
            <div class="col-12 d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-robot me-2"></i>AI提示词管理
                </h4>
                <button class="btn btn-primary btn-sm" onclick="window.location.href='index.html'">
                    <i class="fas fa-home me-1" style="font-size: 14px;"></i>🏠 返回主页
                </button>
            </div>
        </div>

        <div class="row">
            <!-- 左侧操作面板 -->
            <div class="col-md-4">
                <div class="card mb-3">
                    <div class="card-header">
                        <i class="fas fa-plus me-2"></i>添加提示词
                    </div>
                    <div class="card-body">
                        <form id="promptForm">
                            <div class="mb-3">
                                <label class="form-label">标题:</label>
                                <input class="form-control" id="promptTitle" type="text" placeholder="输入提示词标题" required />
                            </div>
                            <div class="mb-3">
                                <label class="form-label">内容:</label>
                                <textarea class="form-control" id="promptContent" rows="12" placeholder="输入提示词内容" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">标签:</label>
                                <input class="form-control" id="promptTags" type="text" placeholder="用逗号分隔多个标签" />
                                <small class="text-muted">例如: 编程,翻译,写作</small>
                            </div>
                            <button class="btn btn-primary w-100" type="submit">
                                <i class="fas fa-save me-2"></i>保存提示词
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 右侧内容区域 -->
            <div class="col-md-8">
                <!-- 搜索和筛选栏 -->
                <div class="search-filter-bar">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="searchInput" placeholder="搜索提示词标题或内容...">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <select class="form-select" id="tagFilter">
                                <option value="">所有标签</option>
                                <!-- 标签选项将动态生成 -->
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                                <i class="fas fa-times"></i> 清除
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 提示词列表 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>提示词列表</span>
                        <small class="text-muted">按创建时间排序</small>
                    </div>
                    <div class="card-body">
                        <!-- 使用提示 -->
                        <div id="usage-tips" class="alert alert-info" style="display: none;">
                            <h6><i class="fas fa-lightbulb me-2"></i>使用提示</h6>
                            <ul class="mb-0 small">
                                <li>点击"复制"按钮复制提示词内容，点击"全部"复制标题+内容</li>
                                <li>使用标签对提示词进行分类管理，便于快速查找</li>
                                <li>支持按标题或内容搜索，快速定位需要的提示词</li>
                                <li>数据自动保存到本地，并可通过云同步功能备份</li>
                            </ul>
                        </div>

                        <div id="promptsList">
                            <!-- 提示词卡片将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑提示词模态框 -->
    <div class="modal fade" id="editPromptModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>编辑提示词
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editPromptForm">
                        <input type="hidden" id="editPromptId">
                        <div class="mb-3">
                            <label class="form-label">标题:</label>
                            <input class="form-control" id="editPromptTitle" type="text" required />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">内容:</label>
                            <textarea class="form-control" id="editPromptContent" rows="12" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">标签:</label>
                            <input class="form-control" id="editPromptTags" type="text" />
                            <small class="text-muted">用逗号分隔多个标签</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveEditedPrompt()">
                        <i class="fas fa-save me-2"></i>保存修改
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.min.js"></script>
    <script src="js/prompt-manager.js"></script>
</body>
</html>
