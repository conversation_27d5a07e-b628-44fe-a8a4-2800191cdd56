<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>常用网站导航</title>
    <!-- 添加网站图标 -->
    <link rel="icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    <!-- 添加 SortableJS 库 -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.14.0/Sortable.min.js"></script>
    <style>
        :root {
            --primary-color: #4285f4;
            --hover-color: #2d6ada;
            --light-bg: #f8f9fa;
            --dark-bg: #343a40;
            --card-shadow: rgba(0, 0, 0, 0.1);
            
            /* 添加网站卡片颜色变量 */
            --card-color-1: #e8f5e9;
            --card-color-2: #e3f2fd;
            --card-color-3: #fff8e1;
            --card-color-4: #f3e5f5;
            --card-color-5: #e0f7fa; 
            --card-color-6: #ffebee;
            
            /* 添加分类卡片颜色变量 */
            --category-gradient-1: linear-gradient(135deg, #f5f7fa, #e4e8f0);
        }
        
        body {
            background-color: var(--light-bg);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding-bottom: 1rem;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), #6c5ce7);
            color: white;
            padding: 1rem 0;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .action-bar {
            padding: 0.5rem 0;
            margin-bottom: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px var(--card-shadow);
        }
        
        .website-card {
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 4px var(--card-shadow);
            height: 100%;
            background: white;
        }
        
        /* 网站卡片背景颜色循环 */
        .website-card:nth-child(6n+1) {
            background-color: var(--card-color-1);
        }
        
        .website-card:nth-child(6n+2) {
            background-color: var(--card-color-2);
        }
        
        .website-card:nth-child(6n+3) {
            background-color: var(--card-color-3);
        }
        
        .website-card:nth-child(6n+4) {
            background-color: var(--card-color-4);
        }
        
        .website-card:nth-child(6n+5) {
            background-color: var(--card-color-5);
        }
        
        .website-card:nth-child(6n) {
            background-color: var(--card-color-6);
        }
        
        .website-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 16px var(--card-shadow);
        }
        
        /* 网站卡片的删除按钮 */
        .website-delete-btn {
            position: absolute;
            bottom: 5px;
            right: 5px;
            background-color: rgba(220, 53, 69, 0.9);
            color: white;
            border: none;
            border-radius: 50%;
            width: 26px;
            height: 26px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            font-size: 14px;
            opacity: 0;  /* 完全隐藏 */
            transition: opacity 0.3s ease, transform 0.2s ease;
            z-index: 20;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
        
        /* 长按显示删除按钮 */
        .website-card.show-delete .website-delete-btn {
            opacity: 1;
        }
        
        .website-delete-btn:hover {
            background-color: #bd2130;
            transform: scale(1.1);
        }
        
        /* 确保点击删除按钮时不会触发卡片的点击事件 */
        .website-card-clickable {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 10;
        }
        
        /* 移除悬停显示网址的效果，改为点击显示 */
        .website-card-info {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            transform: none;
            z-index: 15;
            text-align: center;
            padding: 5px;
            pointer-events: none;
            font-size: 0.85em;
        }

        /* 访问次数显示样式 */
        .website-visit-count {
            position: absolute;
            top: 5px;
            right: 5px;
            background-color: rgba(0, 123, 255, 0.9);
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 0.7em;
            font-weight: 600;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 20;
            pointer-events: none;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .website-card:hover .website-visit-count {
            opacity: 1;
        }
        
        .website-card.show-info .website-card-info {
            opacity: 1;
            pointer-events: auto;
        }
        
        .website-card-info .url {
            font-size: 0.75em;
            margin-top: 5px;
            word-break: break-all;
        }
        
        .website-card-info .btn-group {
            margin-top: 8px;
        }
        
        .website-card-info .btn-group .btn {
            padding: 0.1rem 0.5rem;
            font-size: 0.75em;
        }
        
        .website-card .card-body {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 0.3rem;
            height: 100%;
            min-height: 2.8rem; /* 确保卡片有最小高度 */
        }
        
        .website-card .card-title {
            margin-bottom: 0;
            font-weight: 600;
            color: #333;
            font-size: 0.95rem;
            line-height: 1.2;
            width: 100%; /* 让标题占满宽度 */
            overflow-wrap: break-word; /* 长单词自动换行 */
            text-shadow: 0 1px 1px rgba(255, 255, 255, 0.7);
        }
        
        .website-url {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 6px;
            transform: translateY(100%);
            transition: transform 0.3s ease;
            font-size: 0.8em;
            text-align: center;
        }
        
        .website-card:hover .website-url {
            transform: translateY(0);
        }
        
        .category-section {
            margin-bottom: 1rem;
            background: var(--category-gradient-1);
            border-radius: 12px;
            padding: 0.75rem;
            box-shadow: 0 3px 8px var(--card-shadow);
            position: relative;
            border-left: 4px solid var(--primary-color);
        }
        
        .category-title {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 0.75rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.07);
            padding-right: 3rem; /* 为右侧的删除按钮留出空间 */
        }
        
        .category-title h4 {
            margin: 0;
            font-weight: 600;
            color: #333;
            text-shadow: 0 1px 1px rgba(255, 255, 255, 0.7);
            position: relative;
            padding-left: 0.5rem;
            cursor: pointer;
        }
        
        .category-title h4::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background-color: var(--primary-color);
            border-radius: 3px;
        }
        
        .category-name-editable {
            position: relative;
        }
        
        .category-name-editable:hover::after {
            content: '\f044';
            font-family: 'Font Awesome 5 Free';
            font-weight: 400;
            position: absolute;
            right: -20px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.8rem;
            color: var(--primary-color);
            opacity: 0;
        }
        
        .category-name-editable:hover::after {
            opacity: 0.7;
        }
        
        .category-edit-input {
            margin: 0;
            padding: 0.25rem 0.5rem;
            font-weight: 600;
            box-shadow: 0 0 0 2px var(--primary-color);
            border-radius: 4px;
            font-size: 1rem;
            width: auto;
            display: inline-block;
        }
        
        /* 分类操作按钮区域 */
        .category-actions {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            display: flex;
            gap: 5px;
            padding: 5px; /* 增加可点击区域 */
            z-index: 10;
        }
        
        /* 分类操作按钮的通用样式 */
        .category-actions .btn,
        .add-website-btn {
            border-radius: 50%;
            width: 30px;
            height: 30px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            opacity: 0; /* 完全隐藏 */
        }
        
        .category-actions .btn:hover,
        .add-website-btn:hover {
            transform: scale(1.1);
            opacity: 1; /* 悬停时完全显示 */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
        
        /* 移动分类的按钮 */
        .category-move-controls {
            display: inline-flex;
            margin-left: 15px;
            opacity: 0;
            transition: opacity 0.2s ease;
            position: relative;
            z-index: 5;
        }
        
        .category-move-controls:hover {
            opacity: 1;
        }
        
        .category-move-btn {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 1px solid rgba(0, 0, 0, 0.2);
            background-color: rgba(255, 255, 255, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            margin: 0 2px;
            transition: all 0.2s ease;
            color: var(--primary-color);
        }
        
        .category-move-btn:hover {
            background-color: var(--primary-color);
            color: white;
            transform: scale(1.1);
        }
        
        .category-move-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
        }
        
        .category-move-btn:disabled:hover {
            background-color: rgba(255, 255, 255, 0.7);
            color: var(--primary-color);
            transform: none;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: var(--hover-color);
            border-color: var(--hover-color);
        }
        
        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .return-btn {
            margin-right: 10px;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .website-card .card-body {
                padding: 0.2rem;
            }
            
            .website-card .card-title {
                font-size: 0.85rem;
            }
            
            .header {
                padding: 0.75rem 0;
            }
            
            .header h1 {
                font-size: 1.3rem;
            }
        }
        
        /* 更紧凑的网站布局 */
        .websites-container .row {
            margin-right: -3px;
            margin-left: -3px;
        }
        
        .websites-container .col-md-4,
        .websites-container .col-sm-6,
        .websites-container .col-lg-2,
        .websites-container .col-md-2,
        .websites-container .col-4,
        .websites-container .col-sm-3,
        .websites-container .col-xl-1 {
            padding-right: 3px;
            padding-left: 3px;
            margin-bottom: 6px;
        }
        
        /* 无内容时的提示 */
        .empty-state {
            text-align: center;
            padding: 2rem 1rem;
            background: white;
            border-radius: 8px;
            margin: 1.5rem 0;
            box-shadow: 0 2px 4px var(--card-shadow);
        }
        
        .empty-state h4 {
            margin-bottom: 1rem;
            color: #333;
        }
        
        .empty-state p {
            color: #666;
            margin-bottom: 1.5rem;
        }
        
        /* 紧凑的模态框 */
        .modal-body {
            padding: 1rem;
        }
        
        .modal-header {
            padding: 0.75rem 1rem;
        }
        
        .modal-footer {
            padding: 0.5rem 1rem;
        }
        
        /* 常用网站分类的特殊样式 */
        .frequently-used-category {
            background: linear-gradient(135deg, #f0f7ff, #e1eefe);
            border-left: 4px solid #ff9800;
        }
        
        .frequently-used-category .category-title h4 {
            color: #333;
            font-weight: 700;
        }
        
        .frequently-used-category .category-title h4::before {
            background-color: #ff9800;
        }
        
        .system-category {
            cursor: default;
        }
        
        /* 搜索框样式 */
        .search-container {
            width: 300px;
            margin: 0 10px;
        }
        
        #website-search {
            border-radius: 20px 0 0 20px;
            border: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        #search-button {
            border-radius: 0 20px 20px 0;
            background-color: white;
            color: var(--primary-color);
            border: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        #search-button:hover {
            background-color: var(--primary-color);
            color: white;
        }
        
        @media (max-width: 768px) {
            .d-flex.justify-content-between.align-items-center {
                flex-direction: column;
                align-items: stretch !important;
                gap: 10px;
            }
            
            .search-container {
                width: 100%;
                margin: 10px 0;
                order: 3;
            }
            
            h1 {
                order: 1;
            }
            
            .return-btn {
                order: 2;
                align-self: flex-end;
            }
        }
        
        /* 拖动相关样式 */
        .sortable-ghost {
            opacity: 0.4;
            background-color: #f8f9fa;
            border: 2px dashed var(--primary-color) !important;
        }
        
        .sortable-chosen {
            z-index: 1000;
        }
        
        .sortable-drag {
            opacity: 0.8;
        }
        
        .website-card.drag-mode {
            cursor: move;
            border: 1px dashed var(--primary-color);
            transform: none;
        }
        
        .website-card .drag-handle {
            display: none;
            cursor: move;
        }
        
        .website-card.drag-mode .drag-handle {
            display: block;
        }
        
        /* 密码验证层样式 */
        #password-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .password-container {
            background-color: white;
            padding: 2rem;
            border-radius: 8px;
            width: 90%;
            max-width: 400px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .password-container h3 {
            margin-bottom: 1.5rem;
            text-align: center;
        }
        
        .password-container .form-group {
            margin-bottom: 1rem;
        }
        
        .password-error {
            color: #dc3545;
            margin-top: 0.5rem;
            text-align: center;
            display: none;
        }
        
        /* 添加震动效果 */
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
        
        .shake {
            animation: shake 0.5s;
        }
    </style>
</head>
<body>
    <!-- 密码验证层 -->
    <div id="password-overlay" style="display: none">
        <div class="password-container">
            <h3>ST计划访问验证</h3>
            <div class="form-group">
                <input type="password" id="access-password" class="form-control" placeholder="请输入访问密码">
            </div>
            <button id="password-submit" class="btn btn-primary w-100">确认</button>
            <div id="password-error" class="password-error">密码错误，请重试</div>
            <div class="form-check mt-3">
                <input class="form-check-input" type="checkbox" id="remember-password">
                <label class="form-check-label" for="remember-password">
                    在此设备上记住我
                </label>
            </div>
        </div>
    </div>

    <!-- 页面标题栏 -->
    <div class="header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h1>常用网站导航</h1>
                <div class="search-container">
                    <div class="input-group">
                        <input type="text" id="website-search" class="form-control" placeholder="搜索网站..." aria-label="搜索网站">
                        <button class="btn btn-primary" type="button" id="search-button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    <a href="index.html" class="btn btn-light return-btn">
                        <i class="fas fa-arrow-left"></i> 返回主界面
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <!-- 操作按钮区 -->
        <div class="action-bar">
            <div class="d-flex justify-content-between align-items-center px-3">
                <button class="btn btn-primary" onclick="addNewCategory()">
                    <i class="fas fa-plus"></i> 添加新分类
                </button>
            </div>
        </div>
        
        <!-- 分类容器 -->
        <div id="categories-container">
            <!-- 分类将在这里动态添加 -->
        </div>
        
        <!-- 无内容时显示的提示 -->
        <div id="empty-state" class="empty-state" style="display: none;">
            <i class="fas fa-folder-open"></i>
            <h4>还没有添加任何网站分类</h4>
            <p>点击"添加新分类"按钮创建您的第一个网站分类</p>
            <button class="btn btn-primary" onclick="addNewCategory()">
                <i class="fas fa-plus"></i> 添加新分类
            </button>
        </div>
    </div>

    <!-- 添加分类的模态框 -->
    <div class="modal fade" id="categoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加新分类</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="categoryForm">
                        <div class="mb-3">
                            <label class="form-label">分类名称</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-tag"></i></span>
                                <input type="text" class="form-control" id="categoryName" required placeholder="例如：学习资源、工作工具等">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveCategory()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加网站的模态框 -->
    <div class="modal fade" id="websiteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加新网站</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="websiteForm">
                        <div class="mb-3">
                            <label class="form-label">网站名称</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-globe"></i></span>
                                <input type="text" class="form-control" id="websiteName" required placeholder="例如：哔哩哔哩、知乎等">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">网站地址</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-link"></i></span>
                                <input type="url" class="form-control" id="websiteUrl" required placeholder="以 http:// 或 https:// 开头">
                            </div>
                            <div class="form-text">请输入完整的网址，包括 http:// 或 https://</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveWebsite()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑网站的模态框 -->
    <div class="modal fade" id="editWebsiteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑网站</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editWebsiteForm">
                        <div class="mb-3">
                            <label class="form-label">网站名称</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-globe"></i></span>
                                <input type="text" class="form-control" id="editWebsiteName" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">网站地址</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-link"></i></span>
                                <input type="url" class="form-control" id="editWebsiteUrl" required>
                            </div>
                            <div class="form-text">请输入完整的网址，包括 http:// 或 https://</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">所属分类</label>
                            <select class="form-select" id="editWebsiteCategory" required>
                                <!-- 分类选项将在打开模态框时动态添加 -->
                            </select>
                        </div>
                        <input type="hidden" id="editWebsiteId">
                        <input type="hidden" id="editWebsiteOriginalCategory">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="updateWebsite()">保存修改</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.min.js"></script>
    <script>
        // 存储网站数据的结构
        let websites = JSON.parse(localStorage.getItem('websites')) || {
            categories: []
        };

        // 存储网站点击统计数据
        let websiteStats = JSON.parse(localStorage.getItem('websiteStats')) || {
            monthStart: getMonthStart().toDateString(),
            clicks: {}
        };

        // 当前正在编辑的分类ID
        let currentCategoryId = null;

        // 常用网站分类ID，用于标识特殊的常用网站分类
        const FREQUENTLY_USED_ID = "frequently_used";

        // 初始化页面
        function initPage() {
            // 密码验证功能
            const passwordOverlay = document.getElementById('password-overlay');
            const passwordInput = document.getElementById('access-password');
            const passwordSubmit = document.getElementById('password-submit');
            const passwordError = document.getElementById('password-error');
            const rememberPassword = document.getElementById('remember-password');
            
            // 默认密码
            const correctPassword = 'st6666';
            
            // 检查是否已经记住密码，只有未验证过时才显示密码验证层
            if (localStorage.getItem('st_authenticated') !== 'true') {
                passwordOverlay.style.display = 'flex';
                
                // 密码提交按钮事件
                passwordSubmit.addEventListener('click', function() {
                    verifyPassword();
                });
                
                // 按回车键也可以提交密码
                passwordInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        verifyPassword();
                    }
                });
            }
            
            // 验证密码函数
            function verifyPassword() {
                const inputPassword = passwordInput.value;
                
                if(inputPassword === correctPassword) {
                    // 密码正确
                    passwordOverlay.style.display = 'none';
                    
                    // 如果选择了记住密码，将验证状态保存到localStorage
                    if(rememberPassword.checked) {
                        localStorage.setItem('st_authenticated', 'true');
                    }
                } else {
                    // 密码错误
                    passwordError.style.display = 'block';
                    passwordInput.value = '';
                    passwordInput.focus();
                    
                    // 震动效果
                    passwordOverlay.classList.add('shake');
                    setTimeout(() => {
                        passwordOverlay.classList.remove('shake');
                    }, 500);
                }
            }

            // 检查是否需要重置每月统计
            checkAndResetMonthlyStats();
            
            // 确保常用网站分类存在
            ensureFrequentlyUsedCategory();
            
            // 更新常用网站列表
            updateFrequentlyUsedWebsites();
            
            // 渲染所有分类
            renderCategories();
            checkEmptyState();
            
            // 初始化搜索功能
            initSearchFunction();
        }

        // 获取本月起始日期（1号）
        function getMonthStart() {
            const now = new Date();
            const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
            monthStart.setHours(0, 0, 0, 0);
            return monthStart;
        }

        // 检查是否需要重置每月点击统计
        function checkAndResetMonthlyStats() {
            const currentMonthStart = getMonthStart().toDateString();
            if (websiteStats.monthStart !== currentMonthStart) {
                websiteStats = {
                    monthStart: currentMonthStart,
                    clicks: {}
                };
                localStorage.setItem('websiteStats', JSON.stringify(websiteStats));
            }
        }

        // 确保常用网站分类存在
        function ensureFrequentlyUsedCategory() {
            // 检查常用网站分类是否存在
            const frequentlyUsedExists = websites.categories.some(c => c.id === FREQUENTLY_USED_ID);
            
            // 如果不存在，创建该分类
            if (!frequentlyUsedExists) {
                websites.categories.unshift({
                    id: FREQUENTLY_USED_ID,
                    name: "常用网站",
                    websites: [],
                    isSystem: true // 标记为系统分类，不允许删除
                });
                localStorage.setItem('websites', JSON.stringify(websites));
            } else {
                // 如果存在但不在第一位，将其移到第一位
                const index = websites.categories.findIndex(c => c.id === FREQUENTLY_USED_ID);
                if (index > 0) {
                    const category = websites.categories.splice(index, 1)[0];
                    websites.categories.unshift(category);
                    localStorage.setItem('websites', JSON.stringify(websites));
                }
            }
        }

        // 更新常用网站列表，将点击最多的网站添加到常用分类
        function updateFrequentlyUsedWebsites() {
            // 查找常用网站分类
            const frequentlyUsedCategory = websites.categories.find(c => c.id === FREQUENTLY_USED_ID);
            if (!frequentlyUsedCategory) return;
            
            // 清空现有常用网站
            frequentlyUsedCategory.websites = [];
            
            // 创建网站点击次数的列表
            const clickEntries = Object.entries(websiteStats.clicks)
                .map(([websiteId, clicks]) => ({ websiteId, clicks }))
                .sort((a, b) => b.clicks - a.clicks)
                .slice(0, 10); // 取点击次数最多的10个
            
            // 查找这些网站并添加到常用分类
            if (clickEntries.length > 0) {
                for (const entry of clickEntries) {
                    // 在所有分类中查找该网站
                    let foundWebsite = null;
                    
                    for (const category of websites.categories) {
                        if (category.id === FREQUENTLY_USED_ID) continue; // 跳过常用分类自身
                        
                        const website = category.websites.find(w => w.id === entry.websiteId);
                        if (website) {
                            foundWebsite = { ...website }; // 创建副本
                            break;
                        }
                    }
                    
                    // 如果找到网站，添加到常用分类
                    if (foundWebsite) {
                        frequentlyUsedCategory.websites.push(foundWebsite);
                    }
                }
            }
            
            // 保存更改
            localStorage.setItem('websites', JSON.stringify(websites));
        }

        // 记录网站点击
        function recordWebsiteClick(websiteId) {
            if (!websiteId) return;

            // 确保当月的统计数据已初始化
            checkAndResetMonthlyStats();

            // 更新点击次数
            if (!websiteStats.clicks[websiteId]) {
                websiteStats.clicks[websiteId] = 0;
            }

            websiteStats.clicks[websiteId]++;

            // 保存统计数据
            localStorage.setItem('websiteStats', JSON.stringify(websiteStats));

            // 更新常用网站列表
            updateFrequentlyUsedWebsites();

            // 实时更新访问次数显示
            updateVisitCountDisplay(websiteId);
        }

        // 更新访问次数显示
        function updateVisitCountDisplay(websiteId) {
            const visitCount = websiteStats.clicks[websiteId] || 0;

            // 查找所有该网站的卡片（可能在多个分类中）
            document.querySelectorAll(`[data-website-id="${websiteId}"] .website-visit-count`).forEach(countElement => {
                countElement.innerHTML = `<i class="fas fa-eye"></i> ${visitCount}`;
            });
        }

        // 检查是否显示空状态提示
        function checkEmptyState() {
            const emptyState = document.getElementById('empty-state');
            // 不考虑系统分类（常用网站）
            const hasUserCategories = websites.categories.some(c => c.id !== FREQUENTLY_USED_ID);
            
            if (!hasUserCategories) {
                emptyState.style.display = 'block';
            } else {
                emptyState.style.display = 'none';
            }
        }

        // 渲染所有分类
        function renderCategories() {
            const container = document.getElementById('categories-container');
            container.innerHTML = '';
            
            websites.categories.forEach((category, index) => {
                const categorySection = document.createElement('div');
                categorySection.className = 'category-section';
                
                // 如果是常用网站分类，添加特殊样式
                if (category.id === FREQUENTLY_USED_ID) {
                    categorySection.classList.add('frequently-used-category');
                }
                
                // 确定上下移动按钮的禁用状态
                // 常用网站分类不可移动，始终固定在第一位
                const isSystem = category.id === FREQUENTLY_USED_ID;
                const isFirst = index === 0;
                const isLast = index === websites.categories.length - 1;
                
                // 构建分类HTML
                categorySection.innerHTML = `
                    <div class="category-title">
                        <h4 data-category-id="${category.id}" class="category-name-editable ${isSystem ? 'system-category' : ''}">${category.name}</h4>
                        ${!isSystem ? `
                        <div class="category-move-controls">
                            <button class="category-move-btn move-up" ${isFirst ? 'disabled' : ''} onclick="moveCategory('${category.id}', 'up')">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="category-move-btn move-down" ${isLast ? 'disabled' : ''} onclick="moveCategory('${category.id}', 'down')">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                        ` : ''}
                    </div>
                    ${!isSystem ? `
                    <div class="category-actions">
                        <button class="btn btn-sm btn-outline-primary add-website-btn" onclick="addNewWebsite('${category.id}')">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteCategory('${category.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    ` : ''}
                    <div class="row g-2 websites-container" id="websites-${category.id}">
                        ${renderWebsites(category.websites, isSystem)}
                    </div>
                    ${category.websites.length === 0 ? `<div class="text-center text-muted py-3"><small>${isSystem ? '本月还没有常用网站' : '该分类下还没有网站'}</small></div>` : ''}
                `;
                container.appendChild(categorySection);
            });
            
            checkEmptyState();
            
            // 绑定双击编辑事件
            bindCategoryTitleEditEvents();
        }

        // 渲染网站卡片
        function renderWebsites(websites, isFrequentlyUsed = false) {
            if (websites.length === 0) return '';

            return websites.map(website => {
                // 获取访问次数
                const visitCount = websiteStats.clicks[website.id] || 0;

                // 构建网站卡片HTML
                return `
                    <div class="col-4 col-sm-3 col-md-2 col-xl-1 mb-2">
                        <div class="website-card" id="card-${website.id}" data-website-id="${website.id}" data-category-id="${isFrequentlyUsed ? FREQUENTLY_USED_ID : ''}">
                            ${!isFrequentlyUsed ? `
                            <button class="website-delete-btn" onclick="deleteWebsite('${website.id}', event)">
                                <i class="fas fa-trash"></i>
                            </button>
                            ` : ''}
                            <div class="website-visit-count" title="本月访问次数">
                                <i class="fas fa-eye"></i> ${visitCount}
                            </div>
                            <div class="website-card-clickable" onclick="openWebsite('${website.url}', '${website.id}')"></div>
                            <div class="website-card-info">
                                <div>${website.name}</div>
                                <div class="url">${website.url}</div>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-light" onclick="openWebsite('${website.url}', '${website.id}')">
                                        <i class="fas fa-external-link-alt"></i> 访问
                                    </button>
                                    ${!isFrequentlyUsed ? `
                                    <button class="btn btn-danger" onclick="deleteWebsite('${website.id}', event)">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                    ` : ''}
                                </div>
                            </div>
                            <div class="card-body">
                                <h5 class="card-title">${website.name}</h5>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }
        
        // 打开网站并记录点击
        function openWebsite(url, websiteId) {
            // 记录点击
            recordWebsiteClick(websiteId);
            
            // 打开网站
            window.open(url, '_blank');
        }

        // 监听长按事件以显示删除按钮或激活拖动功能
        document.addEventListener('DOMContentLoaded', function() {
            // 设置长按计时器ID
            let pressTimer;
            let longPressActive = false;
            let dragMode = false;
            
            // 初始化每个分类中网站的拖拽排序
            function initSortable() {
                document.querySelectorAll('.websites-container').forEach(container => {
                    if (container.dataset.sortableBound) return; // 避免重复初始化
                    
                    const categoryId = container.id.replace('websites-', '');
                    if (categoryId === FREQUENTLY_USED_ID) return; // 跳过常用网站分类
                    
                    new Sortable(container, {
                        animation: 150,
                        handle: '.drag-handle',
                        ghostClass: 'sortable-ghost',
                        chosenClass: 'sortable-chosen',
                        dragClass: 'sortable-drag',
                        disabled: true, // 初始禁用，只有长按时才启用
                        onEnd: function(evt) {
                            // 保存排序后的顺序
                            saveWebsiteOrder(categoryId, container);
                            // 不再自动退出拖动模式，允许连续拖动
                            // dragMode = false;
                            // hideDragHandles();
                            // disableSortable();
                        }
                    });
                    
                    container.dataset.sortableBound = 'true';
                });
            }
            
            // 保存网站排序
            function saveWebsiteOrder(categoryId, container) {
                const category = websites.categories.find(c => c.id === categoryId);
                if (!category) return;
                
                // 获取排序后的网站ID顺序
                const newOrder = [];
                container.querySelectorAll('.website-card').forEach(card => {
                    newOrder.push(card.dataset.websiteId);
                });
                
                // 按新顺序重排分类中的网站
                const reorderedWebsites = [];
                newOrder.forEach(websiteId => {
                    const website = category.websites.find(site => site.id === websiteId);
                    if (website) {
                        reorderedWebsites.push(website);
                    }
                });
                
                // 更新分类中的网站
                category.websites = reorderedWebsites;
                
                // 保存到本地存储
                localStorage.setItem('websites', JSON.stringify(websites));
            }
            
            // 当网站卡片被添加到DOM后，为它们添加事件监听
            const bindLongPressEvents = () => {
                document.querySelectorAll('.website-card').forEach(card => {
                    // 防止重复绑定
                    if (card.dataset.eventsBound) return;
                    
                    // 添加拖动手柄
                    if (!card.querySelector('.drag-handle')) {
                        const dragHandle = document.createElement('div');
                        dragHandle.className = 'drag-handle';
                        dragHandle.innerHTML = '<i class="fas fa-grip-vertical"></i>';
                        dragHandle.style.position = 'absolute';
                        dragHandle.style.top = '5px';
                        dragHandle.style.left = '5px';
                        dragHandle.style.padding = '5px';
                        dragHandle.style.cursor = 'move';
                        dragHandle.style.display = 'none'; // 默认隐藏
                        dragHandle.style.zIndex = '10';
                        dragHandle.style.color = '#007bff';
                        card.appendChild(dragHandle);
                    }
                    
                    // 开始长按
                    card.addEventListener('mousedown', function(e) {
                        // 只处理左键点击
                        if (e.button !== 0) return;
                        
                        // 开始长按计时
                        pressTimer = setTimeout(() => {
                            longPressActive = true;
                            
                            // 显示拖动手柄而不是删除按钮
                            if (e.ctrlKey) {
                                // Ctrl+长按进入拖动模式
                                dragMode = true;
                                card.classList.add('drag-mode');
                                showDragHandles();
                                enableSortable();
                            } else {
                                // 普通长按显示删除按钮
                                card.classList.add('show-delete');
                            }
                        }, 800); // 800毫秒的长按时间
                    });
                    
                    // 如果鼠标移开或释放，取消长按
                    card.addEventListener('mouseup', function() {
                        clearTimeout(pressTimer);
                        longPressActive = false;
                    });
                    
                    card.addEventListener('mouseleave', function() {
                        clearTimeout(pressTimer);
                        longPressActive = false;
                    });
                    
                    // 添加右键菜单事件
                    card.addEventListener('contextmenu', function(e) {
                        e.preventDefault(); // 阻止默认右键菜单
                        
                        const websiteId = this.dataset.websiteId;
                        if (websiteId) {
                            openEditWebsiteModal(websiteId);
                        }
                    });
                    
                    // 标记已绑定事件
                    card.dataset.eventsBound = 'true';
                });
            };
            
            // 显示所有拖动手柄
            function showDragHandles() {
                document.querySelectorAll('.website-card .drag-handle').forEach(handle => {
                    handle.style.display = 'block';
                });
            }
            
            // 隐藏所有拖动手柄
            function hideDragHandles() {
                document.querySelectorAll('.website-card .drag-handle').forEach(handle => {
                    handle.style.display = 'none';
                });
                document.querySelectorAll('.website-card.drag-mode').forEach(card => {
                    card.classList.remove('drag-mode');
                });
            }
            
            // 启用拖动排序
            function enableSortable() {
                document.querySelectorAll('.websites-container').forEach(container => {
                    const sortable = Sortable.get(container);
                    if (sortable) {
                        sortable.option("disabled", false);
                    }
                });
            }
            
            // 禁用拖动排序
            function disableSortable() {
                document.querySelectorAll('.websites-container').forEach(container => {
                    const sortable = Sortable.get(container);
                    if (sortable) {
                        sortable.option("disabled", true);
                    }
                });
            }
            
            // 点击页面其他地方隐藏删除按钮、信息面板和结束拖动模式
            document.addEventListener('click', function(e) {
                // 检查点击的是否为删除按钮或其子元素
                const isDeleteButton = e.target.closest('.website-delete-btn') || e.target.closest('.btn-danger[onclick*="deleteWebsite"]');
                
                // 如果点击的是删除按钮，不隐藏删除按钮和信息面板
                if (isDeleteButton) {
                    return;
                }
                
                // 如果点击的不是网站卡片内部元素
                if (!e.target.closest('.website-card')) {
                    document.querySelectorAll('.website-card.show-delete').forEach(card => {
                        card.classList.remove('show-delete');
                    });
                    
                    document.querySelectorAll('.website-card.show-info').forEach(card => {
                        card.classList.remove('show-info');
                    });
                }
            });
            
            // 按Escape键退出拖动模式
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && dragMode) {
                    dragMode = false;
                    hideDragHandles();
                    disableSortable();
                }
            });
            
            // 初始绑定事件
            bindLongPressEvents();
            
            // 初始化排序功能
            initSortable();
            
            // 使用MutationObserver监听DOM变化，以便给新添加的卡片绑定事件
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length) {
                        bindLongPressEvents();
                        initSortable();
                    }
                });
            });
            
            // 监听分类容器的变化
            observer.observe(document.getElementById('categories-container'), {
                childList: true,
                subtree: true
            });
        });

        // 添加新分类
        function addNewCategory() {
            currentCategoryId = null;
            document.getElementById('categoryName').value = '';
            new bootstrap.Modal(document.getElementById('categoryModal')).show();
        }

        // 保存分类
        function saveCategory() {
            const name = document.getElementById('categoryName').value.trim();
            
            if (!name) return;

            if (currentCategoryId) {
                // 更新现有分类
                const category = websites.categories.find(c => c.id === currentCategoryId);
                if (category) {
                    category.name = name;
                }
            } else {
                // 添加新分类
                websites.categories.push({
                    id: Date.now().toString(),
                    name: name,
                    websites: []
                });
            }

            localStorage.setItem('websites', JSON.stringify(websites));
            bootstrap.Modal.getInstance(document.getElementById('categoryModal')).hide();
            renderCategories();
        }

        // 添加新网站
        function addNewWebsite(categoryId) {
            currentCategoryId = categoryId;
            document.getElementById('websiteName').value = '';
            document.getElementById('websiteUrl').value = '';
            new bootstrap.Modal(document.getElementById('websiteModal')).show();
        }

        // 保存网站
        function saveWebsite() {
            const name = document.getElementById('websiteName').value.trim();
            const url = document.getElementById('websiteUrl').value.trim();
            
            if (!name || !url) return;

            // 如果URL不包含协议，添加https://
            let finalUrl = url;
            if (!finalUrl.startsWith('http://') && !finalUrl.startsWith('https://')) {
                finalUrl = 'https://' + finalUrl;
            }

            const category = websites.categories.find(c => c.id === currentCategoryId);
            if (category) {
                category.websites.push({
                    id: Date.now().toString(),
                    name: name,
                    url: finalUrl
                });
                localStorage.setItem('websites', JSON.stringify(websites));
                bootstrap.Modal.getInstance(document.getElementById('websiteModal')).hide();
                renderCategories();
            }
        }

        // 删除分类
        function deleteCategory(categoryId) {
            // 不允许删除常用网站分类
            if (categoryId === FREQUENTLY_USED_ID) return;
            
            if (confirm('确定要删除这个分类吗？分类下的所有网站也将被删除。')) {
                websites.categories = websites.categories.filter(c => c.id !== categoryId);
                localStorage.setItem('websites', JSON.stringify(websites));
                renderCategories();
            }
        }

        // 删除网站
        function deleteWebsite(websiteId, event) {
            // 阻止事件冒泡，防止触发卡片的点击事件
            if (event) {
                event.stopPropagation();
                event.preventDefault();
            }
            
            if (confirm('确定要删除这个网站吗？此操作不可撤销。')) {
                try {
                    // 先从所有分类中删除该网站（包括常用网站分类）
                    let found = false;
                    for (const category of websites.categories) {
                        const index = category.websites.findIndex(w => w.id === websiteId);
                        if (index !== -1) {
                            category.websites.splice(index, 1);
                            found = true;
                            // 不要break，继续检查其他分类，确保彻底删除
                        }
                    }
                    
                    if (found) {
                        // 移除常用网站统计中的数据
                        if (websiteStats.clicks[websiteId]) {
                            delete websiteStats.clicks[websiteId];
                        }
                        
                        // 保存更改到localStorage
                        localStorage.setItem('websites', JSON.stringify(websites));
                        localStorage.setItem('websiteStats', JSON.stringify(websiteStats));
                        
                        // 更新常用网站列表
                        updateFrequentlyUsedWebsites();
                        
                        // 立即重新渲染页面
                        renderCategories();
                    }
                } catch (err) {
                    console.error('删除网站时出错:', err);
                    alert('删除网站时出错，请刷新页面后重试');
                }
            }
        }

        // 为分类标题添加双击编辑功能
        function bindCategoryTitleEditEvents() {
            document.querySelectorAll('.category-name-editable').forEach(titleElement => {
                // 跳过系统分类（不可编辑）
                if (titleElement.classList.contains('system-category')) return;
                
                // 防止重复绑定
                if (titleElement.dataset.eventsBound) return;
                
                // 添加双击事件
                titleElement.addEventListener('dblclick', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    const categoryId = this.dataset.categoryId;
                    const currentName = this.textContent;
                    
                    // 创建输入框
                    const inputElement = document.createElement('input');
                    inputElement.type = 'text';
                    inputElement.className = 'form-control form-control-sm category-edit-input';
                    inputElement.value = currentName;
                    inputElement.dataset.originalValue = currentName;
                    inputElement.dataset.categoryId = categoryId;
                    
                    // 替换标题为输入框
                    this.parentNode.replaceChild(inputElement, this);
                    
                    // 聚焦输入框并全选文本
                    inputElement.focus();
                    inputElement.select();
                    
                    // 处理按键事件 (Enter保存，Esc取消)
                    inputElement.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter') {
                            saveEditedCategoryName(this);
                        } else if (e.key === 'Escape') {
                            cancelCategoryEdit(this);
                        }
                    });
                    
                    // 点击其他地方时保存
                    inputElement.addEventListener('blur', function() {
                        saveEditedCategoryName(this);
                    });
                });
                
                // 标记已绑定事件
                titleElement.dataset.eventsBound = 'true';
            });
        }
        
        // 保存编辑后的分类名称
        function saveEditedCategoryName(inputElement) {
            const newName = inputElement.value.trim();
            const categoryId = inputElement.dataset.categoryId;
            const originalValue = inputElement.dataset.originalValue;
            
            // 如果为空，恢复原值
            if (!newName) {
                inputElement.value = originalValue;
            }
            
            // 如果有变化且不为空，更新分类名称
            if (newName && newName !== originalValue) {
                const category = websites.categories.find(c => c.id === categoryId);
                if (category) {
                    category.name = newName;
                    localStorage.setItem('websites', JSON.stringify(websites));
                }
            }
            
            // 创建标题元素
            const titleElement = document.createElement('h4');
            titleElement.className = 'category-name-editable';
            titleElement.dataset.categoryId = categoryId;
            titleElement.textContent = newName || originalValue;
            
            // 替换输入框为标题
            inputElement.parentNode.replaceChild(titleElement, inputElement);
            
            // 重新绑定事件
            bindCategoryTitleEditEvents();
        }
        
        // 取消编辑
        function cancelCategoryEdit(inputElement) {
            const originalValue = inputElement.dataset.originalValue;
            const categoryId = inputElement.dataset.categoryId;
            
            // 创建标题元素
            const titleElement = document.createElement('h4');
            titleElement.className = 'category-name-editable';
            titleElement.dataset.categoryId = categoryId;
            titleElement.textContent = originalValue;
            
            // 替换输入框为标题
            inputElement.parentNode.replaceChild(titleElement, inputElement);
            
            // 重新绑定事件
            bindCategoryTitleEditEvents();
        }

        // 移动分类位置
        function moveCategory(categoryId, direction) {
            const categoryIndex = websites.categories.findIndex(c => c.id === categoryId);
            if (categoryIndex === -1) return;
            
            // 根据方向计算目标位置
            const targetIndex = direction === 'up' ? categoryIndex - 1 : categoryIndex + 1;
            
            // 检查目标位置是否有效
            // 确保不能将分类移动到常用网站分类(索引0)之前
            if (targetIndex <= 0 || targetIndex >= websites.categories.length) return;
            
            // 交换位置
            [websites.categories[categoryIndex], websites.categories[targetIndex]] = 
            [websites.categories[targetIndex], websites.categories[categoryIndex]];
            
            // 保存更改并重新渲染
            localStorage.setItem('websites', JSON.stringify(websites));
            renderCategories();
        }

        // 打开编辑网站模态框
        function openEditWebsiteModal(websiteId) {
            // 查找网站信息
            let websiteInfo = null;
            let categoryId = null;
            
            // 在所有分类中查找该网站
            for (const category of websites.categories) {
                const website = category.websites.find(w => w.id === websiteId);
                if (website) {
                    websiteInfo = { ...website };
                    categoryId = category.id;
                    break;
                }
            }
            
            if (!websiteInfo) {
                alert('未找到网站信息');
                return;
            }
            
            // 填充表单
            document.getElementById('editWebsiteName').value = websiteInfo.name;
            document.getElementById('editWebsiteUrl').value = websiteInfo.url;
            document.getElementById('editWebsiteId').value = websiteInfo.id;
            document.getElementById('editWebsiteOriginalCategory').value = categoryId;
            
            // 填充分类选择器
            const categorySelect = document.getElementById('editWebsiteCategory');
            categorySelect.innerHTML = '';
            
            websites.categories.forEach(category => {
                // 跳过常用网站分类，因为它是自动生成的
                if (category.id === FREQUENTLY_USED_ID) return;
                
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                
                if (category.id === categoryId) {
                    option.selected = true;
                }
                
                categorySelect.appendChild(option);
            });
            
            // 显示模态框
            new bootstrap.Modal(document.getElementById('editWebsiteModal')).show();
        }
        
        // 更新网站信息
        function updateWebsite() {
            const websiteId = document.getElementById('editWebsiteId').value;
            const newName = document.getElementById('editWebsiteName').value.trim();
            const newUrl = document.getElementById('editWebsiteUrl').value.trim();
            const newCategoryId = document.getElementById('editWebsiteCategory').value;
            const originalCategoryId = document.getElementById('editWebsiteOriginalCategory').value;
            
            if (!newName || !newUrl) {
                alert('请填写完整信息');
                return;
            }
            
            // 如果URL不包含协议，添加https://
            let finalUrl = newUrl;
            if (!finalUrl.startsWith('http://') && !finalUrl.startsWith('https://')) {
                finalUrl = 'https://' + finalUrl;
            }
            
            // 查找原分类和网站
            const originalCategory = websites.categories.find(c => c.id === originalCategoryId);
            if (!originalCategory) {
                alert('未找到原分类');
                return;
            }
            
            const websiteIndex = originalCategory.websites.findIndex(w => w.id === websiteId);
            if (websiteIndex === -1) {
                alert('未找到网站');
                return;
            }
            
            // 如果分类没变，直接更新网站信息
            if (originalCategoryId === newCategoryId) {
                originalCategory.websites[websiteIndex].name = newName;
                originalCategory.websites[websiteIndex].url = finalUrl;
            } else {
                // 如果分类变了，从原分类删除，添加到新分类
                const newCategory = websites.categories.find(c => c.id === newCategoryId);
                if (!newCategory) {
                    alert('未找到新分类');
                    return;
                }
                
                // 创建网站对象（保持原ID）
                const updatedWebsite = {
                    id: websiteId,
                    name: newName,
                    url: finalUrl
                };
                
                // 从原分类删除
                originalCategory.websites.splice(websiteIndex, 1);
                
                // 添加到新分类
                newCategory.websites.push(updatedWebsite);
            }
            
            // 保存更改
            localStorage.setItem('websites', JSON.stringify(websites));
            
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('editWebsiteModal')).hide();
            
            // 更新常用网站列表和重新渲染
            updateFrequentlyUsedWebsites();
            renderCategories();
        }

        // 初始化搜索功能
        function initSearchFunction() {
            const searchInput = document.getElementById('website-search');
            const searchButton = document.getElementById('search-button');
            
            function performSearch() {
                const searchTerm = searchInput.value.toLowerCase();
                const websiteCards = document.querySelectorAll('.website-card');
                
                websiteCards.forEach(card => {
                    const title = card.querySelector('.card-title').textContent.toLowerCase();
                    const category = card.closest('.category-section');
                    
                    if (title.includes(searchTerm)) {
                        card.style.display = 'flex';
                        if (category) {
                            category.style.display = 'block';
                        }
                    } else {
                        card.style.display = 'none';
                    }
                });
                
                // 如果没有搜索结果，隐藏空分类
                document.querySelectorAll('.category-section').forEach(category => {
                    const visibleCards = [...category.querySelectorAll('.website-card')].filter(card => card.style.display !== 'none');
                    if (visibleCards.length === 0 && searchTerm !== '') {
                        category.style.display = 'none';
                    } else {
                        category.style.display = 'block';
                    }
                });
                
                // 显示搜索结果数量
                if (searchTerm !== '') {
                    const visibleCards = [...document.querySelectorAll('.website-card')].filter(card => card.style.display !== 'none');
                    const resultCount = document.querySelector('.result-count');
                    if (!resultCount) {
                        const countElement = document.createElement('div');
                        countElement.className = 'alert alert-info result-count mt-3';
                        countElement.textContent = `找到 ${visibleCards.length} 个匹配的网站`;
                        document.querySelector('.container').insertBefore(countElement, document.querySelector('#categories-container'));
                    } else {
                        resultCount.textContent = `找到 ${visibleCards.length} 个匹配的网站`;
                        resultCount.style.display = 'block';
                    }
                } else {
                    const resultCount = document.querySelector('.result-count');
                    if (resultCount) {
                        resultCount.style.display = 'none';
                    }
                }
            }
            
            // 点击搜索按钮时执行搜索
            searchButton.addEventListener('click', performSearch);
            
            // 按回车键时执行搜索
            searchInput.addEventListener('keyup', function(event) {
                if (event.key === 'Enter') {
                    performSearch();
                }
                
                // 如果搜索框为空，显示所有网站
                if (searchInput.value === '') {
                    const websiteCards = document.querySelectorAll('.website-card');
                    websiteCards.forEach(card => {
                        card.style.display = 'flex';
                    });
                    document.querySelectorAll('.category-section').forEach(category => {
                        category.style.display = 'block';
                    });
                    
                    // 隐藏搜索结果计数
                    const resultCount = document.querySelector('.result-count');
                    if (resultCount) {
                        resultCount.style.display = 'none';
                    }
                }
            });
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', initPage);
    </script>
</body>
</html>
