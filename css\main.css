/**
 * Main CSS file - imports all modular CSS components
 */

/* Base styles */
@import 'base.css';

/* Layout styles */
@import 'layout.css';

/* Components */
@import 'components.css';

/* Progress bars */
@import 'progress.css';

/* Savings goals */
@import 'goals.css';

/* Age tracking */
@import 'age-tracking.css';

/* Inspiration management */
@import 'inspiration.css';

/* Todo list and quadrant system */
@import 'todo.css';

/* Priority management */
@import 'priority.css';

/* Time tracking */
@import 'time-tracking.css';

/* Data sync */
@import 'sync.css';

/* Diet record */
@import 'diet-record.css';

/* Money projects */
@import 'money-projects.css';

/* Responsive design (should be last) */
@import 'responsive.css';

/* Mobile optimization removed */

/* 数据分类类别标题样式 */
.card-header .category-title {
    text-align: center;
    font-weight: bold;
    padding: 8px 0;
    margin: 0 auto;
    font-size: 1.1rem;
    color: #0d6efd;
    letter-spacing: 1px;
    background-color: #f8f9fa;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    width: 100%;
}

/* 调整按钮组样式以确保一行显示 */
.btn-group.flex-wrap .btn-outline-primary {
    flex: 1 0 auto;
    min-width: auto;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

/* 添加响应式处理 */
@media (min-width: 992px) {
    .btn-group.flex-wrap .btn-outline-primary {
        font-size: 0.875rem;
        padding: 0.25rem 0.75rem;
    }
    
    .card-header .category-title {
        width: 30% !important;
    }
}

@media (max-width: 991px) {
    .btn-group.flex-wrap {
        flex-wrap: nowrap;
        overflow-x: auto;
        justify-content: flex-start;
    }
    
    .btn-group.flex-wrap .btn {
        flex: 0 0 auto;
        min-width: 80px;
    }
    
    .card-header .category-title {
        width: 50% !important;
    }
}

@media (max-width: 768px) {
    .btn-group.flex-wrap {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 5px;
    }
    
    .btn-group.flex-wrap .btn {
        border-radius: 0.25rem !important;
        margin: 0 !important;
        min-width: 0;
    }
    
    .card-header .category-title {
        width: 70% !important;
    }
}

@media (max-width: 576px) {
    .btn-group.flex-wrap {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .card-header .category-title {
        width: 90% !important;
    }
}

/* 移除顶部空白 */
.container.mt-4 {
    margin-top: 0 !important;
}

/* 修改卡片标题样式 */
.card-header {
    padding: 0.5rem 1rem;
}

.card-header h5.card-title {
    margin: 0;
    font-size: 1rem;
}

/* 移除按钮组中的多余文字 */
.btn-group .btn-outline-primary {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 调整卡片外边距 - 字体优化版 */
.card {
    margin-bottom: 8px !important;
    /* 移除悬停时的阴影变化 */
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* 调整整体容器边距 */
.container > .row > .col-md-9 > .card > .card-body {
    padding: 0.5rem !important;
}

/* 最大化卡片空间 */
.col-md-9 > .card {
    margin: 0 !important;
}

.col-md-9 > .card > .card-body {
    padding: 0.5rem !important;
}

/* 全局禁用装饰性伪元素 - 去除所有透明小框 */
.card::before, 
.card::after,
.card-title::before, 
.card-title::after,
.card-header::before, 
.card-header::after,
.card-body::before, 
.card-body::after,
.goal-container *::before,
.goal-container *::after {
    content: "" !important;
    display: none !important;
}

/* 恢复功能性图标和UI元素 */
.btn i::before,
.fas::before,
.fab::before,
.far::before,
.fa::before,
i.fas::before,
i.fab::before,
i.far::before,
i.fa::before,
.btn .fas::before,
.btn .fab::before,
.btn .far::before,
.btn .fa::before,
.card-header i::before,
.card-body i::before,
.card-title i::before,
.btn-close::before,
.form-check-input::before,
.dropdown-toggle::after,
.custom-control-label::before,
.custom-control-label::after,
.form-check-label::before,
.form-check-label::after,
.nav-link::after,
.page-link::after,
.badge::before,
.alert-icon::before,
input[type="checkbox"]::before,
input[type="radio"]::before {
    display: inline-block !important;
    content: inherit !important;
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands", "Font Awesome 5 Free" !important;
    font-weight: 900 !important;
}

/* 恢复特定视图中的功能性伪元素 */
.timeline-date:before,
.timeline-item:before {
    display: block !important;
    content: inherit !important;
}

/* 强制恢复所有Font Awesome图标 - 最高优先级 */
* .fas::before,
* .fab::before,
* .far::before,
* .fa::before,
* i.fas::before,
* i.fab::before,
* i.far::before,
* i.fa::before {
    display: inline-block !important;
    content: inherit !important;
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands", "Font Awesome 5 Free" !important;
    font-weight: 900 !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

/* 模态框响应式优化 - 简化版 */
.modal {
    overscroll-behavior: contain;
}

.modal-content {
    max-height: 85vh;
    overflow-y: auto;
    margin: 20px auto !important;
}

@media (max-width: 576px) {
    .modal {
        padding: 0 !important;
    }
    
    .modal-content {
        width: 95% !important;
        max-height: 80vh !important;
        margin: 10px auto !important;
    }
    
    /* iOS相关优化已移除 */
} 