/* Base Styles */
:root {
    --primary-color: #4361ee;
    --primary-dark: #3a56d4;
    --primary-light: #7895ff;
    --secondary-color: #f8f9fa;
    --accent-color: #4cc9f0;
    --secondary-accent: #4895ef;
    --danger-color: #e63946;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --text-color: #2d3748;
    --light-text: #718096;
    --white: #fff;
    --border-color: #e2e8f0;
    --border-light: #f0f4f8;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    --shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.09);
    --border-radius: 10px;
    --border-radius-sm: 6px;
    --transition: all 0.3s ease;
    --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    --time-normal: #4cc9f0;
    --time-warning: #f39c12;
    --time-expired: #e63946;
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-accent));
    --gradient-success: linear-gradient(135deg, #2ecc71, #27ae60);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
    color: var(--text-color);
    background-color: #f7fafc;
    line-height: 1.5;
}

.app-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px 15px;
}

/* Header */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

h1 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-color);
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Buttons */
.primary-btn {
    background: var(--gradient-primary);
    color: var(--white);
    border: none;
    padding: 8px 16px;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: var(--transition);
    box-shadow: 0 2px 6px rgba(67, 97, 238, 0.2);
}

.primary-btn:hover {
    box-shadow: 0 4px 10px rgba(67, 97, 238, 0.3);
    transform: translateY(-1px);
}

.secondary-btn {
    background-color: var(--secondary-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    padding: 8px 16px;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 6px;
}

.secondary-btn:hover {
    background-color: var(--white);
    border-color: var(--primary-light);
    color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.danger-btn {
    background-color: var(--danger-color);
    color: var(--white);
    border: none;
    padding: 8px 16px;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: var(--transition);
    box-shadow: 0 2px 6px rgba(230, 57, 70, 0.2);
    display: flex;
    align-items: center;
    gap: 6px;
}

.danger-btn:hover {
    background-color: #d83144;
    box-shadow: 0 4px 10px rgba(230, 57, 70, 0.3);
    transform: translateY(-1px);
}

.action-btn {
    background: none;
    border: none;
    padding: 6px;
    cursor: pointer;
    color: var(--light-text);
    transition: all 0.2s ease;
    border-radius: 50%;
}

.action-btn:hover {
    color: var(--primary-color);
    background-color: rgba(67, 97, 238, 0.1);
}

/* Dashboard */
.dashboard {
    margin-bottom: 20px;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px; /* 减少间距 */
}

.summary-card {
    background: var(--white);
    padding: 10px 12px; /* 减少内边距 */
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    text-align: center;
    transition: var(--transition);
    border: 1px solid transparent;
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
    border-color: var(--border-color);
}

.summary-card h3 {
    margin: 0 0 6px 0; /* 减少边距 */
    font-size: 0.85rem; /* 减小字体 */
    color: var(--light-text);
    font-weight: 500;
}

.summary-card i {
    margin-bottom: 6px; /* 减少边距 */
}

.summary-card p {
    margin: 0;
    font-size: 1.3rem; /* 减小数字字体 */
    font-weight: 700;
    color: var(--primary-color);
}

/* Goals Container */
.goals-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    width: 100%;
    padding: 0;
    margin: 0;
    align-items: start;
}

.goal-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: 15px;
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid var(--border-light);
}

.goal-card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
    border-color: var(--border-color);
}

.goal-header {
    padding: 10px 12px; /* 减少内边距 */
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.goal-info {
    flex: 1;
}

.goal-name {
    font-size: 1.05rem; /* 稍微减小字体 */
    font-weight: 700;
    color: var(--text-color);
    margin: 0 0 6px 0; /* 减少边距 */
    display: flex;
    align-items: center;
    gap: 6px; /* 减少间距 */
}

.goal-details {
    display: flex;
    flex-direction: column;
    gap: 4px; /* 减少间距 */
}

.goal-detail {
    display: flex;
    align-items: center;
    gap: 5px; /* 减少间距 */
    color: var(--light-text);
    font-size: 0.85rem; /* 减小字体 */
}

.goal-detail i {
    color: var(--primary-color);
    font-size: 0.85rem;
}

.goal-actions {
    display: flex;
    gap: 6px; /* 减少按钮间距 */
}

.progress-container {
    padding: 8px 12px; /* 减少内边距 */
}

.progress-text {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px; /* 减少边距 */
    font-size: 0.85rem; /* 减小字体 */
    font-weight: 500;
}

.progress-text .amount {
    color: var(--primary-color);
    font-weight: 600;
}

.progress-text .percentage {
    color: var(--success-color);
    font-weight: 600;
}

.progress-bar-bg {
    height: 6px; /* 减少进度条高度 */
    background: #f1f5f9;
    border-radius: 3px; /* 相应减少圆角 */
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(to right, #ffd700, #ffb700);
    transition: width 0.5s ease;
    border-radius: 3px; /* 相应减少圆角 */
}

.progress-bar.time-progress-bar {
    background: linear-gradient(to right, #ff5e62, #ff2c2c);
}

.progress-bar.time-warning {
    background: linear-gradient(to right, #ff7e5f, #ff4e2c);
    box-shadow: 0 0 6px rgba(255, 126, 95, 0.5);
}

.progress-bar.time-expired {
    background: linear-gradient(to right, #d92222, #9c0707);
    box-shadow: 0 0 6px rgba(217, 34, 34, 0.5);
}

/* Subgoals Styling */
.subgoals-container {
    padding: 0 12px 10px; /* 减少内边距 */
}

.subgoals-container:empty {
    display: none;
}

.subgoals-title {
    display: none;
}

.subgoal-item {
    padding: 8px 10px; /* 减少内边距 */
    margin-bottom: 5px; /* 减少间距 */
    border-radius: var(--border-radius-sm);
    background-color: var(--secondary-color);
    border: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.subgoal-item:last-child {
    margin-bottom: 0;
}

.subgoal-info {
    flex: 1;
}

.subgoal-name {
    font-weight: 600;
    margin-bottom: 4px; /* 减少边距 */
    font-size: 0.9rem; /* 减小字体 */
}

.subgoal-progress {
    display: flex;
    align-items: center;
    gap: 6px; /* 减少间距 */
    width: 100%;
}

.subgoal-progress-bar-bg {
    flex: 1;
    height: 5px; /* 减少高度 */
    background: rgba(255, 255, 255, 0.5);
    border-radius: 2.5px; /* 相应减少圆角 */
    overflow: hidden;
}

.subgoal-progress-bar {
    height: 100%;
    background: var(--gradient-success);
    transition: width 0.5s ease;
}

.subgoal-progress-text {
    font-size: 0.8rem;
    color: var(--success-color);
    white-space: nowrap;
    font-weight: 600;
}

.subgoal-deadline {
    font-size: 0.8rem;
    color: var(--light-text);
    margin-top: 4px;
}

/* 子目标时间进度样式 */
.subgoal-item .time-progress-container {
    margin-top: 8px;
}

/* 添加子目标按钮的样式 */
.add-subgoal-container {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}

.add-subgoal-btn {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
    border: 1px dashed var(--primary-light);
    padding: 6px 14px;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 6px;
}

.add-subgoal-btn:hover {
    background-color: rgba(67, 97, 238, 0.15);
    transform: translateY(-1px);
}

.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px 0;
    color: var(--light-text);
}

.empty-state i {
    color: var(--border-color);
    margin-bottom: 15px;
}

.empty-state p {
    font-size: 1rem;
    max-width: 300px;
    margin: 0 auto;
    line-height: 1.5;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(5px);
    align-items: center;
    justify-content: center;
}

/* 当模态框显示时应用这些样式 */
.modal[style*="display: block"] {
    display: flex !important;
}

.modal-content {
    background-color: var(--white);
    max-width: 500px;
    width: 90%;
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    position: relative;
    max-height: 85vh;
    overflow-y: auto;
    animation: modalFadeIn 0.3s ease;
    margin: 0 auto; /* 确保水平居中 */
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 22px;
    color: var(--light-text);
    cursor: pointer;
    transition: var(--transition);
}

.close-btn:hover {
    color: var(--danger-color);
    transform: rotate(90deg);
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.95rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
}

.hidden {
    display: none !important;
}

.show {
    display: flex !important;
}

/* Responsive Design */
@media (max-width: 992px) {
    .goals-container {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .summary-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px; /* 移动端进一步减少间距 */
    }

    .summary-card {
        padding: 8px 10px; /* 移动端减少内边距 */
    }

    .summary-card h3 {
        font-size: 0.8rem; /* 移动端减小字体 */
        margin: 0 0 4px 0;
    }

    .summary-card p {
        font-size: 1.2rem; /* 移动端减小数字字体 */
    }

    .header-actions {
        flex-direction: column;
        gap: 6px; /* 减少间距 */
    }

    .header-actions button {
        width: 100%;
        padding: 8px 12px; /* 减少按钮内边距 */
        font-size: 0.9rem; /* 减小按钮字体 */
    }

    .goal-header {
        padding: 8px 10px; /* 移动端减少内边距 */
    }

    .progress-container {
        padding: 6px 10px; /* 移动端减少内边距 */
    }
}

@media (max-width: 480px) {
    .summary-cards {
        grid-template-columns: 1fr;
        gap: 5px; /* 小屏幕进一步减少间距 */
    }

    .summary-card {
        padding: 6px 8px; /* 小屏幕减少内边距 */
    }

    .goal-header {
        flex-direction: column;
        gap: 8px; /* 减少间距 */
        padding: 6px 8px; /* 小屏幕减少内边距 */
    }

    .goal-actions {
        width: 100%;
        justify-content: flex-end;
        gap: 4px; /* 减少按钮间距 */
    }

    .progress-container {
        padding: 5px 8px; /* 小屏幕减少内边距 */
    }

    .subgoals-container {
        padding: 0 8px 8px; /* 小屏幕减少内边距 */
    }

    .subgoal-item {
        padding: 6px 8px; /* 小屏幕减少子目标内边距 */
        margin-bottom: 4px; /* 减少间距 */
    }
}

/* Money Input Modal */
.money-input-content {
    max-width: 360px;
    text-align: center;
    padding: 25px;
    border-radius: var(--border-radius);
    background-color: var(--white);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.money-input-content h2 {
    margin-bottom: 20px;
    color: var(--text-color);
    font-weight: 700;
    font-size: 1.2rem;
}

.money-input-field {
    position: relative;
    margin-bottom: 20px;
}

.money-input-field input {
    width: 100%;
    padding: 12px 36px 12px 15px;
    font-size: 18px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    text-align: center;
    color: var(--primary-color);
    font-weight: 700;
}

.money-input-field input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

.money-input-field::before {
    content: "¥";
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 18px;
    color: var(--light-text);
    font-weight: 700;
}

.money-input-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
}

.money-input-actions button {
    min-width: 100px;
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* Tabs and Additional UI Components */
.tabs {
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.tab-btn {
    padding: 10px 16px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: var(--light-text);
    border-bottom: 2px solid transparent;
    transition: var(--transition);
}

.tab-btn:hover,
.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Modal Confirm Styles */
.confirm-content {
    max-width: 360px;
    text-align: center;
}

/* 紧凑优化：额外的间距调整 */
.goal-card {
    margin-bottom: 12px; /* 减少卡片间距 */
}

.dashboard {
    margin-bottom: 15px; /* 减少仪表盘下边距 */
}

/* 按钮尺寸优化 */
.action-btn {
    padding: 4px 6px; /* 减少按钮内边距 */
    font-size: 0.85rem; /* 减小按钮图标 */
}

/* 时间进度容器优化 */
.time-progress-container {
    margin-top: 6px !important; /* 减少时间进度上边距 */
}

/* 子目标时间进度优化 */
.subgoal-item .time-progress-container {
    margin-top: 5px; /* 减少子目标时间进度边距 */
}

/* 添加子目标按钮优化 */
.add-subgoal-container {
    margin-top: 6px; /* 减少上边距 */
}

.add-subgoal-btn {
    padding: 4px 10px; /* 减少按钮内边距 */
    font-size: 0.8rem; /* 减小字体 */
}

.confirm-content h2 {
    color: var(--danger-color);
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.confirm-content p {
    margin-bottom: 20px;
    color: var(--text-color);
}

.confirm-content .form-actions {
    justify-content: center;
}

/* Drag and Drop Styles */
.draggable-goal {
    position: relative;
    cursor: grab;
    transition: var(--transition);
}

.draggable-goal:hover {
    background-color: rgba(67, 97, 238, 0.03);
}

.draggable-goal::before {
    content: "≡";
    position: absolute;
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: var(--light-text);
    opacity: 0;
    transition: var(--transition);
}

.draggable-goal:hover::before {
    opacity: 1;
    left: 8px;
}

.draggable-goal.dragging {
    opacity: 0.5;
    background-color: rgba(67, 97, 238, 0.05);
    cursor: grabbing;
    box-shadow: var(--shadow-hover);
    z-index: 10;
}

.draggable-goal.dragging::before {
    opacity: 0;
}

/* Loading Indicator */
.loading-spinner {
    display: inline-block;
    width: 18px;
    height: 18px;
    border: 2px solid rgba(67, 97, 238, 0.2);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 3px 6px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 6px;
}

.badge-success {
    background-color: rgba(46, 204, 113, 0.15);
    color: var(--success-color);
}

.badge-warning {
    background-color: rgba(243, 156, 18, 0.15);
    color: var(--warning-color);
}

.badge-danger {
    background-color: rgba(230, 57, 70, 0.15);
    color: var(--danger-color);
}

.badge-primary {
    background-color: rgba(67, 97, 238, 0.15);
    color: var(--primary-color);
}
