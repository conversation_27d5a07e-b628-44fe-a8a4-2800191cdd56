/* 脑中画面子系统的样式定义 */

/* 视图容器通用样式 */
.view-container {
    min-height: 200px;
    transition: opacity 0.2s ease;
    will-change: opacity; /* 优化渲染性能 */
}



/* 阴影脉冲动画 - 移动设备上禁用 */
@keyframes shadowPulse {
    0% { box-shadow: 0 0 0 rgba(13, 110, 253, 0.4); }
    50% { box-shadow: 0 0 10px rgba(13, 110, 253, 0.6); }
    100% { box-shadow: 0 0 0 rgba(13, 110, 253, 0.4); }
}

/* 默认PC浏览器使用动画效果 */
.shadow-pulse {
    animation: shadowPulse 1.5s ease-in-out;
}

/* 时间线视图样式 */
.timeline-view {
    position: relative;
    margin: 20px 0;
    padding-left: 30px;
}

.timeline-view::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 10px;
    width: 4px;
    background: linear-gradient(to bottom, 
        #6c757d 0%, 
        #0d6efd 100%);
    border-radius: 2px;
}

.timeline-item {
    margin-bottom: 25px;
    position: relative;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -34px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #fff;
    border: 3px solid #0d6efd;
    z-index: 1;
}

.timeline-item.past::before {
    border-color: #6c757d;
}

.timeline-item.near-future::before {
    border-color: #0d6efd;
}

.timeline-item.mid-future::before {
    border-color: #20c997;
}

.timeline-item.far-future::before {
    border-color: #0dcaf0;
}

.timeline-card {
    border-radius: 8px;
    box-shadow: 0 1px 5px rgba(0,0,0,0.07);
    overflow: hidden;
    transition: transform 0.2s;
}

/* 移动端优化：减少悬停效果 */
@media (hover: hover) {
    .timeline-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    }
}

.timeline-header {
    padding: 12px 15px;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.timeline-date {
    font-size: 0.85rem;
    color: #6c757d;
}

.timeline-content {
    padding: 15px;
}

.timeline-title {
    margin-bottom: 10px;
    font-weight: 600;
    margin: 0; /* 重置margin，由title-row控制间距 */
    flex: 1; /* 占据剩余空间 */
}

.timeline-description {
    color: #666;
    margin-bottom: 15px;
}

.timeline-footer {
    padding: 10px 15px;
    background-color: rgba(0,0,0,0.02);
    border-top: 1px solid rgba(0,0,0,0.05);
}

.domain-badge, .emotion-badge {
    font-size: 0.75rem;
    padding: 3px 8px;
    border-radius: 12px;
    display: inline-block;
    margin-right: 5px;
}

.domain-badge.finance {
    background-color: #e9f5ff;
    color: #0a58ca;
}

.domain-badge.family {
    background-color: #fff8e1;
    color: #ff8f00;
}

.domain-badge.personal {
    background-color: #f3e5f5;
    color: #9c27b0;
}

.domain-badge.living {
    background-color: #e0f2f1;
    color: #00796b;
}

.domain-badge.career {
    background-color: #e8f5e9;
    color: #388e3c;
}

.domain-badge.other {
    background-color: #f5f5f5;
    color: #616161;
}

.emotion-badge.inspiring {
    background-color: #e3f2fd;
    color: #1565c0;
}

.emotion-badge.yearning {
    background-color: #e8eaf6;
    color: #3949ab;
}

.emotion-badge.reflection {
    background-color: #f3e5f5;
    color: #7b1fa2;
}

.emotion-badge.warning {
    background-color: #ffebee;
    color: #c62828;
}

.motivation-indicator {
    display: flex;
    align-items: center;
    margin-top: 5px;
}

.motivation-label {
    font-size: 0.75rem;
    margin-right: 8px;
    color: #6c757d;
}

.motivation-bar {
    flex-grow: 1;
    height: 6px;
    background-color: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.motivation-fill {
    height: 100%;
    background-color: #0d6efd;
    border-radius: 3px;
}

.tag-pills {
    margin-top: 10px;
}

.tag-pill {
    display: inline-block;
    padding: 2px 8px;
    margin: 0 4px 4px 0;
    border-radius: 12px;
    font-size: 0.75rem;
    background-color: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
}

/* 卡片墙视图样式 */
.grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    padding: 15px;
}

.scene-card {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.07);
    transition: transform 0.3s, box-shadow 0.3s;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.scene-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.scene-card-header {
    padding: 12px 15px;
    background-color: rgba(0,0,0,0.02);
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.scene-card-title {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.4;
    flex: 1; /* 占据剩余空间 */
}

.scene-card-content {
    padding: 15px;
    flex-grow: 1;
}

.scene-card-description {
    font-size: 0.9rem;
    color: #666;
}

.scene-card-footer {
    padding: 10px 15px;
    background-color: rgba(0,0,0,0.02);
    border-top: 1px solid rgba(0,0,0,0.05);
}

/* 响应式调整 - 移动设备优化 */
@media (max-width: 768px) {
    .timeline-view {
        padding-left: 20px;
    }
    
    .timeline-view::before {
        left: 5px;
    }
    
    .timeline-item::before {
        left: -24px;
    }
    
    .grid-view {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px; /* 减小间距 */
        padding: 10px; /* 减小内边距 */
    }
    
    /* 移动设备优化已移除 */
    
    /* 减少滚动到顶部按钮的动画 */
    .scroll-to-top {
        transition: opacity 0.2s;
        transform: none !important;
        opacity: 0.7;
    }
}

/* 模态框样式增强 */
.modal-scene-content {
    padding: 20px;
}

.modal-scene-title {
    font-size: 1.5rem;
    margin-bottom: 20px;
    color: #212529;
    font-weight: 600;
}

.modal-scene-description {
    font-size: 1rem;
    line-height: 1.6;
    color: #495057;
    white-space: pre-line;
    margin-bottom: 20px;
}

.modal-scene-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
}

.modal-scene-meta-item {
    display: flex;
    align-items: center;
}

.modal-scene-meta-label {
    font-weight: 600;
    margin-right: 5px;
    color: #6c757d;
}

.modal-scene-tags {
    margin-top: 20px;
}

.modal-scene-related-goal {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #0d6efd;
}

/* 动画效果 - 在移动设备上禁用 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(5px); }
    to { opacity: 1; transform: translateY(0); }
}

/* PC版浏览器使用淡入动画 */
@media (min-width: 992px) {
    .timeline-item, .scene-card {
        animation: fadeIn 0.4s ease-out;
    }
}

/* 禁用移动设备上的动画 */
@media (max-width: 991px) {
    .timeline-item, .scene-card {
        animation: none;
    }
    
    /* 减少阴影和过渡效果 */
    .timeline-card, .scene-card {
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        transition: none;
    }
}

/* =============== 树状视图样式 =============== */

.tree-view {
    padding: 20px;
    overflow-x: auto;
    overflow-y: auto;
    max-height: 80vh;
}

.tree-node {
    margin: 15px 0;
    position: relative;
}

.tree-node-content {
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.tree-node-content:hover {
    border-color: #0d6efd;
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.15);
}

.tree-node-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.tree-node-info {
    flex: 1;
}

.tree-node-title {
    margin: 0 0 8px 0;
    color: #212529;
    font-size: 1.1rem;
    font-weight: 600;
}

.tree-node-meta {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.tree-node-time {
    font-size: 0.85rem;
    color: #6c757d;
    background: #f8f9fa;
    padding: 2px 8px;
    border-radius: 4px;
}

.tree-node-actions {
    display: flex;
    gap: 5px;
    align-items: center;
}

.tree-node-description {
    color: #495057;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #e9ecef;
}

.tree-children {
    margin-top: 20px;
    position: relative;
    transition: all 0.3s ease;
}

.tree-children.collapsed {
    display: none;
}

.tree-branches {
    display: flex;
    gap: 30px;
    justify-content: center;
    position: relative;
}

.tree-branch {
    flex: 1;
    max-width: 400px;
    position: relative;
}

.tree-branch-left {
    text-align: right;
}

.tree-branch-right {
    text-align: left;
}

/* 连接线样式 */
.tree-branches::before {
    content: '';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 20px;
    background: #6c757d;
}

.tree-branches::after {
    content: '';
    position: absolute;
    top: -20px;
    left: 25%;
    right: 25%;
    height: 2px;
    background: #6c757d;
}

.tree-branch::before {
    content: '';
    position: absolute;
    top: -20px;
    width: 2px;
    height: 20px;
    background: #6c757d;
}

.tree-branch-left::before {
    right: 50%;
}

.tree-branch-right::before {
    left: 50%;
}

.tree-toggle-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.7rem;
}

.tree-toggle-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.tree-toggle-btn i {
    transition: transform 0.2s ease;
}

/* 时间类型徽章样式 */
.time-past {
    background: #6c757d;
    color: white;
}

.time-present {
    background: #198754;
    color: white;
}

.time-future {
    background: #0d6efd;
    color: white;
}

.time-type-badge {
    font-size: 0.75rem;
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 500;
}

/* 操作按钮样式 */
.action-btn {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 0.8rem;
    padding: 4px 6px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: #f8f9fa;
    color: #495057;
}

.edit-btn:hover {
    color: #0d6efd;
}

.delete-btn:hover {
    color: #dc3545;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h5 {
    margin-bottom: 10px;
    color: #495057;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .tree-branches {
        flex-direction: column;
        gap: 20px;
    }

    .tree-branch {
        max-width: none;
        text-align: center;
    }

    .tree-branch::before,
    .tree-branches::before,
    .tree-branches::after {
        display: none;
    }

    .tree-node-content {
        padding: 12px;
    }

    .tree-node-title {
        font-size: 1rem;
    }

    .tree-node-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

/* 工具提示样式 */
.tooltip {
    position: absolute;
    background-color: #212529;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.75rem;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s, transform 0.3s;
    pointer-events: none;
}

.tooltip.visible {
    opacity: 0.9;
    transform: translateY(-5px);
}

/* 无数据状态样式 */
.no-data {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-data i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #dee2e6;
}

.no-data h4 {
    margin-bottom: 10px;
    font-weight: 600;
}

.no-data p {
    font-size: 0.9rem;
}

/* 滚动到顶部按钮 */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #0d6efd;
    color: white;
    text-align: center;
    line-height: 40px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s, transform 0.3s;
    z-index: 100;
    transform: scale(0.8);
}

.scroll-to-top.visible {
    opacity: 1;
    transform: scale(1);
}

.scroll-to-top:hover {
    background-color: #0b5ed7;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 时间类型徽章 */
.time-type-badge {
    font-size: 0.75rem;
    padding: 2px 8px;
    border-radius: 12px;
    display: inline-block;
    margin-right: 5px;
    font-weight: 500;
}

.time-type-badge.time-past {
    background-color: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

.time-type-badge.time-present {
    background-color: #e3f2fd;
    color: #0d6efd;
    border: 1px solid rgba(13, 110, 253, 0.2);
}

.time-type-badge.time-future {
    background-color: #e8f5e9;
    color: #198754;
    border: 1px solid rgba(25, 135, 84, 0.2);
}

/* 记忆时间显示 */
.scene-time {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 4px;
}



/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #dee2e6;
}

.empty-state h5 {
    margin-bottom: 10px;
    color: #495057;
}

.empty-state p {
    margin: 0;
    font-size: 0.9rem;
}

/* 紧凑设计优化 */
.card-body {
    padding: 8px !important;
}

.card-header {
    padding: 8px 12px !important;
    font-size: 0.9rem;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 0.8rem;
}

.form-control-sm {
    padding: 4px 8px;
    font-size: 0.85rem;
}

.mb-3 {
    margin-bottom: 0.75rem !important;
}

/* 时间筛选按钮组 */
.btn-group .btn-check:checked + .btn {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

/* 记忆卡片优化 */
.scene-card {
    margin-bottom: 12px;
    border: 1px solid #e9ecef;
    background: white;
}

.scene-card-header {
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.scene-card-content {
    padding: 8px 12px;
}

.scene-card-footer {
    padding: 6px 12px;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.scene-card-description {
    font-size: 0.85rem;
    line-height: 1.4;
    color: #495057;
}

/* 标题行样式 */
.title-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 8px;
}

.title-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s;
}

.scene-card:hover .title-actions {
    opacity: 1;
}

.action-btn {
    padding: 2px 6px;
    font-size: 0.7rem;
    border: none;
    background: none;
    color: #6c757d;
    border-radius: 3px;
    transition: all 0.2s;
}

.action-btn:hover {
    background-color: #e9ecef;
    color: #495057;
}

.action-btn.edit-btn:hover {
    background-color: #cce5ff;
    color: #0d6efd;
}

.action-btn.delete-btn:hover {
    background-color: #f8d7da;
    color: #dc3545;
}

/* 标题行样式 - 包含标题和操作按钮 */
.title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    margin-bottom: 10px; /* 为时间线描述留出间距 */
}

/* 在卡片头部中的标题行不需要下边距 */
.scene-card-header .title-row {
    margin-bottom: 0;
}

.title-actions {
    display: flex;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.2s ease;
    flex-shrink: 0; /* 防止按钮被压缩 */
}

/* 悬停时显示操作按钮 */
.timeline-card:hover .title-actions,
.scene-card:hover .title-actions,
.timeline-item:hover .title-actions {
    opacity: 1;
}

/* 操作按钮样式 */
.action-btn {
    background: none;
    border: none;
    padding: 4px 6px;
    border-radius: 4px;
    color: #6c757d;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    height: 24px;
}

.action-btn:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: #495057;
}

.action-btn.edit-btn:hover {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
}

.action-btn.delete-btn:hover {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

/* 按钮点击效果 */
.action-btn:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}

/* 移动设备按钮优化已移除 */