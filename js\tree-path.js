// 树状分支图 - JavaScript 功能实现
console.log('tree-path.js loading...');

// 简单测试函数
function addRootNode() {
    console.log('addRootNode called');
    alert('添加根节点功能正在测试中');
}

function fitToScreen() {
    console.log('fitToScreen called');
    alert('适应屏幕功能正在测试中');
}

function togglePathMode() {
    console.log('togglePathMode called');
    alert('路径模式功能正在测试中');
}

function saveTree() {
    console.log('saveTree called');
    alert('保存功能正在测试中');
}

function resetTree() {
    console.log('resetTree called');
    alert('重置功能正在测试中');
}

function searchNodes() {
    console.log('searchNodes called');
}

function filterByDepth() {
    console.log('filterByDepth called');
}

function zoomIn() {
    console.log('zoomIn called');
}

function zoomOut() {
    console.log('zoomOut called');
}

console.log('All functions defined');

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeTree();
    setupEventListeners();
});

// 设置事件监听器
function setupEventListeners() {
    const svg = document.getElementById('treeSvg');

    // 鼠标拖拽平移
    let isMouseDown = false;
    let lastMouseX = 0;
    let lastMouseY = 0;

    svg.addEventListener('mousedown', (e) => {
        if (e.target === svg || e.target.closest('#treeGroup')) {
            isMouseDown = true;
            lastMouseX = e.clientX;
            lastMouseY = e.clientY;
            svg.style.cursor = 'grabbing';
        }
    });

    svg.addEventListener('mousemove', (e) => {
        if (isMouseDown) {
            const deltaX = e.clientX - lastMouseX;
            const deltaY = e.clientY - lastMouseY;

            panX += deltaX;
            panY += deltaY;

            lastMouseX = e.clientX;
            lastMouseY = e.clientY;

            renderTree();
        }
    });

    svg.addEventListener('mouseup', () => {
        isMouseDown = false;
        svg.style.cursor = 'grab';
    });

    svg.addEventListener('mouseleave', () => {
        isMouseDown = false;
        svg.style.cursor = 'grab';
    });

    // 鼠标滚轮缩放
    svg.addEventListener('wheel', (e) => {
        e.preventDefault();

        const rect = svg.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
        const newZoom = Math.max(0.3, Math.min(3, currentZoom * zoomFactor));

        if (newZoom !== currentZoom) {
            // 以鼠标位置为中心缩放
            const zoomRatio = newZoom / currentZoom;
            panX = mouseX - (mouseX - panX) * zoomRatio;
            panY = mouseY - (mouseY - panY) * zoomRatio;

            currentZoom = newZoom;
            renderTree();
        }
    });



// 初始化树状图
function initializeTree() {
    // 初始化为空数据
    treeData = [];
    nodeIdCounter = 1;
    selectedNode = null;
    editingNode = null;
    currentPath = [];
    searchResults = [];
    maxDepthFilter = null;
    currentZoom = 1;
    panX = 0;
    panY = 0;
    isPathMode = false;
    renderTree();
}

// 渲染树状图
function renderTree() {
    const svg = document.getElementById('treeSvg');
    const treeGroup = document.getElementById('treeGroup');
    const emptyState = document.getElementById('emptyState');

    if (treeData.length === 0) {
        emptyState.style.display = 'block';
        treeGroup.innerHTML = '';
        return;
    }

    emptyState.style.display = 'none';

    // 自动布局
    if (treeData.length > 0) {
        calculateLayout(treeData[0]);
    }

    // 清空并重新渲染
    treeGroup.innerHTML = '';

    // 设置变换
    treeGroup.setAttribute('transform', `translate(${panX}, ${panY}) scale(${currentZoom})`);

    // 渲染连线
    if (treeData.length > 0) {
        renderLinks(treeData[0], treeGroup);
    }

    // 渲染节点
    if (treeData.length > 0) {
        renderNodes(treeData[0], treeGroup);
    }

    // 更新缩放显示
    updateZoomDisplay();
}

// 计算自动布局
function calculateLayout(node, depth = 0, parentX = 0, parentY = 0) {
    const levelHeight = 120;
    const nodeWidth = 150;

    if (depth === 0) {
        // 根节点居中
        node.x = 400;
        node.y = 100;
        node.depth = 0;
    }

    if (node.children && node.children.length > 0) {
        const totalWidth = (node.children.length - 1) * nodeWidth;
        const startX = node.x - totalWidth / 2;

        node.children.forEach((child, index) => {
            child.x = startX + index * nodeWidth;
            child.y = node.y + levelHeight;
            child.depth = depth + 1;
            calculateLayout(child, depth + 1, child.x, child.y);
        });
    }
}

// 渲染连线
function renderLinks(node, container) {
    if (node.children && node.children.length > 0) {
        node.children.forEach(child => {
            // 检查深度过滤
            if (maxDepthFilter && child.depth > maxDepthFilter) return;

            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');

            // 创建贝塞尔曲线路径
            const startX = node.x;
            const startY = node.y + 20; // 节点底部
            const endX = child.x;
            const endY = child.y - 20; // 节点顶部

            const controlY = startY + (endY - startY) / 2;

            const pathData = `M ${startX} ${startY} C ${startX} ${controlY} ${endX} ${controlY} ${endX} ${endY}`;

            path.setAttribute('d', pathData);
            path.setAttribute('class', 'tree-link');

            // 路径高亮
            if (isPathMode && isNodeInCurrentPath(node) && isNodeInCurrentPath(child)) {
                path.classList.add('highlighted');
            } else if (isPathMode) {
                path.classList.add('dimmed');
            }

            container.appendChild(path);

            // 递归渲染子连线
            renderLinks(child, container);
        });
    }
}

// 渲染节点
function renderNodes(node, container) {
    // 检查深度过滤
    if (maxDepthFilter && node.depth > maxDepthFilter) return;

    const nodeGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    nodeGroup.setAttribute('class', 'tree-node');
    nodeGroup.setAttribute('transform', `translate(${node.x - 60}, ${node.y - 20})`);
    nodeGroup.setAttribute('data-node-id', node.id);

    // 添加编辑状态类
    if (editingNode && editingNode.id === node.id) {
        nodeGroup.classList.add('editing');
    }

    // 节点背景
    const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    rect.setAttribute('class', 'node-rect');
    rect.setAttribute('width', '120');
    rect.setAttribute('height', '40');
    rect.setAttribute('rx', '20');

    // 根据节点类型和状态设置颜色
    let fillColor = node.isRoot ? '#667eea' : '#28a745';
    if (selectedNode && selectedNode.id === node.id) {
        nodeGroup.classList.add('selected');
    }
    if (searchResults.includes(node.id)) {
        fillColor = '#ffc107';
    }
    if (isPathMode) {
        if (isNodeInCurrentPath(node)) {
            nodeGroup.classList.add('highlighted');
        } else {
            nodeGroup.classList.add('dimmed');
        }
    }

    rect.setAttribute('fill', fillColor);
    rect.setAttribute('stroke', '#ffffff');
    rect.setAttribute('stroke-width', '2');
    nodeGroup.appendChild(rect);

    // 节点文本或输入框
    if (editingNode && editingNode.id === node.id) {
        // 编辑模式：显示输入框
        const foreignObject = document.createElementNS('http://www.w3.org/2000/svg', 'foreignObject');
        foreignObject.setAttribute('x', '5');
        foreignObject.setAttribute('y', '10');
        foreignObject.setAttribute('width', '110');
        foreignObject.setAttribute('height', '20');

        const input = document.createElement('input');
        input.className = 'node-input';
        input.value = node.text;
        input.addEventListener('blur', () => finishEditing(node, input.value));
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                finishEditing(node, input.value);
            } else if (e.key === 'Escape') {
                cancelEditing();
            }
        });

        foreignObject.appendChild(input);
        nodeGroup.appendChild(foreignObject);

        // 自动聚焦
        setTimeout(() => {
            input.focus();
            input.select();
        }, 100);
    } else {
        // 正常模式：显示文本
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('class', 'node-label');
        text.setAttribute('x', '60');
        text.setAttribute('y', '25');
        text.textContent = node.text.length > 12 ? node.text.substring(0, 12) + '...' : node.text;
        nodeGroup.appendChild(text);
    }

    // 深度指示器
    if (node.depth > 0) {
        const depthText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        depthText.setAttribute('class', 'depth-indicator');
        depthText.setAttribute('x', '110');
        depthText.setAttribute('y', '15');
        depthText.textContent = `L${node.depth}`;
        nodeGroup.appendChild(depthText);
    }

    // 控制按钮组（悬停时显示）
    if (!editingNode || editingNode.id !== node.id) {
        const controlsGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        controlsGroup.setAttribute('class', 'node-controls');

        // 添加子节点按钮
        const addBtn = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        addBtn.setAttribute('class', 'node-control-btn add-child-btn');
        addBtn.setAttribute('cx', '130');
        addBtn.setAttribute('cy', '10');
        addBtn.setAttribute('r', '8');
        addBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            addChildNodeInline(node);
        });
        controlsGroup.appendChild(addBtn);

        const addIcon = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        addIcon.setAttribute('x', '130');
        addIcon.setAttribute('y', '14');
        addIcon.setAttribute('text-anchor', 'middle');
        addIcon.setAttribute('fill', 'white');
        addIcon.setAttribute('font-size', '10');
        addIcon.setAttribute('font-weight', 'bold');
        addIcon.textContent = '+';
        addIcon.style.pointerEvents = 'none';
        controlsGroup.appendChild(addIcon);

        // 编辑按钮
        const editBtn = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        editBtn.setAttribute('class', 'node-control-btn edit-btn');
        editBtn.setAttribute('cx', '130');
        editBtn.setAttribute('cy', '25');
        editBtn.setAttribute('r', '8');
        editBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            startEditing(node);
        });
        controlsGroup.appendChild(editBtn);

        const editIcon = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        editIcon.setAttribute('x', '130');
        editIcon.setAttribute('y', '29');
        editIcon.setAttribute('text-anchor', 'middle');
        editIcon.setAttribute('fill', 'white');
        editIcon.setAttribute('font-size', '8');
        editIcon.textContent = '✎';
        editIcon.style.pointerEvents = 'none';
        controlsGroup.appendChild(editIcon);

        // 删除按钮（非根节点）
        if (!node.isRoot) {
            const deleteBtn = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            deleteBtn.setAttribute('class', 'node-control-btn delete-btn');
            deleteBtn.setAttribute('cx', '130');
            deleteBtn.setAttribute('cy', '40');
            deleteBtn.setAttribute('r', '8');
            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                deleteNodeInline(node);
            });
            controlsGroup.appendChild(deleteBtn);

            const deleteIcon = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            deleteIcon.setAttribute('x', '130');
            deleteIcon.setAttribute('y', '44');
            deleteIcon.setAttribute('text-anchor', 'middle');
            deleteIcon.setAttribute('fill', 'white');
            deleteIcon.setAttribute('font-size', '10');
            deleteIcon.textContent = '×';
            deleteIcon.style.pointerEvents = 'none';
            controlsGroup.appendChild(deleteIcon);
        }

        nodeGroup.appendChild(controlsGroup);
    }

    // 添加事件监听
    nodeGroup.addEventListener('click', () => selectNode(node));
    nodeGroup.addEventListener('dblclick', () => startEditing(node));

    container.appendChild(nodeGroup);

    // 递归渲染子节点
    if (node.children && node.children.length > 0) {
        node.children.forEach(child => renderNodes(child, container));
    }
}

// 使节点可拖拽
function makeNodeDraggable(element, node) {
    let isDragging = false;
    let startX, startY;
    
    element.addEventListener('mousedown', (e) => {
        if (e.button === 0) { // 左键
            isDragging = true;
            startX = e.clientX - node.x;
            startY = e.clientY - node.y;
            element.style.cursor = 'grabbing';
            e.preventDefault();
        }
    });
    
    document.addEventListener('mousemove', (e) => {
        if (isDragging) {
            node.x = e.clientX - startX;
            node.y = e.clientY - startY;
            renderTree();
        }
    });
    
    document.addEventListener('mouseup', () => {
        if (isDragging) {
            isDragging = false;
            element.style.cursor = 'pointer';
        }
    });
}

// 选择节点
function selectNode(node) {
    selectedNode = node;

    if (isPathMode) {
        // 路径模式下，构建从根到当前节点的路径
        currentPath = getPathToNode(node);
        updatePathInfo();
    }

    renderTree();
}

// 获取到指定节点的路径
function getPathToNode(targetNode) {
    const path = [];

    function findPath(node, currentPath) {
        currentPath.push(node);

        if (node.id === targetNode.id) {
            return true;
        }

        if (node.children) {
            for (let child of node.children) {
                if (findPath(child, currentPath)) {
                    return true;
                }
            }
        }

        currentPath.pop();
        return false;
    }

    if (treeData.length > 0) {
        findPath(treeData[0], path);
    }

    return path;
}

// 检查节点是否在当前路径中
function isNodeInCurrentPath(node) {
    return currentPath.some(pathNode => pathNode.id === node.id);
}

// 更新路径信息
function updatePathInfo() {
    const pathInfo = document.getElementById('pathInfo');
    const pathContent = document.getElementById('pathContent');

    if (currentPath.length > 0) {
        const pathText = currentPath.map(node => node.text).join(' → ');
        pathContent.innerHTML = `
            <div class="mb-2"><strong>路径长度:</strong> ${currentPath.length} 步</div>
            <div><strong>路径:</strong> ${pathText}</div>
        `;
        pathInfo.classList.add('show');
    } else {
        pathInfo.classList.remove('show');
    }
}

// 开始编辑节点
function startEditing(node) {
    // 如果已经在编辑其他节点，先完成编辑
    if (editingNode) {
        cancelEditing();
    }

    editingNode = node;
    renderTree();
}

// 完成编辑
function finishEditing(node, newText) {
    if (newText && newText.trim() && newText.trim() !== node.text) {
        node.text = newText.trim();
    }
    editingNode = null;
    renderTree();
}

// 取消编辑
function cancelEditing() {
    editingNode = null;
    renderTree();
}

// 内联添加子节点
function addChildNodeInline(parentNode) {
    const newNode = {
        id: nodeIdCounter++,
        text: '新选择',
        children: []
    };

    if (!parentNode.children) {
        parentNode.children = [];
    }
    parentNode.children.push(newNode);

    // 重新计算布局
    calculateLayout(treeData[0]);
    renderTree();

    // 立即开始编辑新节点
    setTimeout(() => {
        startEditing(newNode);
    }, 100);
}

// 内联删除节点
function deleteNodeInline(nodeToDelete) {
    if (nodeToDelete.isRoot) {
        return; // 不能删除根节点
    }

    // 确认删除
    if (nodeToDelete.children && nodeToDelete.children.length > 0) {
        if (!confirm(`删除"${nodeToDelete.text}"将同时删除其所有子节点，确定继续吗？`)) {
            return;
        }
    }

    // 从树中删除节点
    deleteNodeFromTree(treeData[0], nodeToDelete.id);

    // 如果删除的是选中节点，清除选择
    if (selectedNode && selectedNode.id === nodeToDelete.id) {
        selectedNode = null;
    }

    // 如果删除的是编辑节点，取消编辑
    if (editingNode && editingNode.id === nodeToDelete.id) {
        editingNode = null;
    }

    renderTree();
}

// 添加根节点
function addRootNode() {
    if (treeData.length > 0) {
        if (!confirm('已存在根节点，是否要替换？这将清空所有数据。')) {
            return;
        }
    }

    const newNode = {
        id: nodeIdCounter++,
        text: '人生起点',
        isRoot: true,
        children: []
    };

    treeData = [newNode];
    selectedNode = newNode;
    editingNode = null;
    currentPath = [];

    renderTree();

    // 立即开始编辑新根节点
    setTimeout(() => {
        startEditing(newNode);
    }, 100);
}

// 从树中删除指定节点
function deleteNodeFromTree(node, targetId) {
    if (node.children) {
        node.children = node.children.filter(child => {
            if (child.id === targetId) {
                return false;
            }
            deleteNodeFromTree(child, targetId);
            return true;
        });
    }
}

// 保存树状图
function saveTree() {
    if (treeData.length === 0) {
        alert('没有数据可以保存');
        return;
    }
    
    const dataStr = JSON.stringify(treeData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = 'life-path-tree.json';
    link.click();
    
    URL.revokeObjectURL(url);
    alert('树状图已保存到文件');
}



// 搜索节点
function searchNodes() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    searchResults = [];

    if (searchTerm.trim() === '') {
        renderTree();
        return;
    }

    function searchInNode(node) {
        if (node.text.toLowerCase().includes(searchTerm)) {
            searchResults.push(node.id);
        }
        if (node.children) {
            node.children.forEach(child => searchInNode(child));
        }
    }

    if (treeData.length > 0) {
        searchInNode(treeData[0]);
    }

    renderTree();
}

// 按深度过滤
function filterByDepth() {
    const depthFilter = document.getElementById('depthFilter').value;
    maxDepthFilter = depthFilter ? parseInt(depthFilter) : null;
    renderTree();
}

// 缩放功能
function zoomIn() {
    currentZoom = Math.min(3, currentZoom * 1.2);
    renderTree();
}

function zoomOut() {
    currentZoom = Math.max(0.3, currentZoom / 1.2);
    renderTree();
}

function updateZoomDisplay() {
    const zoomLevel = document.getElementById('zoomLevel');
    if (zoomLevel) {
        zoomLevel.textContent = Math.round(currentZoom * 100) + '%';
    }
}

// 适应屏幕
function fitToScreen() {
    if (treeData.length === 0) return;

    // 计算所有节点的边界
    let minX = Infinity, maxX = -Infinity;
    let minY = Infinity, maxY = -Infinity;

    function getBounds(node) {
        minX = Math.min(minX, node.x - 60);
        maxX = Math.max(maxX, node.x + 60);
        minY = Math.min(minY, node.y - 20);
        maxY = Math.max(maxY, node.y + 20);

        if (node.children) {
            node.children.forEach(child => getBounds(child));
        }
    }

    getBounds(treeData[0]);

    const svg = document.getElementById('treeSvg');
    const svgRect = svg.getBoundingClientRect();

    const contentWidth = maxX - minX;
    const contentHeight = maxY - minY;

    const scaleX = (svgRect.width - 100) / contentWidth;
    const scaleY = (svgRect.height - 100) / contentHeight;

    currentZoom = Math.min(scaleX, scaleY, 2);

    panX = (svgRect.width / 2) - ((minX + maxX) / 2) * currentZoom;
    panY = (svgRect.height / 2) - ((minY + maxY) / 2) * currentZoom;

    renderTree();
}

// 自动布局
function autoLayout() {
    if (treeData.length > 0) {
        calculateLayout(treeData[0]);
        renderTree();
    }
}

// 切换路径模式
function togglePathMode() {
    isPathMode = !isPathMode;
    const button = event.target.closest('button');

    if (isPathMode) {
        button.classList.add('btn-primary');
        button.classList.remove('btn-outline-primary');
        if (selectedNode) {
            currentPath = getPathToNode(selectedNode);
            updatePathInfo();
        }
    } else {
        button.classList.remove('btn-primary');
        button.classList.add('btn-outline-primary');
        currentPath = [];
        document.getElementById('pathInfo').classList.remove('show');
    }

    renderTree();
}



// 重置树状图
function resetTree() {
    if (confirm('确定要重置树状图吗？这将清空所有数据。')) {
        initializeTree();
        selectedNode = null;
        editingNode = null;
        currentPath = [];
        searchResults = [];
        maxDepthFilter = null;
        currentZoom = 1;
        panX = 0;
        panY = 0;
        isPathMode = false;

        // 重置UI
        document.getElementById('searchInput').value = '';
        document.getElementById('depthFilter').value = '';
        document.getElementById('pathInfo').classList.remove('show');

        // 重置路径模式按钮
        const pathButton = document.querySelector('[onclick="togglePathMode()"]');
        if (pathButton) {
            pathButton.classList.remove('btn-primary');
            pathButton.classList.add('btn-outline-primary');
        }
    }
}
