<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>脚本管理 - ST计划</title>
    <!-- 添加网站图标 -->
    <link rel="icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <link rel="shortcut icon" href="assets/img/favicon.svg" type="image/svg+xml">
    
    <!-- 样式文件 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    <link rel="stylesheet" type="text/css" href="css/main.css">
    <link rel="stylesheet" type="text/css" href="css/script-manager.css">
    
    <style>
        body {
            overflow-x: hidden;
        }
        .view-container {
            width: 100%;
            overflow-x: hidden;
        }

        /* 密码验证层样式 */
        #password-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.95);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            flex-direction: column;
        }
        .password-container {
            width: 320px;
            max-width: 90%;
            background-color: #fff;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .password-container h3 {
            margin-bottom: 20px;
            color: #333;
        }
        .password-container .form-group {
            margin-bottom: 20px;
        }
        .password-error {
            color: #dc3545;
            margin-top: 10px;
            display: none;
        }
    </style>
</head>

<body>
    <!-- 密码验证层 -->
    <div id="password-overlay" style="display: none">
        <div class="password-container">
            <h3>ST计划访问验证</h3>
            <div class="form-group">
                <input type="password" id="access-password" class="form-control" placeholder="请输入访问密码">
            </div>
            <button id="password-submit" class="btn btn-primary w-100">确认</button>
            <div id="password-error" class="password-error">密码错误，请重试</div>
            <div class="form-check mt-3">
                <input class="form-check-input" type="checkbox" id="remember-password">
                <label class="form-check-label" for="remember-password">
                    在此设备上记住我
                </label>
            </div>
        </div>
    </div>

    <div class="container-fluid mt-3" id="main-content" style="display: none">
        <!-- 页面头部 -->
        <div class="row mb-4">
            <div class="col-6">
                <h2><i class="fas fa-code me-2"></i>脚本管理</h2>
            </div>
            <div class="col-6 text-end">
                <button class="btn btn-primary btn-sm" onclick="window.location.href='index.html'">
                    <i class="fas fa-home me-1" style="font-size: 14px;"></i>🏠 返回主页
                </button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="row">
            <!-- 左侧：脚本列表 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>脚本列表</h5>
                        <div class="header-actions">
                            <button class="btn btn-outline-danger btn-sm me-2" id="batch-delete-btn" onclick="toggleBatchMode()" title="批量删除">
                                <i class="fas fa-trash-alt me-1"></i>批量删除
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="showAddScriptModal()">
                                <i class="fas fa-plus me-1"></i>新建
                            </button>
                        </div>
                    </div>

                    <!-- 批量操作工具栏 -->
                    <div class="batch-toolbar" id="batch-toolbar" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center p-2 bg-light border-bottom">
                            <div class="batch-selection">
                                <label class="form-check-label me-3">
                                    <input type="checkbox" class="form-check-input me-1" id="select-all-checkbox" onchange="toggleSelectAll()">
                                    全选
                                </label>
                                <span class="selected-count text-muted" id="selected-count">已选择 0 个脚本</span>
                            </div>
                            <div class="batch-actions">
                                <button class="btn btn-outline-secondary btn-sm me-2" onclick="selectInverse()">
                                    <i class="fas fa-exchange-alt me-1"></i>反选
                                </button>
                                <button class="btn btn-danger btn-sm me-2" onclick="batchDeleteSelected()" id="batch-delete-confirm-btn" disabled>
                                    <i class="fas fa-trash me-1"></i>删除选中
                                </button>
                                <button class="btn btn-secondary btn-sm" onclick="exitBatchMode()">
                                    <i class="fas fa-times me-1"></i>取消
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="card-body p-0">
                        <div id="scripts-list" class="scripts-list">
                            <!-- 脚本列表将在这里动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="card mt-3">
                    <div class="card-body text-center">
                        <div class="row">
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-number" id="total-scripts">0</div>
                                    <div class="stat-label">总脚本数</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-number" id="total-size">0KB</div>
                                    <div class="stat-label">存储大小</div>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>

            <!-- 右侧：脚本详情和编辑 -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0" id="script-title">
                            <i class="fas fa-file-code me-2"></i>选择一个脚本查看详情
                        </h5>
                        <div id="script-actions" style="display: none;">
                            <button class="btn btn-outline-primary btn-sm me-2" onclick="editCurrentScript()">
                                <i class="fas fa-edit me-1"></i>编辑
                            </button>
                            <button class="btn btn-outline-success btn-sm me-2" onclick="copyScriptCode()">
                                <i class="fas fa-copy me-1"></i>复制
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="deleteCurrentScript()">
                                <i class="fas fa-trash me-1"></i>删除
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="script-details" class="script-details">
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-code fa-3x mb-3"></i>
                                <p>请从左侧选择一个脚本查看详情</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑脚本模态框 -->
    <div class="modal fade" id="scriptModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="scriptModalTitle">
                        <i class="fas fa-plus me-2"></i>新建脚本
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="scriptForm">
                        <div class="mb-3">
                            <label for="scriptName" class="form-label">脚本名称</label>
                            <input type="text" class="form-control" id="scriptName" required>
                        </div>

                        <div class="mb-3">
                            <label for="scriptLanguage" class="form-label">脚本语言</label>
                            <select class="form-select" id="scriptLanguage">
                                <option value="javascript">JavaScript</option>
                                <option value="python">Python</option>
                                <option value="bash">Bash</option>
                                <option value="powershell">PowerShell</option>
                                <option value="sql">SQL</option>
                                <option value="html">HTML</option>
                                <option value="css">CSS</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="scriptCode" class="form-label">脚本代码</label>
                            <textarea class="form-control code-editor" id="scriptCode" rows="15" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveScript()">保存脚本</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.min.js"></script>
    <script src="js/script-manager.js"></script>
    
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查密码验证
            checkPasswordAccess();
            
            // 初始化脚本管理器
            if (typeof ScriptManager !== 'undefined') {
                ScriptManager.init();
            }
        });
    </script>
</body>
</html>
