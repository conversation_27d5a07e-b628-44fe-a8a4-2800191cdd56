<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>任务进度 - ST计划</title>
    <!-- 添加网站图标 -->
    <link rel="icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <link rel="shortcut icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <!-- 样式文件 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    <link rel="stylesheet" type="text/css" href="css/main.css">
    <style>
        /* 整体样式 */
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: #f7f9fc;
            color: #333;
        }
        
        /* 主容器 */
        .container-fluid {
            max-width: 1400px;
            padding: 20px;
        }
        
        /* 标题区域 */
        .header {
            padding: 20px 0 15px;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        /* 搜索框 */
        .search-container {
            margin-bottom: 20px;
        }
        
        /* 任务板样式 */
        .task-board {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            margin-bottom: 16px;
            padding: 16px;
        }
        
        .task-board-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .task-board-header:hover {
            background-color: #f8f9fa;
            border-radius: 4px;
            margin: 0 -8px 16px -8px;
            padding: 8px 8px 12px 8px;
        }
        
        .task-board-title {
            font-weight: 600;
            font-size: 18px;
            cursor: pointer;
            user-select: none;
            display: flex;
            align-items: center;
            flex: 1;
        }
        
        /* 任务项样式 - 更紧凑 */
        .task-item {
            padding: 8px 12px;
            margin-bottom: 6px;
            background: white;
            border-radius: 6px;
            transition: all 0.2s ease;
            border-left: 3px solid #3498db;
            position: relative; /* 相对定位容器 */
        }
        
        .task-item:hover {
            box-shadow: 0 3px 8px rgba(0,0,0,0.08);
        }
        
        .task-item.task-completed {
            opacity: 0.7;
            border-left: 3px solid #27ae60;
        }
        
        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .task-title {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 0;
            overflow: hidden;
            padding-right: 95px; /* 为右侧按钮预留空间 */
        }
        
        .task-name {
            margin: 0 8px 0 8px;
            font-size: 14px;
            cursor: pointer;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            flex: 1;
            line-height: 1.2;
        }
        
        .task-checkbox {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }
        
        .task-completed .task-name {
            text-decoration: line-through;
            color: #7f8c8d;
        }
        
        .task-deadline {
            padding: 2px 6px;
            font-size: 12px;
            border-radius: 10px;
            background-color: #f0f0f0;
            margin-left: 8px;
            white-space: nowrap;
        }
        
        .task-deadline.urgent {
            background-color: #ffeceb;
            color: #e74c3c;
        }
        
        .task-actions {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 2px;
            position: absolute; /* 绝对定位 */
            right: 12px;        /* 固定在右侧 */
            top: 8px;           /* 与任务项内边距对齐 */
            opacity: 0;         /* 默认隐藏 */
            transition: opacity 0.2s ease; /* 平滑过渡 */
        }
        
        /* 鼠标悬停在任务项上时显示按钮 */
        .task-item:hover .task-actions {
            opacity: 1;
        }
        
        /* 子任务容器 - 仅缩进内容 */
        .subtasks-container {
            margin-top: 6px;
            padding-left: 24px; /* 只缩进内容，不影响整体宽度 */
            border-left: 1px dashed #e0e0e0;
            margin-left: 8px;   /* 适当的左侧边距 */
        }
        
        /* 添加子任务表单 */
        .subtask-form-container {
            margin-top: 4px;
            padding: 6px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }

        /* 长按提示器 */
        .task-item.long-press-active {
            background-color: #f0f7ff;
            transition: background-color 0.3s ease;
        }
        
        .long-press-indicator {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid #3498db;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
            display: none;
            z-index: 100;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 设备响应式调整 */
        @media (max-width: 768px) {
            .task-actions .btn-icon {
                padding: 1px 4px;
                font-size: 11px;
            }
            
            .task-name {
                font-size: 13px;
            }
            
            .task-title {
                padding-right: 80px; /* 移动端稍微窄一点 */
            }
            
            /* 在移动设备上始终显示按钮，因为没有hover */
            .task-actions {
                opacity: 1;
            }
        }
        
        /* 按钮图标样式 - 更小 */
        .btn-icon {
            width: 22px;
            height: 22px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            font-size: 0.7rem;
        }
        
        .btn-icon i {
            font-size: 0.7rem;
        }
        
        /* 进度条样式 */
        .progress {
            height: 6px;
            margin-top: 8px;
            border-radius: 3px;
            background-color: #f0f0f0;
        }
        
        .progress-bar {
            transition: width 0.3s ease;
        }
        
        /* 工具栏样式 */
        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        /* 添加任务/任务板表单 */
        .add-form {
            background: white;
            border-radius: 6px;
            padding: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 16px;
        }
        
        /* 任务表列表样式 */
        .boards-list {
            margin-bottom: 16px;
        }
        
        .board-card {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: white;
            border-radius: 6px;
            margin-bottom: 6px;
            transition: all 0.2s;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .board-card:hover {
            box-shadow: 0 3px 8px rgba(0,0,0,0.1);
        }
        
        .board-card.active {
            background-color: #ebf5ff;
            border-left: 3px solid #3498db;
        }
        
        /* 右键菜单样式 */
        .context-menu {
            position: absolute;
            display: none;
            background-color: #fff;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            min-width: 160px;
            padding: 6px 0;
        }
        
        .context-menu-item {
            display: flex;
            align-items: center;
            padding: 6px 12px;
            cursor: pointer;
            transition: background-color 0.2s;
            color: #333;
            font-size: 13px;
        }
        
        .context-menu-item:hover {
            background-color: #f5f5f5;
        }
        
        .context-menu-item i {
            margin-right: 8px;
            width: 14px;
            text-align: center;
            font-size: 12px;
        }
        
        .context-menu-item.text-danger:hover {
            background-color: #fee;
        }
        
        /* 按钮样式 */
        .btn-sm {
            padding: 0.1rem 0.3rem;
            font-size: 0.75rem;
        }
        
        /* 表单组件紧凑化 */
        .form-control-sm {
            padding: 0.2rem 0.4rem;
            font-size: 0.8rem;
            height: calc(1.5em + 0.5rem + 2px);
        }
        
        /* 卡片和面板紧凑化 */
        .card {
            margin-bottom: 12px;
        }
        
        .card-header {
            padding: 8px 12px;
        }
        
        .card-body {
            padding: 10px 12px;
        }
    </style>
</head>

<body>
    <div class="container mt-3">
        <!-- 导航按钮 -->
        <div class="row mb-3">
            <div class="col-12 d-flex justify-content-end">
                <button class="btn btn-primary btn-sm" onclick="window.location.href='index.html'">
                    <i class="fas fa-home me-1" style="font-size: 14px;"></i>🏠 返回主页
                </button>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-3">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">任务管理</h5>
                    </div>
                    <div class="card-body">
                        <!-- 添加任务表表单 -->
                        <form id="taskBoardForm" class="mb-2">
                            <div class="mb-2">
                                <label class="form-label small">新建任务表:</label>
                                <input class="form-control form-control-sm" id="boardName" type="text" placeholder="输入任务表名称" required />
                            </div>
                            <button class="btn btn-primary btn-sm w-100" type="submit">
                                <i class="fas fa-plus"></i> 创建任务表
                            </button>
                        </form>
                        
                        <!-- 任务表列表 -->
                        <div class="mb-2">
                            <label class="form-label small">任务表列表:</label>
                            <div class="list-group" id="taskBoardsList">
                                <!-- 任务表将在这里动态添加 -->
                            </div>
                        </div>
                        
                        <!-- 系统选项 -->
                        <div class="card mt-2">
                            <div class="card-header py-1 small fw-bold">系统选项</div>
                            <div class="card-body p-2">
                                <div class="form-check form-switch mb-1">
                                    <input class="form-check-input" type="checkbox" id="showCompletedTasks" checked>
                                    <label class="form-check-label small" for="showCompletedTasks">显示已完成任务</label>
                                </div>
                                <div class="form-check form-switch mb-1">
                                    <input class="form-check-input" type="checkbox" id="sortByDeadline">
                                    <label class="form-check-label small" for="sortByDeadline">按截止日期排序</label>
                                </div>
                                <button class="btn btn-sm btn-danger w-100 mt-1" id="clearCompletedTasks">
                                    清理所有已完成任务
                                </button>
                                <button class="btn btn-sm btn-outline-danger w-100 mt-1" id="resetTaskSystem">
                                    <i class="fas fa-sync-alt"></i> 重置任务系统
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-9">
                <div class="task-container">
                    <!-- 添加任务面板 -->
                    <div class="add-task-panel">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5 class="mb-0">添加新任务</h5>
                            <button class="btn btn-outline-info btn-sm" id="batchModeToggle">
                                <i class="fas fa-list me-1"></i>批量模式
                            </button>
                        </div>

                        <!-- 普通添加表单 -->
                        <form id="addTaskForm" class="normal-add-form">
                            <div class="row g-2">
                                <div class="col-md-7">
                                    <input type="text" class="form-control form-control-sm" id="taskName" placeholder="任务名称" required>
                                </div>
                                <div class="col-md-3">
                                    <input type="text" class="form-control form-control-sm" id="taskDeadline" placeholder="截止日期 (MMDD)">
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary btn-sm w-100"><i class="fas fa-plus"></i></button>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-12">
                                    <select class="form-select form-select-sm" id="taskBoardSelect" required>
                                        <!-- 任务表选项将在这里动态添加 -->
                                    </select>
                                    <small class="text-muted fs-6">*长按任务项快速添加子任务</small>
                                </div>
                            </div>
                        </form>

                        <!-- 批量添加表单 -->
                        <div id="batchAddContainer" class="batch-add-form" style="display: none;">
                            <div class="mb-3">
                                <label class="form-label small">批量添加任务</label>
                                <textarea class="form-control form-control-sm" id="batchTaskNames" rows="6" placeholder="输入多个任务名称，每行一个任务，空行会被忽略&#10;例如：&#10;吃饭&#10;睡觉&#10;洗漱" required></textarea>
                                <small class="text-muted">每行一个任务，空行会被忽略</small>
                            </div>
                            <div class="row g-2">
                                <div class="col-md-8">
                                    <select class="form-select form-select-sm" id="batchTaskBoardSelect" required>
                                        <!-- 任务表选项将在这里动态添加 -->
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <button type="button" class="btn btn-primary btn-sm w-100" id="batchAddBtn">
                                        <i class="fas fa-plus me-1"></i>批量添加
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 任务表内容区域 -->
                    <div id="taskBoardsContainer">
                        <!-- 任务表内容将在这里动态添加 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 模态框：编辑任务 -->
    <div class="modal fade" id="editTaskModal" tabindex="-1" aria-labelledby="editTaskModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header py-2">
                    <h5 class="modal-title" id="editTaskModalLabel">编辑任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body py-2">
                    <form id="editTaskForm">
                        <input type="hidden" id="editTaskId">
                        <div class="mb-2">
                            <label for="editTaskName" class="form-label small">任务名称</label>
                            <input type="text" class="form-control form-control-sm" id="editTaskName" required>
                        </div>
                        <div class="mb-2">
                            <label for="editTaskDeadline" class="form-label small">截止日期</label>
                            <input type="text" class="form-control form-control-sm" id="editTaskDeadline" placeholder="截止日期 (MMDD)">
                        </div>
                        <div class="mb-2">
                            <label for="editTaskBoard" class="form-label small">任务表</label>
                            <select class="form-select form-select-sm" id="editTaskBoard">
                                <!-- 任务表选项将在这里动态添加 -->
                            </select>
                        </div>
                        <div class="mb-0">
                            <label for="editParentTask" class="form-label small">父任务</label>
                            <select class="form-select form-select-sm" id="editParentTask">
                                <option value="">顶级任务</option>
                                <!-- 父任务选项将在这里动态添加 -->
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer py-2">
                    <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-sm btn-primary" id="saveTaskEdit">保存更改</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript 文件 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 引入主系统配置和数据 -->
    <script src="js/st.js"></script>
    <!-- 任务进度系统脚本 -->
    <script src="js/task-progress.js"></script>
</body>

</html>