/* Progress bar styles */

.progress {
    height: 10px !important;
    background-color: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin: 0.15rem 0 !important;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.08);
    position: relative;
}

.progress-bar {
    display: flex;
    flex-direction: column;
    justify-content: center;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    background-color: #0d6efd;
    transition: width 0.8s cubic-bezier(0.22, 0.61, 0.36, 1);
    background: linear-gradient(to right, #3498db, #2980b9);
    animation: progressFill 1.2s ease-out forwards;
    background-size: 150% 100%;
    box-shadow: 0 0 6px rgba(52, 152, 219, 0.5);
    font-size: 9px;
    font-weight: bold;
    text-shadow: 0 1px 1px rgba(0,0,0,0.3);
    align-items: center;
    justify-content: center;
    line-height: 1;
}

.progress-bar::before,
.progress-bar > *,
.progress-bar span,
.progress-bar i,
.progress-bar em {
    font-size: 11px !important;
    line-height: 1;
    position: relative;
    top: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.progress-bar [class*="icon"],
.progress-bar [class*="fa-"],
.progress-bar [class*="emoji"],
.progress-bar:has(> *) {
    font-size: 11px !important;
    vertical-align: middle;
    line-height: 1;
}

.progress-bar.bg-danger {
    background-color: #dc3545 !important;
    background: linear-gradient(to right, #e74c3c, #c0392b) !important;
    box-shadow: 0 0 6px rgba(231, 76, 60, 0.5);
}

.progress-bar.high {
    background: linear-gradient(45deg, #48bb78, #68d391);
    box-shadow: 0 0 6px rgba(72, 187, 120, 0.5);
}

.progress-bar.medium {
    background: linear-gradient(45deg, #ecc94b, #f6e05e);
    box-shadow: 0 0 6px rgba(236, 201, 75, 0.5);
}

.progress-bar.low {
    background: linear-gradient(45deg, #f56565, #fc8181);
    box-shadow: 0 0 6px rgba(245, 101, 101, 0.5);
}

/* For age goals, use different colors */
.progress-bar:not(.bg-danger):not(.age-goal-card.lifetime .progress-bar):not(.savings-progress-bar) {
    background: linear-gradient(to right, #3498db, #2980b9) !important;
}

.progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, 
                rgba(255,255,255,0.2) 0%, 
                rgba(255,255,255,0.05) 40%, 
                rgba(0,0,0,0.05) 100%);
    pointer-events: none;
}

/* Enhanced progress display */
.progress-wrapper {
    position: relative;
    background: #ffffff;
    border-radius: 6px;
    padding: 0.75rem;
    margin: 0.5rem 0;
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.03);
}

.progress-text {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    white-space: nowrap;
}

.progress-details {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
    color: #718096;
    margin-top: 0.5rem;
}

/* Amount display in progress */
.amount-display-wrapper {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.amount-item {
    text-align: center;
    position: relative;
}

.amount-label {
    font-size: 0.8rem;
    color: #64748b;
    margin-bottom: 0.15rem;
    opacity: 0.9;
}

.amount-value {
    font-size: 1.15rem;
    font-weight: 600;
    color: #1a202c;
    text-shadow: 0 1px 0 rgba(255,255,255,0.7);
}

.amount-value.current {
    color: #3182ce;
}

.amount-value.target {
    color: #2d3748;
}

.remaining-amount {
    color: #e53e3e;
    font-weight: 600;
    font-size: 0.85rem;
    text-align: right;
    padding-top: 0.2rem;
    letter-spacing: 0.2px;
}

/* Progress animation */
@keyframes progressFill {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(0); }
}

/* 存款进度条闪金光动画 - 增强版 */
@keyframes goldGlowing {
    0% {
        background-position: -200% 0;
        box-shadow:
            0 0 10px rgba(255, 215, 0, 0.8),
            0 0 20px rgba(255, 215, 0, 0.6),
            0 0 30px rgba(255, 215, 0, 0.4),
            inset 0 0 10px rgba(255, 255, 255, 0.2);
        filter: brightness(1.0) saturate(1.2);
    }
    25% {
        box-shadow:
            0 0 15px rgba(255, 215, 0, 0.9),
            0 0 30px rgba(255, 215, 0, 0.7),
            0 0 45px rgba(255, 215, 0, 0.5),
            inset 0 0 15px rgba(255, 255, 255, 0.3);
        filter: brightness(1.1) saturate(1.3);
    }
    50% {
        background-position: 0% 0;
        box-shadow:
            0 0 20px rgba(255, 215, 0, 1.0),
            0 0 40px rgba(255, 215, 0, 0.8),
            0 0 60px rgba(255, 215, 0, 0.6),
            inset 0 0 20px rgba(255, 255, 255, 0.4);
        filter: brightness(1.2) saturate(1.4);
    }
    75% {
        box-shadow:
            0 0 15px rgba(255, 215, 0, 0.9),
            0 0 30px rgba(255, 215, 0, 0.7),
            0 0 45px rgba(255, 215, 0, 0.5),
            inset 0 0 15px rgba(255, 255, 255, 0.3);
        filter: brightness(1.1) saturate(1.3);
    }
    100% {
        background-position: 200% 0;
        box-shadow:
            0 0 10px rgba(255, 215, 0, 0.8),
            0 0 20px rgba(255, 215, 0, 0.6),
            0 0 30px rgba(255, 215, 0, 0.4),
            inset 0 0 10px rgba(255, 255, 255, 0.2);
        filter: brightness(1.0) saturate(1.2);
    }
}

/* 存款进度条专用样式 - 最高优先级 */
.progress-bar.savings-progress-bar,
.progress .progress-bar.savings-progress-bar,
.progress-wrapper .progress-bar.savings-progress-bar {
    background: linear-gradient(45deg, #FFD700 0%, #FFA500 25%, #FFDF00 50%, #FFB347 75%, #FFD700 100%) !important;
    background-size: 400% 100% !important;
    animation: goldGlowing 1.5s infinite ease-in-out !important;
    position: relative !important;
    overflow: hidden !important;
}

.savings-progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 20%,
        rgba(255, 255, 255, 0.8) 50%,
        rgba(255, 255, 255, 0.3) 80%,
        transparent 100%);
    animation: goldShine 2s infinite linear;
    z-index: 1;
}

.savings-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(255,255,255,0.1) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255,255,255,0.1) 50%,
        rgba(255,255,255,0.1) 75%,
        transparent 75%,
        transparent);
    background-size: 20px 20px;
    animation: goldPattern 3s infinite linear;
    z-index: 2;
}

@keyframes goldShine {
    0% {
        left: -100%;
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        left: 100%;
        opacity: 0;
    }
}

@keyframes goldPattern {
    0% { background-position: 0 0; }
    100% { background-position: 20px 20px; }
}

/* 时间进度条特殊样式 */
.time-progress-bar {
    background: linear-gradient(to right, #9c59b6, #8e44ad) !important;
    box-shadow: 0 0 6px rgba(156, 89, 182, 0.5);
}

.time-warning {
    background: linear-gradient(to right, #e67e22, #d35400) !important;
    box-shadow: 0 0 6px rgba(230, 126, 34, 0.5);
}

.time-expired {
    background: linear-gradient(to right, #c0392b, #922b21) !important;
    box-shadow: 0 0 6px rgba(192, 57, 43, 0.5);
}

/* 处理表情符号在进度条中的显示 */
.progress-bar:has(> *) {
    padding: 0 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 特别处理emoji表情符号 */
.progress-bar:has(> *):before {
    content: "";
    display: inline-block;
    width: 0;
    height: 100%;
    vertical-align: middle;
}

/* 进度条内的emoji图标特殊样式 */
.progress .emoji-icon,
.progress-bar .emoji-icon,
.progress-bar:has(> [class*="emoji"]),
.progress-bar:has(> *) {
    font-size: 11px !important;
    line-height: 1;
    padding: 0 2px;
    vertical-align: middle;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

/* 确保表情符号可见 */
.progress-bar span,
.progress-bar > *,
.progress-bar:first-child {
    position: relative;
    z-index: 2;
    mix-blend-mode: normal;
    color: white;
    text-shadow: 0 1px 1px rgba(0,0,0,0.4);
}

