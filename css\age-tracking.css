/* Age tracking styles */

/* Age progress card */
.age-progress-card {
    background: linear-gradient(to bottom right, #f8f9fa, #ffffff);
    border-radius: 0.5rem;
    padding: 1.5rem;
}

.age-progress-card .card-title {
    color: #2c3e50;
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.age-progress-card .card-text {
    color: #34495e;
    line-height: 1.6;
    margin-bottom: 1.25rem;
}

.age-progress-card .progress {
    height: 1.5rem;
    border-radius: 0.75rem;
    background-color: #e9ecef;
    margin: 1rem 0;
}

.age-progress-card .progress-bar {
    background: linear-gradient(to right, #3498db, #2980b9);
    border-radius: 0.75rem;
    transition: width 0.3s ease;
}

.age-progress-card .progress-bar.bg-danger {
    background: linear-gradient(to right, #e74c3c, #c0392b);
}

.age-progress-card .btn {
    background: #3498db;
    border: none;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.age-progress-card .btn:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

/* Age goals section */
.age-goals-section {
    margin-bottom: 1rem;
}

.age-category-title {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    margin: 1rem 0 0.75rem;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.age-category-title.yearly {
    background: rgba(231, 76, 60, 0.1);
    color: #c0392b;
}

.age-category-title.yearly::before {
    content: "📅";
}

.age-category-title.lifetime {
    background: rgba(52, 152, 219, 0.1);
    color: #2980b9;
}

.age-category-title.lifetime::before {
    content: "🌟";
}

/* Age goals grid */
.age-goals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 0.35rem;
    padding: 0 0.35rem;
}

/* Age goal cards - 增强版 */
.age-goal-card {
    border: none;
    border-radius: 16px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    box-shadow:
        0 4px 20px rgba(0,0,0,0.08),
        0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: 0.75rem;
    position: relative;
    overflow: hidden;
}

.age-goal-card:hover {
    transform: translateY(-3px) scale(1.01);
    box-shadow:
        0 8px 30px rgba(0,0,0,0.12),
        0 4px 8px rgba(0,0,0,0.08);
}

.age-goal-card .card-body {
    padding: 0.5rem !important;
}

/* Yearly goal styling - 增强版 */
.age-goal-card.yearly {
    border-left: 5px solid #e74c3c;
    background: linear-gradient(135deg,
        rgba(231, 76, 60, 0.08) 0%,
        rgba(231, 76, 60, 0.02) 50%,
        transparent 100%);
    position: relative;
}

.age-goal-card.yearly::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(231, 76, 60, 0.1), transparent);
    border-radius: 0 16px 0 100%;
}

.age-goal-card.yearly .goal-name::before {
    content: "📅";
    margin-right: 8px;
    filter: drop-shadow(0 1px 2px rgba(231, 76, 60, 0.3));
}

.age-goal-card.yearly .progress-bar {
    background: linear-gradient(90deg, #e74c3c, #c0392b, #a93226) !important;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

/* Lifetime goal styling - 增强版 */
.age-goal-card.lifetime {
    border-left: 5px solid #3498db;
    background: linear-gradient(135deg,
        rgba(52, 152, 219, 0.08) 0%,
        rgba(52, 152, 219, 0.02) 50%,
        transparent 100%);
    position: relative;
}

.age-goal-card.lifetime::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(52, 152, 219, 0.1), transparent);
    border-radius: 0 16px 0 100%;
}

.age-goal-card.lifetime .goal-name::before {
    content: "🌟";
    margin-right: 8px;
    filter: drop-shadow(0 1px 2px rgba(52, 152, 219, 0.3));
    animation: starTwinkle 2s ease-in-out infinite alternate;
}

@keyframes starTwinkle {
    0% { filter: drop-shadow(0 1px 2px rgba(52, 152, 219, 0.3)) brightness(1); }
    100% { filter: drop-shadow(0 2px 4px rgba(52, 152, 219, 0.5)) brightness(1.2); }
}

.age-goal-card.lifetime .progress-bar {
    background: linear-gradient(90deg, #f39c12, #e67e22, #d35400) !important;
    box-shadow: 0 2px 8px rgba(243, 156, 18, 0.3);
}

/* Goal info layout */
.age-goal-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    margin-bottom: 0.5rem;
}

.goal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
}

.goal-name {
    font-size: 1rem;
    font-weight: 600;
    color: #1a365d;
}

/* Age range display */
.age-range {
    display: flex;
    align-items: center;
    gap: 0.35rem;
    color: #4a5568;
    font-size: 0.85rem;
    margin-bottom: 0.35rem;
}

.age-range span {
    color: #2c5282;
    font-weight: 500;
}

.age-arrow {
    color: #a0aec0;
    font-weight: normal;
}

/* Time information - 增强版 */
.time-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #f7fafc, #edf2f7);
    padding: 0.4rem 0.6rem;
    border-radius: 8px;
    margin-top: 0.3rem;
    border: 1px solid rgba(226, 232, 240, 0.8);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.time-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(66, 153, 225, 0.1),
        transparent);
    transition: left 0.5s ease;
}

.time-info:hover::before {
    left: 100%;
}

.time-info:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.remaining-days {
    display: flex;
    align-items: center;
    gap: 0.35rem;
    color: #4a5568;
    font-size: 0.85rem;
}

.remaining-days i {
    color: #718096;
}

.days-count {
    font-weight: 700;
    font-size: 0.95rem;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

/* 紧急度指示器 */
.days-count.urgent {
    color: #fff;
    background: linear-gradient(135deg, #e53e3e, #c53030);
    animation: urgentPulse 1.5s ease-in-out infinite;
    box-shadow: 0 2px 8px rgba(229, 62, 62, 0.4);
}

.days-count.warning {
    color: #fff;
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    box-shadow: 0 2px 8px rgba(237, 137, 54, 0.3);
}

.days-count.normal {
    color: #2b6cb0;
    background: linear-gradient(135deg, #bee3f8, #90cdf4);
}

@keyframes urgentPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(229, 62, 62, 0.4);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 4px 16px rgba(229, 62, 62, 0.6);
    }
}

.days-label {
    color: #718096;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Time progress values */
.time-value {
    font-size: 0.8rem;
    font-weight: 500;
}

.time-value.urgent {
    color: #c53030;
}

.time-value.warning {
    color: #b7791f;
}

.time-value.normal {
    color: #2b6cb0;
}

/* Progress section */
.progress-section {
    margin: 0.35rem 0 0 0;
}

.progress {
    margin: 0;
    height: 0.5rem !important;
}

/* 针对年龄目标的进度条特别处理 */
.age-goal-card .progress {
    height: 14px !important;
    overflow: hidden;
}

.age-goal-card .progress-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    line-height: 1;
}

/* 确保年龄目标的进度条中的表情图标正确显示 */
.age-goal-card .progress-bar::before,
.age-goal-card .progress-bar > *,
.age-goal-card .progress-bar span,
.age-goal-card .progress-bar i,
.age-goal-card .progress-bar em {
    font-size: 11px !important;
    line-height: 1;
    vertical-align: middle;
    position: relative;
    top: 0;
}

/* Goal actions */
.age-goal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.35rem;
    margin-top: 0.5rem;
}

.age-goal-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    color: #ffffff;
}

.age-goal-actions .btn-icon {
    color: #ffffff;
}

.age-goal-card .btn-sm {
    padding: 0.15rem 0.35rem !important;
    font-size: 0.75rem !important;
    line-height: 1.2;
    border-radius: 4px;
}

.age-goal-card .btn-icon {
    font-size: 0.7rem;
    margin-right: 0.15rem;
}

/* 年龄数字荧光效果 - 增强版 */
.age-display {
    background: linear-gradient(45deg, #9c27b0, #673ab7, #3f51b5, #2196f3);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 800;
    font-size: 1.1rem;
    display: inline-block;
    padding: 0.3rem 0.6rem;
    border-radius: 8px;
    position: relative;
    animation:
        glowPurple 2s ease-in-out infinite alternate,
        gradientShift 4s ease-in-out infinite;
    filter: drop-shadow(0 2px 4px rgba(156, 39, 176, 0.3));
}

.age-display::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #9c27b0, #673ab7, #3f51b5, #2196f3);
    background-size: 300% 300%;
    border-radius: 10px;
    z-index: -1;
    opacity: 0.3;
    animation: gradientShift 4s ease-in-out infinite;
    filter: blur(4px);
}

@keyframes glowPurple {
    0% {
        filter: drop-shadow(0 2px 4px rgba(156, 39, 176, 0.3)) brightness(1);
    }
    100% {
        filter: drop-shadow(0 4px 12px rgba(156, 39, 176, 0.6)) brightness(1.1);
    }
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}