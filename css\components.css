/* Component styles */

/* 全局Font Awesome图标修复 */
.fas, .far, .fab, .fa {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands", "Font Awesome 5 Free" !important;
    font-weight: 900 !important;
    display: inline-block !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    line-height: 1 !important;
}

.fas::before, .far::before, .fab::before, .fa::before {
    display: inline-block !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

/* 确保图标在所有容器中都能正确显示 */
* .fas::before, * .far::before, * .fab::before, * .fa::before {
    display: inline-block !important;
    content: inherit !important;
}

/* Form controls */
.form-control {
    font-size: 0.85rem;
    padding: 0.35rem 0.5rem;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    height: auto;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52,152,219,0.1);
}

.form-label {
    font-size: 0.85rem;
    color: #4a5568;
    margin-bottom: 0.25rem;
    font-weight: 500;
}

/* Buttons */
.btn {
    font-size: 0.85rem !important;
    padding: 0.35rem 0.5rem;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    white-space: nowrap;
    height: auto;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem !important;
}

/* Button colors */
.btn-primary {
    background: linear-gradient(45deg, #3498db, #2980b9);
    border: none;
    color: white;
}

.btn-success {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    border: none;
    color: white;
}

.btn-warning {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    border: none;
    color: white;
}

.btn-danger {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    border: none;
    color: white;
}

.btn-outline-primary {
    color: #3498db !important;
    border-color: #3498db !important;
}

.btn-outline-primary:hover,
.btn-check:checked + .btn-outline-primary {
    color: white !important;
    background-color: #3498db !important;
}

/* Button groups */
.btn-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    border-radius: 6px;
    overflow: hidden;
}

.btn-group .btn {
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 4px !important;
    margin: 0.125rem !important;
    font-size: 0.875rem !important;
    padding: 0.25rem 0.75rem !important;
}

.btn-group .btn-check:checked + .btn-outline-primary {
    background-color: #0d6efd;
    color: white;
    box-shadow: 0 2px 4px rgba(13, 110, 253, 0.2);
}

/* Action button groups */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.action-buttons .btn {
    flex: 1;
    padding: 0.5rem;
    font-size: 0.9rem;
    border-radius: 4px;
    border: none;
}

/* Input groups */
.input-group {
    display: flex;
    gap: 0.5rem;
}

.input-group .form-control {
    flex: 1;
    border-radius: 0.375rem;
}

.input-group .btn {
    white-space: nowrap;
    border-radius: 0.375rem;
}

/* Navigation buttons */
.nav-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-top: 0.5rem;
}

.nav-buttons .btn {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 4px;
    margin: 0;
    white-space: nowrap;
}

.nav-buttons .btn-check:checked + .btn-outline-primary {
    background-color: #0d6efd;
    color: white;
}

/* View switch */
.view-switch {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.view-switch button {
    padding: 0.25rem 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: #fff;
    color: #495057;
    cursor: pointer;
    transition: all 0.2s;
}

.view-switch button.active {
    background: #0d6efd;
    color: #fff;
    border-color: #0d6efd;
}

.view-switch button i {
    margin-right: 0.35rem;
}

.view-switch button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 习惯打卡卡片的紧凑样式 */
.goal-container .card[data-habit-index] {
    margin-bottom: 0 !important;
    box-shadow: 0 2px 5px rgba(0,0,0,0.12);
    position: relative;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease;
    border-radius: 6px;
}

.goal-container .card[data-habit-index]:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 7px rgba(0,0,0,0.15);
}

/* 重要：禁用习惯卡片上的所有金钱和硬币图标伪元素，但保留Font Awesome图标 */
.goal-container .card[data-habit-index]::before,
.goal-container .card[data-habit-index]::after {
    content: "" !important;
    display: none !important;
}

/* 禁用非图标元素的伪元素，但保留Font Awesome图标 */
.goal-container .card[data-habit-index] *:not(.fas):not(.far):not(.fab):not(.fa):not(i)::before,
.goal-container .card[data-habit-index] *:not(.fas):not(.far):not(.fab):not(.fa):not(i)::after {
    content: "" !important;
    display: none !important;
}

/* 确保图标按钮中的图标正确显示 */
.goal-container .card[data-habit-index] .btn i::before,
.goal-container .card[data-habit-index] .fas::before,
.goal-container .card[data-habit-index] .far::before,
.goal-container .card[data-habit-index] .fab::before,
.goal-container .card[data-habit-index] .fa::before {
    display: inline-block !important;
    content: inherit !important;
}

.goal-container .card[data-habit-index] .card-body {
    padding: 0.4rem !important;
}

.goal-container .card[data-habit-index] .mb-1 {
    margin-bottom: 0.15rem !important;
}

.goal-container .card[data-habit-index] .progress {
    margin-bottom: 0.15rem !important;
    height: 4px !important;
}

.goal-container .card[data-habit-index] .mt-1 {
    margin-top: 0.2rem !important;
}

.goal-container .card[data-habit-index] .card-title {
    font-size: 0.9rem;
    padding: 0 1.5rem;
    width: 100%;
    text-align: center;
}

.goal-container .card[data-habit-index] .text-muted.small {
    font-size: 0.75rem;
}

/* 习惯卡片删除按钮样式 */
.goal-container .card[data-habit-index] .btn-danger {
    border: none;
    border-radius: 3px;
    font-size: 0.7rem !important;
    padding: 1px 5px !important;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    min-width: auto;
    line-height: 1.2;
    opacity: 0.8;
}

.goal-container .card[data-habit-index] .btn-danger:hover {
    transform: none;
    box-shadow: 0 2px 3px rgba(0,0,0,0.15);
    opacity: 1;
}

.goal-container .card[data-habit-index] .btn-danger i {
    font-size: 0.7rem;
}

/* 紧凑的习惯列表布局 */
.goal-container .row.g-1 {
    margin-bottom: 0 !important;
    row-gap: 0.25rem !important;
}

/* 打卡按钮样式 */
.goal-container .card[data-habit-index] .btn-outline-success {
    border-width: 1px;
    line-height: 1;
    padding: 2px 8px !important;
    font-size: 0.75rem;
    transition: all 0.2s ease;
    position: relative;
}

/* 习惯卡片完成时样式 */
.goal-container .card[data-habit-index]:has(.btn-outline-success:disabled) {
    border-color: #28a745;
    border-width: 1px;
    background-color: rgba(40, 167, 69, 0.05);
}

/* 打卡按钮点击动画效果 */
.habit-card-checked {
    animation: habitCheckedPulse 0.6s ease-out;
}

@keyframes habitCheckedPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* 习惯完成后的样式 */
.goal-container .card.habit-completed {
    display: none;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

/* 进度条动画效果 */
.goal-container .card[data-habit-index] .progress-bar {
    transition: width 0.4s ease-out;
}

/* 增强习惯卡片卡片标题 */
.goal-container .card[data-habit-index] .card-title {
    font-size: 0.9rem;
    padding: 0 1.5rem;
    width: 100%;
    text-align: center;
} 