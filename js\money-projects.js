// 获取北京时间
function getBeiJingTime() {
    const now = new Date();
    const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
    const beijing = new Date(utc + (8 * 3600000));
    return beijing.toISOString();
}

// 赚钱项目记录管理系统
const MoneyProjects = {
    // 存储键名 - 合并到主系统
    STORAGE_KEY: 'savingsData',

    // 数据结构
    data: {
        categories: [], // 分类数组
        projects: [], // 项目数组
        settings: {
            defaultCategories: [] // 改为空数组，全部自定义
        }
    },
    
    // 当前编辑的项目ID
    editingProjectId: null,

    // 选中的项目ID数组
    selectedProjects: [],

    // 格式化收益显示
    formatRevenue(amount) {
        if (amount >= 10000) {
            // 超过1万用w表示，保留1位小数，如果小数为0则不显示
            const wValue = amount / 10000;
            return wValue % 1 === 0 ? `¥${Math.round(wValue)}w` : `¥${wValue.toFixed(1)}w`;
        } else if (amount >= 1000) {
            // 超过1千用k表示，保留1位小数，如果小数为0则不显示
            const kValue = amount / 1000;
            return kValue % 1 === 0 ? `¥${Math.round(kValue)}k` : `¥${kValue.toFixed(1)}k`;
        } else {
            return `¥${Math.round(amount)}`;
        }
    },

    // 计算100万收益需要的数量
    calculateMillionTarget() {
        if (this.data.projects.length === 0) {
            // 更新提示信息
            this.updateMillionTargetTooltip('暂无项目数据');
            return '-';
        }

        // 找到单价最高的项目
        let highestRevenueProject = null;
        let highestRevenue = 0;
        let totalRevenue = 0;
        let validProjectCount = 0;

        this.data.projects.forEach(project => {
            if (project.revenue > 0) {
                totalRevenue += project.revenue;
                validProjectCount++;

                if (project.revenue > highestRevenue) {
                    highestRevenue = project.revenue;
                    highestRevenueProject = project;
                }
            }
        });

        if (!highestRevenueProject || highestRevenue <= 0) {
            this.updateMillionTargetTooltip('暂无有效项目数据');
            return '-';
        }

        // 计算需要多少个该项目才能达到100万
        const targetAmount = 1000000; // 100万
        const requiredQuantity = Math.ceil(targetAmount / highestRevenue);

        // 计算平均单价信息用于提示
        const averageRevenue = totalRevenue / validProjectCount;
        const averageRequiredQuantity = Math.ceil(targetAmount / averageRevenue);

        // 更新提示信息
        const projectName = highestRevenueProject.name;
        const revenueText = this.formatRevenue(highestRevenue);
        const avgRevenueText = this.formatRevenue(averageRevenue);

        let tooltipText = `基于最高单价项目"${projectName}"(${revenueText})计算`;
        if (validProjectCount > 1) {
            tooltipText += `\n平均单价${avgRevenueText}需要${this.formatQuantity(averageRequiredQuantity)}`;
        }

        this.updateMillionTargetTooltip(tooltipText);

        // 格式化显示
        return this.formatQuantity(requiredQuantity);
    },

    // 格式化数量显示
    formatQuantity(quantity) {
        if (quantity >= 10000) {
            const wValue = quantity / 10000;
            return wValue % 1 === 0 ? `${Math.round(wValue)}w个` : `${wValue.toFixed(1)}w个`;
        } else if (quantity >= 1000) {
            const kValue = quantity / 1000;
            return kValue % 1 === 0 ? `${Math.round(kValue)}k个` : `${kValue.toFixed(1)}k个`;
        } else {
            return `${quantity}个`;
        }
    },

    // 更新百万目标提示信息
    updateMillionTargetTooltip(message) {
        const millionTargetElement = document.getElementById('millionTarget');
        if (millionTargetElement) {
            millionTargetElement.setAttribute('title', message);
        }
    },

    // 计算单个项目的100万收益需要的数量
    calculateProjectMillionTarget(project) {
        if (!project || !project.revenue || project.revenue <= 0) {
            return '-';
        }

        const targetAmount = 1000000; // 100万
        const requiredQuantity = Math.ceil(targetAmount / project.revenue);

        return this.formatQuantity(requiredQuantity);
    },

    // 初始化
    init() {
        console.log('初始化赚钱项目记录系统...');
        this.loadData();
        this.bindEvents();
        this.updateDisplay();
        this.updateStatistics();
        this.updateCategorySelects();
    },

    // 加载数据 - 从主系统数据中加载
    loadData() {
        try {
            const savedData = localStorage.getItem(this.STORAGE_KEY);
            if (savedData) {
                const mainData = JSON.parse(savedData);
                // 合并赚钱项目数据到主系统数据结构中
                this.data.categories = mainData.moneyProjectCategories || [];
                this.data.projects = mainData.moneyProjects || [];
                console.log('赚钱项目数据加载成功');
            } else {
                // 首次使用，初始化空分类
                this.data.categories = [];
            }
        } catch (error) {
            console.error('加载赚钱项目数据失败:', error);
            this.data.categories = [];
        }
    },
    
    // 保存数据 - 合并到主系统数据中
    saveData() {
        try {
            // 获取主系统数据
            const savedData = localStorage.getItem(this.STORAGE_KEY);
            let mainData = savedData ? JSON.parse(savedData) : {};

            // 更新赚钱项目相关数据
            mainData.moneyProjectCategories = this.data.categories;
            mainData.moneyProjects = this.data.projects;

            // 保存回主系统
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(mainData));
            console.log('赚钱项目数据保存成功');
        } catch (error) {
            console.error('保存赚钱项目数据失败:', error);
        }
    },

    // 绑定事件
    bindEvents() {
        // 添加分类按钮
        document.getElementById('addCategoryBtn').addEventListener('click', () => {
            this.addCategory();
        });

        // 分类输入框回车事件
        document.getElementById('newCategoryName').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.addCategory();
            }
        });

        // 添加项目表单
        document.getElementById('projectForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addProject();
        });

        // 编辑项目表单
        document.getElementById('saveProjectBtn').addEventListener('click', () => {
            this.saveEditProject();
        });

        // 筛选和排序事件
        document.getElementById('filterCategory').addEventListener('change', () => {
            this.updateDisplay();
        });

        document.getElementById('filterStatus').addEventListener('change', () => {
            this.updateDisplay();
        });

        // 搜索框事件
        document.getElementById('searchInput').addEventListener('input', () => {
            this.updateDisplay();
        });

        // 清除搜索按钮事件
        document.getElementById('clearSearchBtn').addEventListener('click', () => {
            document.getElementById('searchInput').value = '';
            this.updateDisplay();
        });

        // 状态变化时显示/隐藏暂停原因
        document.getElementById('projectStatus').addEventListener('change', (e) => {
            this.togglePauseReason('pauseReasonContainer', e.target.value === 'paused');
        });

        document.getElementById('editProjectStatus').addEventListener('change', (e) => {
            this.togglePauseReason('editPauseReasonContainer', e.target.value === 'paused');
        });

        // 批量操作事件
        document.getElementById('selectAllCheckbox').addEventListener('change', () => {
            this.toggleSelectAll();
        });

        document.getElementById('clearSelectionBtn').addEventListener('click', () => {
            this.clearSelection();
        });

        document.getElementById('batchCompleteBtn').addEventListener('click', () => {
            this.batchUpdateStatus('completed');
        });

        document.getElementById('batchPauseBtn').addEventListener('click', () => {
            this.batchPause();
        });

        document.getElementById('batchRestartBtn').addEventListener('click', () => {
            this.batchRestart();
        });

        document.getElementById('batchDeleteBtn').addEventListener('click', () => {
            this.batchDelete();
        });

        // 全局点击事件 - 点击其他地方时恢复总目标收益模糊状态
        document.addEventListener('click', (e) => {
            // 如果点击的不是统计信息中的目标收益元素，则恢复模糊状态
            const targetRevenueElement = document.getElementById('targetRevenue');
            if (targetRevenueElement && e.target.id !== 'targetRevenue') {
                if (!targetRevenueElement.classList.contains('blurred')) {
                    targetRevenueElement.classList.add('blurred');
                    targetRevenueElement.title = '点击查看目标收益';
                }
            }
        });
    },

    // ===== 分类管理方法 =====

    // 添加分类
    addCategory() {
        const nameInput = document.getElementById('newCategoryName');
        if (!nameInput) return;

        const name = nameInput.value.trim();
        if (!name) {
            alert('请输入分类名称');
            return;
        }

        // 检查是否已存在相同的分类
        if (this.data.categories.includes(name)) {
            alert('该分类已存在');
            return;
        }

        // 添加到分类列表
        this.data.categories.push(name);

        // 保存数据
        this.saveData();

        // 清空输入框
        nameInput.value = '';

        // 更新显示
        this.updateCategoriesDisplay();
        this.updateCategorySelects();
    },

    // 删除分类
    deleteCategory(categoryName) {
        if (confirm('确定要删除这个分类吗？删除后该分类下的项目将变为无分类状态。')) {
            // 从分类列表中移除
            this.data.categories = this.data.categories.filter(cat => cat !== categoryName);
            
            // 更新项目中的分类引用
            this.data.projects.forEach(project => {
                if (project.category === categoryName) {
                    project.category = '';
                }
            });

            this.saveData();
            this.updateCategoriesDisplay();
            this.updateCategorySelects();
            this.updateDisplay();
        }
    },

    // 更新分类显示
    updateCategoriesDisplay() {
        const container = document.getElementById('categoriesContainer');
        if (!container) return;

        if (this.data.categories.length === 0) {
            container.innerHTML = '';
            container.style.display = 'none';
            return;
        }

        container.style.display = 'block';
        container.innerHTML = this.data.categories.map(categoryName => `
            <div class="category-item">
                <span class="category-name" ondblclick="MoneyProjects.editCategoryName('${categoryName}')" title="双击修改分类名称">${categoryName}</span>
                <button class="btn btn-sm btn-outline-danger delete-category-btn" onclick="MoneyProjects.deleteCategory('${categoryName}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `).join('');
    },

    // 更新分类选择框
    updateCategorySelects() {
        const selects = ['projectCategory', 'editProjectCategory', 'filterCategory'];
        
        selects.forEach(selectId => {
            const select = document.getElementById(selectId);
            if (!select) return;

            // 保存当前选中值
            const currentValue = select.value;
            
            // 清空选项（保留第一个默认选项）
            const firstOption = select.querySelector('option');
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }

            // 添加分类选项
            this.data.categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                select.appendChild(option);
            });

            // 恢复选中值
            if (currentValue && this.data.categories.includes(currentValue)) {
                select.value = currentValue;
            }
        });
    },

    // ===== 项目管理方法 =====

    // 添加项目
    addProject() {
        const name = document.getElementById('projectName').value.trim();
        const category = document.getElementById('projectCategory').value;
        const revenue = parseFloat(document.getElementById('projectRevenue').value) || 0;
        const targetQuantity = parseInt(document.getElementById('projectTargetQuantity').value) || 1;
        const currentQuantity = parseInt(document.getElementById('projectCurrentQuantity').value) || 0;
        const priority = parseInt(document.getElementById('projectPriority').value) || 3;
        const deadline = document.getElementById('projectDeadline').value;
        const note = document.getElementById('projectNote').value.trim();
        let status = document.getElementById('projectStatus').value;
        const pauseReason = document.getElementById('projectPauseReason').value.trim();

        if (!name) {
            alert('请输入项目名称');
            return;
        }

        // 自动状态更新：当前数量达到目标数量时自动标记为已完成
        if (currentQuantity >= targetQuantity && targetQuantity > 0) {
            status = 'completed';
        }

        // 创建项目对象
        const project = {
            id: Date.now().toString(),
            name: name,
            category: category,
            revenue: revenue,
            targetQuantity: targetQuantity,
            currentQuantity: currentQuantity,
            priority: priority,
            deadline: deadline,
            note: note,
            status: status,
            pauseReason: status === 'paused' ? pauseReason : '',
            createdAt: getBeiJingTime(),
            updatedAt: getBeiJingTime()
        };

        // 添加到数据中
        this.data.projects.unshift(project);

        // 保存数据
        this.saveData();

        // 清空表单
        this.clearProjectForm();

        // 更新显示
        this.updateDisplay();
        this.updateStatistics();
    },

    // 编辑项目
    editProject(projectId) {
        const project = this.data.projects.find(p => p.id === projectId);
        if (!project) return;

        this.editingProjectId = projectId;

        // 填充编辑表单
        document.getElementById('editProjectId').value = project.id;
        document.getElementById('editProjectName').value = project.name;
        document.getElementById('editProjectCategory').value = project.category;
        document.getElementById('editProjectRevenue').value = project.revenue;
        document.getElementById('editProjectTargetQuantity').value = project.targetQuantity || project.quantity || 1;
        document.getElementById('editProjectCurrentQuantity').value = project.currentQuantity || 0;
        document.getElementById('editProjectPriority').value = project.priority || 3;
        document.getElementById('editProjectDeadline').value = project.deadline || '';
        document.getElementById('editProjectNote').value = project.note;
        document.getElementById('editProjectStatus').value = project.status;
        document.getElementById('editProjectPauseReason').value = project.pauseReason || '';

        // 显示/隐藏暂停原因
        this.togglePauseReason('editPauseReasonContainer', project.status === 'paused');

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('editProjectModal'));
        modal.show();
    },

    // 保存编辑的项目
    saveEditProject() {
        if (!this.editingProjectId) return;

        const projectIndex = this.data.projects.findIndex(p => p.id === this.editingProjectId);
        if (projectIndex === -1) return;

        const name = document.getElementById('editProjectName').value.trim();
        const category = document.getElementById('editProjectCategory').value;
        const revenue = parseFloat(document.getElementById('editProjectRevenue').value) || 0;
        const targetQuantity = parseInt(document.getElementById('editProjectTargetQuantity').value) || 1;
        const currentQuantity = parseInt(document.getElementById('editProjectCurrentQuantity').value) || 0;
        const priority = parseInt(document.getElementById('editProjectPriority').value) || 3;
        const deadline = document.getElementById('editProjectDeadline').value;
        const note = document.getElementById('editProjectNote').value.trim();
        let status = document.getElementById('editProjectStatus').value;
        const pauseReason = document.getElementById('editProjectPauseReason').value.trim();

        if (!name) {
            alert('请输入项目名称');
            return;
        }

        // 自动状态更新：当前数量达到目标数量时自动标记为已完成
        if (currentQuantity >= targetQuantity && targetQuantity > 0 && status !== 'completed') {
            status = 'completed';
        }

        // 更新项目数据
        this.data.projects[projectIndex] = {
            ...this.data.projects[projectIndex],
            name: name,
            category: category,
            revenue: revenue,
            targetQuantity: targetQuantity,
            currentQuantity: currentQuantity,
            priority: priority,
            deadline: deadline,
            note: note,
            status: status,
            pauseReason: status === 'paused' ? pauseReason : '',
            updatedAt: getBeiJingTime()
        };

        // 保存数据
        this.saveData();

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('editProjectModal'));
        modal.hide();

        // 清除编辑状态
        this.editingProjectId = null;

        // 更新显示
        this.updateDisplay();
        this.updateStatistics();
    },

    // 删除项目
    deleteProject(projectId) {
        if (confirm('确定要删除这个项目吗？')) {
            this.data.projects = this.data.projects.filter(p => p.id !== projectId);
            this.saveData();
            this.updateDisplay();
            this.updateStatistics();
        }
    },

    // 清空项目表单
    clearProjectForm() {
        document.getElementById('projectName').value = '';
        document.getElementById('projectCategory').value = '';
        document.getElementById('projectRevenue').value = '';
        document.getElementById('projectTargetQuantity').value = '';
        document.getElementById('projectCurrentQuantity').value = '';
        document.getElementById('projectPriority').value = '3';
        document.getElementById('projectDeadline').value = '';
        document.getElementById('projectNote').value = '';
        document.getElementById('projectStatus').value = 'planning';
        document.getElementById('projectPauseReason').value = '';
        this.togglePauseReason('pauseReasonContainer', false);
    },

    // ===== 显示和统计方法 =====

    // 更新项目显示
    updateDisplay() {
        const container = document.getElementById('projectsContainer');
        if (!container) return;

        let filteredProjects = this.getFilteredProjects();

        if (filteredProjects.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-5">
                    <i class="fas fa-folder-open fa-3x mb-3"></i>
                    <p>暂无符合条件的项目</p>
                </div>
            `;
            return;
        }

        container.innerHTML = `<div class="row">${filteredProjects.map(project => this.renderProjectCard(project)).join('')}</div>`;
    },

    // 获取筛选后的项目
    getFilteredProjects() {
        let projects = [...this.data.projects];

        // 按分类筛选
        const filterCategory = document.getElementById('filterCategory').value;
        if (filterCategory) {
            projects = projects.filter(p => p.category === filterCategory);
        }

        // 按状态筛选
        const filterStatus = document.getElementById('filterStatus').value;
        if (filterStatus) {
            projects = projects.filter(p => p.status === filterStatus);
        }

        // 按搜索关键词筛选
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            const searchTerm = searchInput.value.trim().toLowerCase();
            if (searchTerm) {
                projects = projects.filter(p => {
                    const name = (p.name || '').toLowerCase();
                    const note = (p.note || '').toLowerCase();
                    const category = (p.category || '').toLowerCase();
                    return name.includes(searchTerm) ||
                           note.includes(searchTerm) ||
                           category.includes(searchTerm);
                });
            }
        }

        // 排序 - 固定排序：优先级 > 目标收益
        projects.sort((a, b) => {
            // 首先按优先级排序（5星 > 4星 > 3星 > 2星 > 1星）
            const aPriority = a.priority || 3;
            const bPriority = b.priority || 3;
            if (bPriority !== aPriority) {
                return bPriority - aPriority;
            }

            // 优先级相同时，按目标收益排序（高到低）
            const aTargetQuantity = a.targetQuantity || a.quantity || 1;
            const bTargetQuantity = b.targetQuantity || b.quantity || 1;
            return (b.revenue * bTargetQuantity) - (a.revenue * aTargetQuantity);
        });

        return projects;
    },

    // 渲染项目卡片
    renderProjectCard(project) {
        const statusText = {
            'planning': '计划中',
            'in-progress': '进行中',
            'completed': '已完成',
            'paused': '暂停'
        };

        const statusClass = {
            'planning': 'secondary',
            'in-progress': 'warning',
            'completed': 'success',
            'paused': 'danger'
        };

        // 兼容旧数据结构
        const targetQuantity = project.targetQuantity || project.quantity || 1;
        const currentQuantity = project.currentQuantity || 0;
        const priority = project.priority || 3;
        const currentRevenue = project.revenue * currentQuantity;
        const targetRevenue = project.revenue * targetQuantity;
        const createdDate = new Date(project.createdAt).toLocaleDateString();

        // 计算100万收益需要的数量
        const millionTargetQuantity = this.calculateProjectMillionTarget(project);

        // 生成星级显示
        const priorityStars = '⭐'.repeat(priority);

        // 计算剩余天数
        const remainingDays = this.calculateRemainingDays(project.deadline);
        const remainingDaysHtml = this.formatRemainingDays(remainingDays);

        // 检查是否选中
        const isSelected = this.selectedProjects.includes(project.id);

        return `
            <div class="col-md-6 mb-3">
                <div class="card project-card-optimized ${isSelected ? 'selected' : ''}" data-project-id="${project.id}">
                    <!-- 卡片头部：选择框 + 项目名称 + 状态 -->
                    <div class="card-header-custom">
                        <div class="project-header">
                            <div class="project-title-section">
                                <input type="checkbox" class="project-checkbox" ${isSelected ? 'checked' : ''}
                                       onchange="MoneyProjects.toggleProjectSelection('${project.id}')">
                                <h6 class="project-title">${project.name}</h6>
                                <span class="priority-stars" title="优先级: ${priority}星">${priorityStars}</span>
                            </div>
                            <span class="badge status-badge bg-${statusClass[project.status]}">${statusText[project.status]}</span>
                        </div>
                        ${project.category ? `<div class="project-category"><i class="fas fa-tag"></i> ${project.category}</div>` : ''}
                    </div>

                    <!-- 卡片主体：核心数据 -->
                    <div class="card-body-custom">
                        <!-- 暂停原因提示 -->
                        ${project.status === 'paused' && project.pauseReason ? `
                            <div class="pause-reason-alert">
                                <i class="fas fa-pause"></i> ${project.pauseReason}
                            </div>
                        ` : ''}

                        <!-- 核心数据网格 -->
                        <div class="project-data-grid">
                            <div class="data-item">
                                <div class="data-label">单价</div>
                                <div class="data-value price-value">${this.formatRevenue(project.revenue)}</div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">进度</div>
                                <div class="data-value progress-value">
                                    <span class="current-quantity clickable-quantity"
                                          onclick="MoneyProjects.editCurrentQuantity('${project.id}')"
                                          title="点击修改当前数量">${currentQuantity}</span>
                                    <span class="quantity-separator">/</span>
                                    <span class="target-quantity">${targetQuantity}</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-label">百万目标</div>
                                <div class="data-value million-target-value" title="赚100万需要的数量">${millionTargetQuantity}</div>
                            </div>
                            <div class="data-item revenue-item">
                                <div class="data-label">收益</div>
                                <div class="data-value revenue-value">
                                    <div class="current-revenue">${this.formatRevenue(currentRevenue)}</div>
                                    <div class="target-revenue">目标 ${this.formatRevenue(targetRevenue)}</div>
                                </div>
                            </div>
                        </div>

                        <!-- 备注信息 -->
                        ${project.note ? `<div class="project-note">${project.note}</div>` : ''}
                    </div>

                    <!-- 卡片底部：时间信息 + 操作按钮 -->
                    <div class="card-footer-custom">
                        <div class="time-info-horizontal">
                            <div class="created-time">${createdDate}</div>
                            ${project.deadline ? `<div class="remaining-days">${remainingDaysHtml}</div>` : ''}
                        </div>
                        <div class="action-buttons">
                            ${project.status === 'paused' ? `
                                <button class="btn btn-outline-success btn-sm action-btn"
                                        onclick="MoneyProjects.restartProject('${project.id}')"
                                        title="重启项目">
                                    <i class="fas fa-play"></i>
                                </button>
                            ` : ''}
                            <button class="btn btn-outline-primary btn-sm action-btn"
                                    onclick="MoneyProjects.editProject('${project.id}')"
                                    title="编辑项目">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm action-btn"
                                    onclick="MoneyProjects.deleteProject('${project.id}')"
                                    title="删除项目">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    // 更新统计信息
    updateStatistics() {
        const totalProjects = this.data.projects.length;

        // 计算当前收益和目标收益
        let currentTotalRevenue = 0;
        let targetTotalRevenue = 0;

        this.data.projects.forEach(project => {
            const targetQuantity = project.targetQuantity || project.quantity || 1;
            const currentQuantity = project.currentQuantity || 0;

            currentTotalRevenue += project.revenue * currentQuantity;
            targetTotalRevenue += project.revenue * targetQuantity;
        });

        const inProgressProjects = this.data.projects.filter(p => p.status === 'in-progress').length;
        const completedProjects = this.data.projects.filter(p => p.status === 'completed').length;

        document.getElementById('totalProjects').textContent = totalProjects;
        document.getElementById('currentRevenue').textContent = this.formatRevenue(currentTotalRevenue);
        document.getElementById('targetRevenue').textContent = this.formatRevenue(targetTotalRevenue);
        document.getElementById('inProgressProjects').textContent = inProgressProjects;
        document.getElementById('completedProjects').textContent = completedProjects;

        // 更新分类显示
        this.updateCategoriesDisplay();
    },

    // 格式化时间
    formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    },

    // 编辑当前数量
    editCurrentQuantity(projectId) {
        const project = this.data.projects.find(p => p.id === projectId);
        if (!project) return;

        const currentQuantity = project.currentQuantity || 0;
        const targetQuantity = project.targetQuantity || project.quantity || 1;

        const newQuantity = prompt(`请输入当前数量 (目标数量: ${targetQuantity})`, currentQuantity);

        if (newQuantity === null) return; // 用户取消

        const parsedQuantity = parseInt(newQuantity);
        if (isNaN(parsedQuantity) || parsedQuantity < 0) {
            alert('请输入有效的数量（不能小于0）');
            return;
        }

        if (parsedQuantity > targetQuantity) {
            if (!confirm(`当前数量 (${parsedQuantity}) 超过了目标数量 (${targetQuantity})，确定要设置吗？`)) {
                return;
            }
        }

        // 更新项目数据
        const projectIndex = this.data.projects.findIndex(p => p.id === projectId);
        if (projectIndex !== -1) {
            const project = this.data.projects[projectIndex];
            project.currentQuantity = parsedQuantity;
            project.updatedAt = getBeiJingTime();

            // 自动状态更新：当前数量达到目标数量时自动标记为已完成
            const targetQuantity = project.targetQuantity || project.quantity || 1;
            if (parsedQuantity >= targetQuantity && targetQuantity > 0 && project.status !== 'completed') {
                project.status = 'completed';
                project.pauseReason = ''; // 清除暂停原因
            }

            // 保存数据
            this.saveData();

            // 更新显示
            this.updateDisplay();
            this.updateStatistics();
        }
    },

    // 重启单个项目
    restartProject(projectId) {
        const projectIndex = this.data.projects.findIndex(p => p.id === projectId);
        if (projectIndex !== -1) {
            const project = this.data.projects[projectIndex];
            if (project.status === 'paused') {
                project.status = 'in-progress';
                project.pauseReason = '';
                project.updatedAt = getBeiJingTime();

                this.saveData();
                this.updateDisplay();
                this.updateStatistics();
            }
        }
    },

    // 编辑分类名称
    editCategoryName(oldCategoryName) {
        const newCategoryName = prompt('请输入新的分类名称:', oldCategoryName);

        if (newCategoryName === null) return; // 用户取消

        const trimmedName = newCategoryName.trim();
        if (!trimmedName) {
            alert('分类名称不能为空');
            return;
        }

        if (trimmedName === oldCategoryName) return; // 名称没有变化

        // 检查新名称是否已存在
        if (this.data.categories.includes(trimmedName)) {
            alert('该分类名称已存在');
            return;
        }

        // 更新分类列表中的名称
        const categoryIndex = this.data.categories.findIndex(cat => cat === oldCategoryName);
        if (categoryIndex !== -1) {
            this.data.categories[categoryIndex] = trimmedName;
        }

        // 更新所有项目中的分类引用
        this.data.projects.forEach(project => {
            if (project.category === oldCategoryName) {
                project.category = trimmedName;
                project.updatedAt = getBeiJingTime();
            }
        });

        // 保存数据
        this.saveData();

        // 更新显示
        this.updateCategoriesDisplay();
        this.updateCategorySelects();
        this.updateDisplay();
    },

    // 显示/隐藏暂停原因输入框
    togglePauseReason(containerId, show) {
        const container = document.getElementById(containerId);
        if (container) {
            container.style.display = show ? 'block' : 'none';
        }
    },

    // 计算剩余天数
    calculateRemainingDays(deadline) {
        if (!deadline) return null;

        const today = new Date(getBeiJingTime());
        const deadlineDate = new Date(deadline);
        const diffTime = deadlineDate - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        return diffDays;
    },

    // 格式化剩余天数显示
    formatRemainingDays(days) {
        if (days === null) return '';
        if (days < 0) return `<span class="text-danger">逾期${Math.abs(days)}天</span>`;
        if (days === 0) return `<span class="text-warning">今天截止</span>`;
        if (days <= 3) return `<span class="text-warning">剩余${days}天</span>`;
        return `<span class="text-muted">剩余${days}天</span>`;
    },

    // 批量选择相关方法
    toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const isChecked = selectAllCheckbox.checked;

        if (isChecked) {
            // 选择所有当前显示的项目
            const filteredProjects = this.getFilteredProjects();
            this.selectedProjects = filteredProjects.map(p => p.id);
        } else {
            // 取消所有选择
            this.selectedProjects = [];
        }

        this.updateBatchOperationBar();
        this.updateDisplay();
    },

    clearSelection() {
        this.selectedProjects = [];
        document.getElementById('selectAllCheckbox').checked = false;
        this.updateBatchOperationBar();
        this.updateDisplay();
    },

    toggleProjectSelection(projectId) {
        const index = this.selectedProjects.indexOf(projectId);
        if (index > -1) {
            this.selectedProjects.splice(index, 1);
        } else {
            this.selectedProjects.push(projectId);
        }

        this.updateBatchOperationBar();
        this.updateSelectAllCheckbox();
    },

    updateSelectAllCheckbox() {
        const filteredProjects = this.getFilteredProjects();
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');

        if (filteredProjects.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else {
            const selectedInFiltered = filteredProjects.filter(p => this.selectedProjects.includes(p.id)).length;

            if (selectedInFiltered === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (selectedInFiltered === filteredProjects.length) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }
    },

    updateBatchOperationBar() {
        const batchBar = document.getElementById('batchOperationBar');
        const selectedCount = document.getElementById('selectedCount');

        if (this.selectedProjects.length > 0) {
            batchBar.style.display = 'block';
            selectedCount.textContent = this.selectedProjects.length;
        } else {
            batchBar.style.display = 'none';
        }
    },

    // 批量操作方法
    batchUpdateStatus(newStatus) {
        if (this.selectedProjects.length === 0) return;

        this.selectedProjects.forEach(projectId => {
            const projectIndex = this.data.projects.findIndex(p => p.id === projectId);
            if (projectIndex !== -1) {
                this.data.projects[projectIndex].status = newStatus;
                this.data.projects[projectIndex].updatedAt = getBeiJingTime();

                // 如果标记为完成，清除暂停原因
                if (newStatus === 'completed') {
                    this.data.projects[projectIndex].pauseReason = '';
                }
            }
        });

        this.saveData();
        this.clearSelection();
        this.updateDisplay();
        this.updateStatistics();
    },

    batchPause() {
        if (this.selectedProjects.length === 0) return;

        const reason = prompt('请输入暂停原因（可选）:');
        if (reason === null) return; // 用户取消

        this.selectedProjects.forEach(projectId => {
            const projectIndex = this.data.projects.findIndex(p => p.id === projectId);
            if (projectIndex !== -1) {
                this.data.projects[projectIndex].status = 'paused';
                this.data.projects[projectIndex].pauseReason = reason.trim();
                this.data.projects[projectIndex].updatedAt = getBeiJingTime();
            }
        });

        this.saveData();
        this.clearSelection();
        this.updateDisplay();
        this.updateStatistics();
    },

    batchRestart() {
        if (this.selectedProjects.length === 0) return;

        if (!confirm(`确定要重启选中的 ${this.selectedProjects.length} 个项目吗？`)) return;

        this.selectedProjects.forEach(projectId => {
            const projectIndex = this.data.projects.findIndex(p => p.id === projectId);
            if (projectIndex !== -1) {
                const project = this.data.projects[projectIndex];
                // 只重启暂停的项目
                if (project.status === 'paused') {
                    this.data.projects[projectIndex].status = 'in-progress';
                    this.data.projects[projectIndex].pauseReason = '';
                    this.data.projects[projectIndex].updatedAt = getBeiJingTime();
                }
            }
        });

        this.saveData();
        this.clearSelection();
        this.updateDisplay();
        this.updateStatistics();
    },

    batchDelete() {
        if (this.selectedProjects.length === 0) return;

        if (!confirm(`确定要删除选中的 ${this.selectedProjects.length} 个项目吗？此操作不可撤销！`)) return;

        this.data.projects = this.data.projects.filter(p => !this.selectedProjects.includes(p.id));

        this.saveData();
        this.clearSelection();
        this.updateDisplay();
        this.updateStatistics();
    },

    // 切换统计信息中目标收益的模糊状态
    toggleStatisticsTargetRevenueBlur() {
        const targetRevenueElement = document.getElementById('targetRevenue');
        if (!targetRevenueElement) return;

        // 切换模糊状态
        targetRevenueElement.classList.toggle('blurred');

        // 更新提示文本
        if (targetRevenueElement.classList.contains('blurred')) {
            targetRevenueElement.title = '点击查看目标收益';
        } else {
            targetRevenueElement.title = '点击隐藏目标收益';
        }
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    MoneyProjects.init();
});

// 导出到全局作用域
window.MoneyProjects = MoneyProjects;
