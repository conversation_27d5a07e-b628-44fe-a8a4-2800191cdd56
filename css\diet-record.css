/* 饮食记录样式 - 限制在饮食记录页面内 */

/* 基础变量 - 仅在饮食记录页面生效 */
.diet-record-page {
    --diet-primary: #28a745;
    --diet-secondary: #6c757d;
    --diet-success: #20c997;
    --diet-warning: #ffc107;
    --diet-danger: #dc3545;
    --diet-info: #17a2b8;
    --diet-light: #f8f9fa;
    --diet-dark: #343a40;
    --diet-border: #dee2e6;
    --diet-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --diet-radius: 8px;
}

/* 页面整体样式 */
.diet-record-page {
    background-color: #f5f7fa;
}

/* 卡片样式增强 - 仅在饮食记录页面 */
.diet-record-page .card {
    border: none;
    border-radius: var(--diet-radius);
    box-shadow: var(--diet-shadow);
    transition: all 0.3s ease;
}

.diet-record-page .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.diet-record-page .card-header {
    background: linear-gradient(135deg, var(--diet-primary), var(--diet-success));
    color: white;
    border-bottom: none;
    font-weight: 600;
    border-radius: var(--diet-radius) var(--diet-radius) 0 0 !important;
}

.diet-record-page .card-header i {
    opacity: 0.9;
}

/* 表单样式 - 仅在饮食记录页面 */
.diet-record-page .form-control,
.diet-record-page .form-select {
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 0.6rem 0.75rem;
    transition: all 0.3s ease;
}

.diet-record-page .form-control:focus,
.diet-record-page .form-select:focus {
    border-color: var(--diet-primary);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.diet-record-page .form-label {
    font-weight: 500;
    color: var(--diet-dark);
    margin-bottom: 0.5rem;
}

/* 按钮样式 - 仅在饮食记录页面 */
.diet-record-page .btn-primary {
    background: linear-gradient(135deg, var(--diet-primary), var(--diet-success));
    border: none;
    border-radius: 6px;
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.diet-record-page .btn-primary:hover {
    background: linear-gradient(135deg, #218838, #1e7e34);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.diet-record-page .btn-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    border: none;
    border-radius: 6px;
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    transition: all 0.3s ease;
    color: #212529;
}

.diet-record-page .btn-warning:hover {
    background: linear-gradient(135deg, #e0a800, #dc6502);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
    color: #212529;
}

.diet-record-page .btn-outline-primary {
    border: 2px solid var(--diet-primary);
    color: var(--diet-primary);
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.diet-record-page .btn-outline-primary:hover {
    background-color: var(--diet-primary);
    border-color: var(--diet-primary);
    transform: translateY(-1px);
}

.diet-record-page .btn-outline-secondary {
    border: 2px solid var(--diet-secondary);
    color: var(--diet-secondary);
    border-radius: 6px;
    transition: all 0.3s ease;
}

/* 视图切换按钮特殊样式 */
.diet-record-page .btn-group .btn-outline-primary {
    border: 2px solid #6f42c1;
    color: #6f42c1;
    background-color: white;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.diet-record-page .btn-group .btn-outline-primary:hover {
    background-color: #6f42c1;
    border-color: #6f42c1;
    color: white;
    transform: translateY(-1px);
}

.diet-record-page .btn-group .btn-outline-primary.active,
.diet-record-page .btn-group .btn-check:checked + .btn-outline-primary {
    background-color: #6f42c1;
    border-color: #6f42c1;
    color: white;
    box-shadow: 0 2px 8px rgba(111, 66, 193, 0.3);
}



/* 饮食记录项样式 - 仅在饮食记录页面 */
.diet-record-page .diet-record-item {
    background: white;
    border: 1px solid var(--diet-border);
    border-radius: var(--diet-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.diet-record-page .diet-record-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(to bottom, var(--diet-primary), var(--diet-success));
}

.diet-record-page .diet-record-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.diet-record-page .diet-record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #f0f0f0;
}

.diet-record-page .diet-date {
    font-weight: 600;
    color: var(--diet-dark);
    font-size: 1.1rem;
}

.diet-record-page .meal-time-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.diet-record-page .meal-time-badge.morning {
    background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
    color: #d63031;
}

.diet-record-page .meal-time-badge.afternoon {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
}

.diet-record-page .meal-time-badge.evening {
    background: linear-gradient(135deg, #a29bfe, #6c5ce7);
    color: white;
}

.diet-record-page .diet-content {
    margin-bottom: 0.5rem;
}

.diet-record-page .food-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.diet-record-page .food-item:last-child {
    border-bottom: none;
}

.diet-record-page .food-name {
    font-weight: 500;
    color: var(--diet-dark);
    flex: 1;
}

.diet-record-page .food-amount {
    color: var(--diet-secondary);
    font-size: 0.9rem;
    margin-left: 1rem;
}

.diet-record-page .diet-note {
    background: #f8f9fa;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
    color: var(--diet-secondary);
    font-style: italic;
    margin-top: 0.5rem;
}

.diet-record-page .diet-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.75rem;
    padding-top: 0.5rem;
    border-top: 1px solid #f0f0f0;
}

.diet-record-page .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    border-radius: 4px;
}

/* 空状态样式 - 仅在饮食记录页面 */
.diet-record-page .empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--diet-secondary);
}

.diet-record-page .empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
    color: var(--diet-primary);
}

.diet-record-page .empty-state h5 {
    margin-bottom: 0.5rem;
    color: var(--diet-dark);
}

.diet-record-page .empty-state p {
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

/* 日期分组样式 - 仅在饮食记录页面 */
.diet-record-page .date-group {
    margin-bottom: 2rem;
}

.diet-record-page .date-group-header {
    background: linear-gradient(135deg, var(--diet-light), #e9ecef);
    padding: 0.75rem 1rem;
    border-radius: var(--diet-radius);
    margin-bottom: 1rem;
    font-weight: 600;
    color: var(--diet-dark);
    border-left: 4px solid var(--diet-primary);
}

/* 时间段分组样式 */
.diet-record-page .meal-group {
    margin-bottom: 1.5rem;
    background: white;
    border-radius: var(--diet-radius);
    box-shadow: var(--diet-shadow);
    overflow: hidden;
}

.diet-record-page .meal-group-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 0.75rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dee2e6;
}

.diet-record-page .meal-count {
    font-size: 0.85rem;
    color: var(--diet-secondary);
    background: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    border: 1px solid #dee2e6;
}

.diet-record-page .meal-records {
    padding: 0.5rem;
}

/* 简化的记录项样式 */
.diet-record-page .simple-record-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
}

.diet-record-page .simple-record-item:hover {
    background: #e9ecef;
    transform: translateX(3px);
}

.diet-record-page .simple-record-item:last-child {
    margin-bottom: 0;
}

.diet-record-page .record-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.diet-record-page .food-info {
    flex: 1;
    font-size: 0.95rem;
    color: var(--diet-dark);
}

.diet-record-page .record-time {
    font-size: 0.8rem;
    color: var(--diet-secondary);
    background: white;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.diet-record-page .record-note {
    background: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
    color: var(--diet-secondary);
    font-style: italic;
    margin-bottom: 0.5rem;
    border: 1px solid #dee2e6;
}

.diet-record-page .record-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

/* 响应式设计 - 仅在饮食记录页面 */
@media (max-width: 768px) {
    .diet-record-page .diet-record-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .diet-record-page .food-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .diet-record-page .food-amount {
        margin-left: 0;
    }

    .diet-record-page .diet-actions {
        justify-content: center;
    }

    .diet-record-page .record-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .diet-record-page .record-actions {
        justify-content: center;
        width: 100%;
    }

    .diet-record-page .meal-group-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

/* 动画效果已删除 - 避免添加记录时的跳动 */

/* 加载状态 - 仅在饮食记录页面 */
.diet-record-page .loading {
    text-align: center;
    padding: 2rem;
    color: var(--diet-secondary);
}

.diet-record-page .loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 食品库卡片样式 - 仅在饮食记录页面 */
.diet-record-page .food-add-section {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.75rem;
    margin-bottom: 0.75rem;
}

.diet-record-page .food-cards-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    height: calc(100vh - 520px);
    min-height: 300px;
    overflow-y: auto;
    padding: 0.5rem;
    align-content: flex-start;
}

.diet-record-page .food-card {
    background: linear-gradient(135deg, var(--diet-primary), var(--diet-success));
    border: 1px solid var(--diet-primary);
    border-radius: 15px;
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
    font-weight: 500;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    white-space: nowrap;
    width: auto;
    height: auto;
    flex: none;
}

.diet-record-page .food-card:hover {
    background: linear-gradient(135deg, #218838, #1e7e34);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.diet-record-page .food-card .delete-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.7rem;
    padding: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    opacity: 0.8;
    flex-shrink: 0;
}

.diet-record-page .food-card .delete-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    opacity: 1;
}

.diet-record-page .food-library-empty {
    text-align: center;
    color: var(--diet-secondary);
    font-size: 0.85rem;
    padding: 2rem 1rem;
    font-style: italic;
}

/* 响应式设计 - 食品库卡片 */
@media (max-width: 768px) {
    .diet-record-page .food-cards-container {
        height: calc(100vh - 480px);
        min-height: 250px;
    }

    .diet-record-page .food-card {
        font-size: 0.75rem;
        padding: 0.35rem 0.5rem;
    }
}

@media (max-width: 480px) {
    .diet-record-page .food-cards-container {
        height: calc(100vh - 450px);
        min-height: 200px;
        gap: 0.4rem;
    }

    .diet-record-page .food-card {
        font-size: 0.7rem;
        padding: 0.3rem 0.4rem;
    }
}
