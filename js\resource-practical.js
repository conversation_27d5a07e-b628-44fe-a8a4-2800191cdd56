// 简洁实用型资源争夺管理系统
const ResourcePractical = {
    resources: [],
    reflections: [],

    init() {
        this.loadData();
        this.bindEvents();
        this.renderResources();
        this.updateStats();
        this.currentView = 'resources'; // 当前视图状态
    },

    loadData() {
        this.resources = JSON.parse(localStorage.getItem('resourcePractical_resources') || '[]');
        this.reflections = JSON.parse(localStorage.getItem('resourcePractical_reflections') || '[]');
    },

    saveData() {
        localStorage.setItem('resourcePractical_resources', JSON.stringify(this.resources));
        localStorage.setItem('resourcePractical_reflections', JSON.stringify(this.reflections));
    },

    bindEvents() {
        // 资源表单提交
        document.getElementById('resourceForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addResource();
        });

        // 筛选器事件
        document.getElementById('filterStatus').addEventListener('change', () => this.applyFilters());
        document.getElementById('filterType').addEventListener('change', () => this.applyFilters());
        document.getElementById('filterPriority').addEventListener('change', () => this.applyFilters());

        // 为所有编号文本框绑定事件
        this.bindNumberedTextareas();
    },

    bindNumberedTextareas() {
        const textareas = document.querySelectorAll('.numbered-textarea');
        textareas.forEach(textarea => {
            // 初始化时添加第一个编号
            if (textarea.value.trim() === '') {
                textarea.value = '1. ';
            }

            // 监听键盘事件
            textarea.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.handleEnterKey(textarea);
                } else if (e.key === 'Backspace') {
                    this.handleBackspace(textarea, e);
                }
            });

            // 监听输入事件，确保始终有编号
            textarea.addEventListener('input', (e) => {
                this.ensureNumbering(textarea);
            });

            // 监听焦点事件
            textarea.addEventListener('focus', (e) => {
                if (textarea.value.trim() === '') {
                    textarea.value = '1. ';
                    // 将光标移到末尾
                    setTimeout(() => {
                        textarea.setSelectionRange(textarea.value.length, textarea.value.length);
                    }, 0);
                }
            });
        });
    },

    handleEnterKey(textarea) {
        const cursorPos = textarea.selectionStart;
        const value = textarea.value;
        const beforeCursor = value.substring(0, cursorPos);
        const afterCursor = value.substring(cursorPos);

        // 获取当前行
        const lines = beforeCursor.split('\n');
        const currentLine = lines[lines.length - 1];

        // 检查当前行是否有编号
        const numberMatch = currentLine.match(/^(\d+)\.\s*/);
        if (numberMatch) {
            const currentNumber = parseInt(numberMatch[1]);
            const nextNumber = currentNumber + 1;
            const newLine = `\n${nextNumber}. `;

            // 插入新行和编号
            textarea.value = beforeCursor + newLine + afterCursor;

            // 设置光标位置到新编号后面
            const newCursorPos = cursorPos + newLine.length;
            textarea.setSelectionRange(newCursorPos, newCursorPos);
        } else {
            // 如果当前行没有编号，添加普通换行
            textarea.value = beforeCursor + '\n' + afterCursor;
            textarea.setSelectionRange(cursorPos + 1, cursorPos + 1);
        }
    },

    handleBackspace(textarea, e) {
        const cursorPos = textarea.selectionStart;
        const value = textarea.value;

        if (cursorPos > 0) {
            const beforeCursor = value.substring(0, cursorPos);
            const lines = beforeCursor.split('\n');
            const currentLine = lines[lines.length - 1];

            // 如果光标在编号后面（如 "1. |"），防止删除编号
            const numberMatch = currentLine.match(/^(\d+)\.\s*$/);
            if (numberMatch && cursorPos === beforeCursor.length) {
                e.preventDefault();
                return;
            }
        }
    },

    ensureNumbering(textarea) {
        const value = textarea.value;
        if (value.trim() === '') {
            textarea.value = '1. ';
            textarea.setSelectionRange(3, 3);
        }
    },

    addResource() {
        const resource = {
            id: Date.now(),
            type: document.getElementById('resourceType').value,
            name: document.getElementById('resourceName').value,
            priority: document.getElementById('resourcePriority').value,
            status: document.getElementById('resourceStatus').value,
            deadline: document.getElementById('resourceDeadline').value,
            note: document.getElementById('resourceNote').value,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        if (!resource.name.trim()) {
            alert('请输入资源描述');
            return;
        }

        this.resources.unshift(resource);
        this.saveData();
        this.renderResources();
        this.updateStats();
        this.clearForm();
    },

    clearForm() {
        document.getElementById('resourceForm').reset();
        document.getElementById('resourcePriority').value = 'medium';
        document.getElementById('resourceStatus').value = '争夺中';
    },

    renderResources() {
        const container = document.getElementById('resourcesContainer');
        
        if (this.resources.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-5">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>还没有记录任何资源争夺</p>
                    <p>开始记录您的第一个资源吧！</p>
                </div>
            `;
            return;
        }

        const html = this.resources.map(resource => this.createResourceCard(resource)).join('');
        container.innerHTML = html;
    },

    createResourceCard(resource) {
        const priorityClass = `priority-${resource.priority}`;
        const statusBadge = this.getStatusBadge(resource.status);
        const deadlineText = resource.deadline ?
            `<div class="mt-2"><small class="text-muted"><i class="fas fa-clock me-1"></i>${new Date(resource.deadline).toLocaleString()}</small></div>` : '';

        return `
            <div class="card resource-card ${priorityClass} mb-3">
                <div class="card-body">
                    <div class="resource-header">
                        <div class="resource-content">
                            <h6 class="card-title">${resource.name}</h6>
                            <div class="mb-2">
                                ${statusBadge}
                                <span class="badge bg-secondary">${resource.type}</span>
                                <span class="badge bg-${this.getPriorityColor(resource.priority)}">${this.getPriorityText(resource.priority)}</span>
                            </div>
                        </div>
                        <div class="resource-actions">
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="ResourcePractical.editResource(${resource.id})" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-success" onclick="ResourcePractical.updateStatus(${resource.id}, '已获得')" title="标记为已获得">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-outline-warning" onclick="ResourcePractical.updateStatus(${resource.id}, '已错失')" title="标记为已错失">
                                    <i class="fas fa-times"></i>
                                </button>
                                <button class="btn btn-outline-danger" onclick="ResourcePractical.deleteResource(${resource.id})" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    ${resource.note ? `<p class="card-text small text-muted">${resource.note}</p>` : ''}
                    ${deadlineText}
                </div>
            </div>
        `;
    },

    getStatusBadge(status) {
        const badges = {
            '待争夺': '<span class="badge bg-secondary status-badge">待争夺</span>',
            '争夺中': '<span class="badge bg-primary status-badge">争夺中</span>',
            '已获得': '<span class="badge bg-success status-badge">已获得</span>',
            '已错失': '<span class="badge bg-danger status-badge">已错失</span>'
        };
        return badges[status] || badges['争夺中'];
    },

    getPriorityColor(priority) {
        const colors = {
            'high': 'danger',
            'medium': 'warning',
            'low': 'success'
        };
        return colors[priority] || 'secondary';
    },

    getPriorityText(priority) {
        const texts = {
            'high': '高优先级',
            'medium': '中优先级',
            'low': '低优先级'
        };
        return texts[priority] || '未知';
    },

    updateStatus(id, newStatus) {
        const resource = this.resources.find(r => r.id === id);
        if (resource) {
            resource.status = newStatus;
            resource.updatedAt = new Date().toISOString();
            this.saveData();
            this.renderResources();
            this.updateStats();
        }
    },

    editResource(id) {
        const resource = this.resources.find(r => r.id === id);
        if (resource) {
            document.getElementById('resourceType').value = resource.type;
            document.getElementById('resourceName').value = resource.name;
            document.getElementById('resourcePriority').value = resource.priority;
            document.getElementById('resourceStatus').value = resource.status;
            document.getElementById('resourceDeadline').value = resource.deadline;
            document.getElementById('resourceNote').value = resource.note;
            
            // 删除原资源，表单提交时会添加新的
            this.deleteResource(id, false);
        }
    },

    deleteResource(id, showConfirm = true) {
        if (showConfirm && !confirm('确定要删除这个资源记录吗？')) {
            return;
        }

        this.resources = this.resources.filter(r => r.id !== id);
        this.saveData();
        this.renderResources();
        this.updateStats();
    },

    applyFilters() {
        const statusFilter = document.getElementById('filterStatus').value;
        const typeFilter = document.getElementById('filterType').value;
        const priorityFilter = document.getElementById('filterPriority').value;

        let filteredResources = this.resources;

        if (statusFilter) {
            filteredResources = filteredResources.filter(r => r.status === statusFilter);
        }
        if (typeFilter) {
            filteredResources = filteredResources.filter(r => r.type === typeFilter);
        }
        if (priorityFilter) {
            filteredResources = filteredResources.filter(r => r.priority === priorityFilter);
        }

        const container = document.getElementById('resourcesContainer');
        if (filteredResources.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-5">
                    <i class="fas fa-search fa-3x mb-3"></i>
                    <p>没有找到符合条件的资源</p>
                    <button class="btn btn-outline-primary" onclick="ResourcePractical.clearFilters()">清除筛选</button>
                </div>
            `;
        } else {
            const html = filteredResources.map(resource => this.createResourceCard(resource)).join('');
            container.innerHTML = html;
        }
    },

    clearFilters() {
        document.getElementById('filterStatus').value = '';
        document.getElementById('filterType').value = '';
        document.getElementById('filterPriority').value = '';
        this.renderResources();
    },

    updateStats() {
        const today = new Date().toDateString();
        const todayResources = this.resources.filter(r => 
            new Date(r.createdAt).toDateString() === today
        );

        document.getElementById('todayTotal').textContent = todayResources.length;
        document.getElementById('todaySuccess').textContent = 
            todayResources.filter(r => r.status === '已获得').length;
        document.getElementById('todayMissed').textContent = 
            todayResources.filter(r => r.status === '已错失').length;
        document.getElementById('todayPending').textContent = 
            todayResources.filter(r => r.status === '争夺中' || r.status === '待争夺').length;
    },

    renderReflections() {
        const container = document.getElementById('reflectionsContainer');

        if (this.reflections.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-3">
                    <i class="fas fa-book-open fa-2x mb-2"></i>
                    <p>还没有保存任何反思</p>
                    <p>开始记录您的第一次反思吧！</p>
                </div>
            `;
            return;
        }

        const html = this.reflections.map(reflection => this.createReflectionCard(reflection)).join('');
        container.innerHTML = html;
    },

    createReflectionCard(reflection) {
        return `
            <div class="card mb-3 border-start border-4 border-info">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-calendar-day me-2"></i>${reflection.date}
                        </h6>
                        <button class="btn btn-outline-danger btn-sm" onclick="ResourcePractical.deleteReflection(${reflection.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    ${reflection.gains ? `
                        <div class="mb-2">
                            <strong class="text-success">
                                <i class="fas fa-trophy me-1"></i>今日收获:
                            </strong>
                            <div class="small text-muted mb-1" style="white-space: pre-line;">${reflection.gains}</div>
                        </div>
                    ` : ''}
                    ${reflection.mistakes ? `
                        <div class="mb-2">
                            <strong class="text-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>失误分析:
                            </strong>
                            <div class="small text-muted mb-1" style="white-space: pre-line;">${reflection.mistakes}</div>
                        </div>
                    ` : ''}
                    ${reflection.plan ? `
                        <div class="mb-2">
                            <strong class="text-primary">
                                <i class="fas fa-lightbulb me-1"></i>明日改进:
                            </strong>
                            <div class="small text-muted mb-1" style="white-space: pre-line;">${reflection.plan}</div>
                        </div>
                    ` : ''}
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>记录时间: ${new Date(reflection.createdAt).toLocaleString()}
                    </small>
                </div>
            </div>
        `;
    },

    deleteReflection(id) {
        if (!confirm('确定要删除这条反思记录吗？')) {
            return;
        }

        this.reflections = this.reflections.filter(r => r.id !== id);
        this.saveData();
        this.renderReflections();
    },

    toggleReflectionView() {
        const resourcesContainer = document.getElementById('resourcesContainer');
        const reflectionsContainer = document.getElementById('reflectionsContainer');
        const toggleBtn = document.querySelector('.card-header button');

        if (this.currentView === 'resources') {
            // 切换到反思视图
            resourcesContainer.style.display = 'none';
            reflectionsContainer.style.display = 'block';
            this.renderReflections();
            toggleBtn.innerHTML = '<i class="fas fa-list me-2"></i>查看资源';
            this.currentView = 'reflections';
        } else {
            // 切换到资源视图
            resourcesContainer.style.display = 'block';
            reflectionsContainer.style.display = 'none';
            toggleBtn.innerHTML = '<i class="fas fa-lightbulb me-2"></i>查看反思';
            this.currentView = 'resources';
        }
    },




};

// 保存反思功能
function saveReflection() {
    const reflection = {
        id: Date.now(),
        date: new Date().toDateString(),
        gains: document.getElementById('todayGains').value,
        mistakes: document.getElementById('todayMistakes').value,
        plan: document.getElementById('tomorrowPlan').value,
        createdAt: new Date().toISOString()
    };

    if (!reflection.gains && !reflection.mistakes && !reflection.plan) {
        alert('请至少填写一项反思内容');
        return;
    }

    ResourcePractical.reflections.unshift(reflection);
    ResourcePractical.saveData();
    ResourcePractical.renderReflections(); // 立即更新反思显示

    // 清空表单
    document.getElementById('todayGains').value = '1. ';
    document.getElementById('todayMistakes').value = '1. ';
    document.getElementById('tomorrowPlan').value = '1. ';
}

// 视图切换函数
function toggleReflectionView() {
    ResourcePractical.toggleReflectionView();
}

// 全局函数，供HTML调用
window.ResourcePractical = ResourcePractical;
window.saveReflection = saveReflection;
window.toggleReflectionView = toggleReflectionView;
