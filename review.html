<!DOCTYPE html>

<html lang="zh">

<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>复盘系统 - ST计划</title>
    <!-- 简化图标引用 -->
    <link rel="icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <!-- 核心样式文件 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" type="text/css" href="css/main.css">
    <!-- 精简后的样式 -->
    <style>
        body {
            background-color: #f8f9fa;
            color: #333;
            font-family: system-ui, sans-serif;
        }
        .view-container {
            width: 100%;
        }
        /* 基础卡片样式 */
        .template-card {
            margin-bottom: 10px;
            border: 1px solid #ddd;
        }
        /* 简化标签样式 - 合并为一种 */
        .badge-type {
            color: #fff;
            padding: 3px 6px;
            font-size: 12px;
        }
        .bg-daily { background-color: #3498db; }
        .bg-weekly { background-color: #2ecc71; }
        .bg-monthly { background-color: #9b59b6; }
        
        /* 响应式布局调整 */
        @media (max-width: 767px) {
            .container { padding: 0 5px; }
            .card { margin-bottom: 10px; }
            .card-body { padding: 8px; }
            .btn { padding: 4px 8px; font-size: 0.8rem; }
            .form-control, .form-select { padding: 4px 8px; font-size: 0.9rem; }
            /* 在移动设备上堆叠布局 */
            .row > div { width: 100%; }
            .col-md-3, .col-md-9 { flex: 0 0 100%; max-width: 100%; }
            /* 简化表单间距 */
            .mb-2 { margin-bottom: 0.5rem !important; }
            .input-group > .btn { padding: 2px 5px; }
        }
    </style>
</head>

<body>
    <div class="container mt-3">
        <!-- 顶部导航按钮 - 简化 -->
        <div class="row mb-2">
            <div class="col-12 d-flex justify-content-between align-items-center">
                <div class="btn-group flex-wrap" role="group">
                    <input class="btn-check" id="templateMode" name="reviewViewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="templateMode">复盘模板</label>
                    <input class="btn-check" id="dailyMode" name="reviewViewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="dailyMode">今日复盘</label>
                    <input class="btn-check" id="historyMode" name="reviewViewMode" type="radio" />
                    <label class="btn btn-outline-primary btn-sm" for="historyMode">历史复盘</label>
                </div>
                <a href="index.html" class="btn btn-primary btn-sm">
                    <i class="fas fa-home me-1" style="font-size: 14px;"></i>🏠 返回主页
                </a>
            </div>
        </div>

        <div class="row">
            <!-- 左侧栏 -->
            <div class="col-md-3">
                <div class="card mb-3">
                    <div class="card-header bg-primary text-white">复盘系统</div>
                    <div class="card-body">
                        <!-- 创建模板表单 -->
                        <form id="templateForm" class="mb-3">
                            <div class="mb-2">
                                <label class="form-label">模板名称:</label>
                                <input class="form-control" id="templateName" type="text" placeholder="输入模板名称" required />
                            </div>
                            <div class="mb-2">
                                <label class="form-label">模板类型:</label>
                                <select class="form-select" id="templateType">
                                    <option value="daily">日复盘</option>
                                    <option value="weekly">周复盘</option>
                                    <option value="monthly">月复盘</option>
                                </select>
                            </div>
                            <div class="mb-2">
                                <label class="form-label">复盘项:</label>
                                <div id="templateItems">
                                    <div class="input-group mb-2">
                                        <input type="text" class="form-control template-item" placeholder="复盘项内容" required />
                                        <button type="button" class="btn btn-outline-danger remove-item">删除</button>
                                    </div>
                                </div>
                                <button type="button" id="addItemBtn" class="btn btn-outline-secondary w-100 mt-2">
                                    添加复盘项
                                </button>
                            </div>
                            <button class="btn btn-primary w-100" type="submit">保存模板</button>
                        </form>

                        <!-- 数据统计 - 简化 -->
                        <div class="card mt-3">
                            <div class="card-header">复盘统计</div>
                            <div class="card-body p-2">
                                <div class="d-flex justify-content-between mb-1">
                                    <span>总复盘数:</span>
                                    <span id="totalReviewCount" class="badge bg-primary">0</span>
                                </div>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>日复盘:</span>
                                    <span id="dailyReviewCount" class="badge bg-daily">0</span>
                                </div>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>周复盘:</span>
                                    <span id="weeklyReviewCount" class="badge bg-weekly">0</span>
                                </div>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>月复盘:</span>
                                    <span id="monthlyReviewCount" class="badge bg-monthly">0</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>连续天数:</span>
                                    <span id="streakCount" class="badge bg-danger">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧内容区 -->
            <div class="col-md-9">
                <!-- 模板视图 -->
                <div id="templateView" class="view-container">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h5 class="mb-0">复盘模板</h5>
                    </div>
                    <div id="templatesContainer">
                        <!-- 模板卡片将在这里动态添加 -->
                    </div>
                </div>

                <!-- 日复盘视图 - 简化 -->
                <div id="dailyReviewView" class="view-container" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <h5 class="mb-0">今日复盘</h5>
                            <div id="currentDate" class="text-muted small mt-1">
                                <span id="formattedCurrentDate"></span>
                            </div>
                        </div>
                        <div>
                            <button id="saveDraftBtn" class="btn btn-outline-secondary btn-sm">草稿</button>
                            <button id="editDraftBtn" class="btn btn-outline-info btn-sm">编辑</button>
                            <button id="saveReviewBtn" class="btn btn-primary btn-sm">保存</button>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <div id="reviewTemplateSelector" class="mb-2">
                                <label class="form-label">选择复盘模板:</label>
                                <select class="form-select" id="templateSelect">
                                    <!-- 模板选项将在这里动态添加 -->
                                </select>
                            </div>
                            <form id="reviewForm">
                                <div id="reviewItemsContainer">
                                    <!-- 复盘项将在这里动态添加 -->
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- 历史复盘视图 - 简化 -->
                <div id="historyReviewView" class="view-container" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h5 class="mb-0">历史复盘</h5>
                        <button id="archiveDataBtn" class="btn btn-outline-warning btn-sm">归档</button>
                    </div>
                    
                    <!-- 搜索框 - 简化 -->
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="row g-2">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <input type="text" id="reviewSearchInput" class="form-control" placeholder="搜索..." />
                                        <button class="btn btn-outline-primary" id="reviewSearchBtn">搜索</button>
                                    </div>
                                </div>
                                <div class="col-md-6 text-md-end">
                                    <div class="form-check d-inline-block me-2">
                                        <input class="form-check-input" type="checkbox" id="selectAllReviews">
                                        <label class="form-check-label" for="selectAllReviews">全选</label>
                                    </div>
                                    <button id="batchDeleteReviewsBtn" class="btn btn-outline-danger btn-sm" style="display: none;">
                                        删除
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion" id="reviewHistoryAccordion">
                        <!-- 历史复盘记录将在这里动态添加 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 精简模态框 -->
    <div class="modal fade" id="confirmModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmModalLabel">确认操作</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="confirmModalBody">
                    确认要删除此模板吗？此操作不可恢复。
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmModalBtn">确认删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑模板模态框 - 简化 -->
    <div class="modal fade" id="editTemplateModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editTemplateModalLabel">编辑模板</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editTemplateForm">
                        <input type="hidden" id="editTemplateId">
                        <div class="mb-2">
                            <label class="form-label">模板名称:</label>
                            <input class="form-control" id="editTemplateName" type="text" required />
                        </div>
                        <div class="mb-2">
                            <label class="form-label">模板类型:</label>
                            <select class="form-select" id="editTemplateType">
                                <option value="daily">日复盘</option>
                                <option value="weekly">周复盘</option>
                                <option value="monthly">月复盘</option>
                            </select>
                        </div>
                        <div class="mb-2">
                            <label class="form-label">复盘项:</label>
                            <div id="editTemplateItems">
                                <!-- 编辑项将在这里动态添加 -->
                            </div>
                            <button type="button" id="editAddItemBtn" class="btn btn-outline-secondary w-100 mt-2">添加复盘项</button>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveEditTemplateBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 复盘详情模态框 - 简化 -->
    <div class="modal fade" id="reviewDetailModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="reviewDetailModalLabel">复盘详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="reviewDetailModalBody">
                    <!-- 复盘内容将在这里动态添加 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-danger" id="deleteReviewDetailBtn">删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 核心脚本文件 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/review.js"></script>
    <script src="js/sync.js"></script>
</body>

</html> 