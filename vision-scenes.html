<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>记忆管理 - ST计划</title>
    <!-- 网站图标 -->
    <link rel="icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <link rel="shortcut icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <!-- 内联PNG图标，用于不支持SVG的浏览器 -->
    <link rel="icon" href="data:image/png;base64,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" type="image/png">
    <!-- 样式文件 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    <link rel="stylesheet" type="text/css" href="css/main.css">
    <link rel="stylesheet" type="text/css" href="css/vision-scenes.css">
    <!-- 自定义样式 -->
    <style>
        body {
            overflow-x: hidden;
        }
        .view-container {
            width: 100%;
            overflow-x: hidden;
        }
        /* 密码验证层样式 */
        #password-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.95);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            flex-direction: column;
        }
        .password-container {
            width: 320px;
            max-width: 90%;
            background-color: #fff;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .password-container h3 {
            margin-bottom: 20px;
            color: #333;
        }
        .password-container .form-group {
            margin-bottom: 20px;
        }
        .password-error {
            color: #dc3545;
            margin-top: 10px;
            display: none;
        }
        /* 移动设备触摸优化已移除 */
        
        /* 移动设备触摸反馈已移除 */
        
        /* 表单错误反馈 */
        .is-invalid {
            border-color: #dc3545 !important;
            background-color: #fff8f8 !important;
        }
    </style>
</head>

<body>
    <!-- 密码验证层 -->
    <div id="password-overlay" style="display: none">
        <div class="password-container">
            <h3>ST计划访问验证</h3>
            <div class="form-group">
                <input type="password" id="access-password" class="form-control" placeholder="请输入访问密码">
            </div>
            <button id="password-submit" class="btn btn-primary w-100">确认</button>
            <div id="password-error" class="password-error">密码错误，请重试</div>
            <div class="form-check mt-3">
                <input class="form-check-input" type="checkbox" id="remember-password">
                <label class="form-check-label" for="remember-password">
                    在此设备上记住我
                </label>
            </div>
        </div>
    </div>

    <div class="container mt-3">
        <!-- 顶部导航按钮 -->
        <div class="row mb-3">
            <div class="col-12 d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-tree text-success me-2"></i>记忆树
                </h4>
                <button class="btn btn-primary btn-sm" onclick="window.location.href='index.html'">
                    <i class="fas fa-home me-1" style="font-size: 14px;"></i>🏠 返回主页
                </button>
            </div>
        </div>

        <div class="row">
            <!-- 左侧边栏 -->
            <div class="col-md-3">
                <div class="card mb-4">
                    <div class="card-header">
                        记忆管理
                    </div>
                    <div class="card-body">
                        <!-- 时间维度筛选 -->
                        <div class="mb-3">
                            <label class="form-label">时间维度:</label>
                            <div class="btn-group w-100" role="group">
                                <input type="radio" class="btn-check" name="timeFilter" id="pastFilter" checked>
                                <label class="btn btn-outline-secondary btn-sm" for="pastFilter">过去</label>
                                <input type="radio" class="btn-check" name="timeFilter" id="presentFilter">
                                <label class="btn btn-outline-secondary btn-sm" for="presentFilter">现在</label>
                                <input type="radio" class="btn-check" name="timeFilter" id="futureFilter">
                                <label class="btn btn-outline-secondary btn-sm" for="futureFilter">未来</label>
                            </div>
                        </div>

                        <!-- 添加新记忆表单 -->
                        <form id="sceneForm">
                            <div class="mb-3">
                                <label class="form-label">父记忆:</label>
                                <select class="form-control form-control-sm" id="parentScene">
                                    <option value="">作为根记忆</option>
                                </select>
                            </div>
                            <div class="mb-3" id="positionGroup" style="display: none;">
                                <label class="form-label">分支位置:</label>
                                <div class="btn-group w-100" role="group">
                                    <input type="radio" class="btn-check" name="position" id="leftPosition" value="left">
                                    <label class="btn btn-outline-secondary btn-sm" for="leftPosition">左分支</label>
                                    <input type="radio" class="btn-check" name="position" id="rightPosition" value="right">
                                    <label class="btn btn-outline-secondary btn-sm" for="rightPosition">右分支</label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">记忆标题:</label>
                                <input class="form-control form-control-sm" id="sceneTitle" type="text" required />
                            </div>
                            <div class="mb-3">
                                <label class="form-label">时间 (年月):</label>
                                <input class="form-control form-control-sm" id="sceneTime" type="month" required />
                            </div>
                            <div class="mb-3">
                                <label class="form-label">记忆描述:</label>
                                <textarea class="form-control form-control-sm" id="sceneDescription" rows="4" required></textarea>
                            </div>
                            <button class="btn btn-primary btn-sm w-100" type="submit">添加记忆</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- 右侧主内容区 -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>记忆展示</span>
                        <small class="text-muted">按时间从新到旧排序</small>
                    </div>
                    <div class="card-body p-2">
                        <!-- 记忆树视图 -->
                        <div class="tree-view view-container" id="treeView">
                            <!-- 记忆树内容将在这里动态添加 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 记忆详情模态框 -->
    <div class="modal fade" id="sceneDetailModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalSceneTitle">记忆详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="modalSceneContent">
                    <!-- 记忆详情内容将在这里动态添加 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="editSceneBtn">编辑</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑记忆模态框 -->
    <div class="modal fade" id="editSceneModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑记忆</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editSceneForm">
                        <input type="hidden" id="editSceneId">
                        <div class="mb-3">
                            <label class="form-label">记忆标题:</label>
                            <input class="form-control" id="editSceneTitle" type="text" required />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">时间 (年月):</label>
                            <input class="form-control" id="editSceneTime" type="month" required />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">记忆描述:</label>
                            <textarea class="form-control" id="editSceneDescription" rows="5" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveEditSceneBtn">保存</button>
                </div>
            </div>
        </div>
    </div>



    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.min.js"></script>
    <script src="js/st.js"></script>
    <script src="js/vision-scenes.js"></script>
    <script>
        // 密码验证功能
        document.addEventListener('DOMContentLoaded', function() {
            // 性能优化：仅在需要验证时才加载验证相关DOM元素
            const passwordOverlay = document.getElementById('password-overlay');
            
            // 检查是否已经记住密码
            if (localStorage.getItem('st_authenticated') !== 'true') {
                // 显示验证层
                passwordOverlay.style.display = 'flex';
                
                // 延迟获取验证相关元素，避免不必要的DOM查询
                const passwordInput = document.getElementById('access-password');
                const passwordSubmit = document.getElementById('password-submit');
                const passwordError = document.getElementById('password-error');
                const rememberPassword = document.getElementById('remember-password');
                
                // 默认密码
                const correctPassword = 'st6666';
                
                // 密码提交按钮事件
                passwordSubmit.addEventListener('click', function() {
                    verifyPassword();
                }, { passive: true });
                
                // 按回车键也可以提交密码
                passwordInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        verifyPassword();
                    }
                }, { passive: true });
                
                // 验证密码函数
                function verifyPassword() {
                    const inputPassword = passwordInput.value;
                    
                    if(inputPassword === correctPassword) {
                        // 密码正确，隐藏验证层
                        passwordOverlay.style.display = 'none';
                        
                        // 如果选择了记住密码，将验证状态保存到localStorage
                        if(rememberPassword.checked) {
                            localStorage.setItem('st_authenticated', 'true');
                        }
                        
                        // 通过验证后初始化系统
                        initSystem();
                    } else {
                        // 密码错误
                        passwordError.style.display = 'block';
                        passwordInput.value = '';
                        passwordInput.focus();
                    }
                }
            } else {
                // 已验证，隐藏验证层
                passwordOverlay.style.display = 'none';
                
                // 直接初始化系统
                initSystem();
            }
        });
        
        // 系统初始化函数
        function initSystem() {
            // 初始化脑中画面系统
            if (typeof initVisionScenes === 'function') {
                // 使用setTimeout延迟初始化，让页面先渲染完成
                setTimeout(initVisionScenes, 100);
            }
        }
    </script>
</body>

</html> 