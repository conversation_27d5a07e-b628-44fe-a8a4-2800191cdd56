// 数据进度比较功能
// 作为ST计划的一部分，用于比较用户参数与目标参数

// 确保appData中有dataProgress对象和分类数组
if (!appData.dataProgress) {
    appData.dataProgress = {
        items: [],
        categories: []
    };
} else if (!appData.dataProgress.categories) {
    // 确保老数据也有categories字段
    appData.dataProgress.categories = [];
}

// 初始化Bootstrap模态框
let editProgressModal;
let deleteConfirmModal;
let categoryManagementModal;
let currentDeleteIndex = -1;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化模态框
    editProgressModal = new bootstrap.Modal(document.getElementById('editProgressModal'));
    deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    
    // 检查分类管理模态框是否存在
    const categoryManagementModalEl = document.getElementById('categoryManagementModal');
    if (categoryManagementModalEl) {
        categoryManagementModal = new bootstrap.Modal(categoryManagementModalEl);
    }
    
    // 初始化监听器
    initEventListeners();
    
    // 初始化数据
    initData();
    
    // 渲染进度卡片
    renderProgressItems();
});

// 初始化事件监听器
function initEventListeners() {
    // 保存进度按钮点击事件
    const saveProgressBtn = document.getElementById('saveProgressBtn');
    if (saveProgressBtn) {
        saveProgressBtn.addEventListener('click', function() {
            addProgressItem();
        });
    }
    
    // 取消添加按钮点击事件
    const cancelAddBtn = document.getElementById('cancelAddBtn');
    if (cancelAddBtn) {
        cancelAddBtn.addEventListener('click', function() {
            const addFormContent = document.querySelector('#addProgressForm .row');
            if (addFormContent) {
                addFormContent.style.display = 'none';
            }
            
            const toggleFormBtn = document.getElementById('toggleFormBtn');
            if (toggleFormBtn) {
                toggleFormBtn.innerHTML = '<i class="fas fa-plus me-1"></i>添加比较';
            }
        });
    }
    
    // 更新按钮点击事件
    const updateProgressBtn = document.getElementById('updateProgressBtn');
    if (updateProgressBtn) {
        updateProgressBtn.addEventListener('click', function() {
            saveProgressEdit();
        });
    }
    
    // 确认删除按钮点击事件
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            if (currentDeleteIndex !== -1) {
                confirmDeleteProgressItem(currentDeleteIndex);
            }
        });
    }
    
    // 分类管理按钮点击事件
    const manageCategoriesBtn = document.getElementById('manageCategoriesBtn');
    if (manageCategoriesBtn) {
        manageCategoriesBtn.addEventListener('click', function() {
            openCategoryManagement();
        });
    }
    
    // 添加分类按钮点击事件
    const addCategoryBtn = document.getElementById('addCategoryBtn');
    if (addCategoryBtn) {
        addCategoryBtn.addEventListener('click', function() {
            addCategory();
        });
    }
    
    // 分类筛选器变更事件
    const categoryFilter = document.getElementById('categoryFilter');
    if (categoryFilter) {
        categoryFilter.addEventListener('change', function() {
            filterByCategory();
        });
    }
    
    // 表单显示/隐藏切换
    const toggleFormBtn = document.getElementById('toggleFormBtn');
    if (toggleFormBtn) {
        // 默认状态 - 表单隐藏
        const addProgressForm = document.getElementById('addProgressForm');
        if (addProgressForm) {
            // 初始状态下只显示表单的标题栏，内容隐藏
            addProgressForm.style.display = 'block';
            const addFormContent = document.querySelector('#addProgressForm .row');
            if (addFormContent) {
                addFormContent.style.display = 'none';
            }
        }
        
        // 添加点击事件监听器
        toggleFormBtn.addEventListener('click', function() {
            const addFormContent = document.querySelector('#addProgressForm .row');
            if (!addFormContent) return;
            
            if (addFormContent.style.display === 'none') {
                // 显示表单内容
                addProgressForm.style.display = 'block';
                addFormContent.style.display = 'flex';
                // 按钮文字变为"取消"
                toggleFormBtn.innerHTML = '<i class="fas fa-times me-1"></i>取消添加';
                
                // 添加表单动画效果
                addProgressForm.classList.add('animate__animated', 'animate__fadeIn');
                setTimeout(() => {
                    addProgressForm.classList.remove('animate__animated', 'animate__fadeIn');
                }, 500);
            } else {
                // 隐藏表单内容
                addFormContent.style.display = 'none';
                // 按钮文字变为"添加比较"
                toggleFormBtn.innerHTML = '<i class="fas fa-plus me-1"></i>添加比较';
            }
        });
    }
}

// 初始化数据
function initData() {
    // 从localStorage加载数据
    const savedData = localStorage.getItem('savingsData');
    if (savedData) {
        const parsedData = JSON.parse(savedData);
        
        // 如果主系统的appData有其他数据，确保不会被覆盖
        // 将解析出的数据合并到当前的appData中
        Object.keys(parsedData).forEach(key => {
            if (key !== 'dataProgress') {
                appData[key] = parsedData[key];
            }
        });
        
        // 更新dataProgress数据
        if (parsedData.dataProgress) {
            appData.dataProgress = parsedData.dataProgress;
            
            // 确保categories字段存在
            if (!appData.dataProgress.categories) {
                appData.dataProgress.categories = [];
            }
        } else {
            // 如果不存在，则初始化一个空的dataProgress对象
            appData.dataProgress = {
                items: [],
                categories: []
            };
        }
    }
    
    // 初始化分类选项
    updateCategoryOptions();
    
    // 初始化分类筛选器
    updateCategoryFilter();
}

// 渲染所有参数比较卡片
function renderProgressItems() {
    const container = document.getElementById('dataProgressContainer');
    if (!container) return;

    // 清空容器
    container.innerHTML = '';

    // 获取当前的分类筛选值
    const categoryFilter = document.getElementById('categoryFilter');
    const selectedCategoryId = categoryFilter ? categoryFilter.value : 'all';

    // 根据分类筛选数据
    let itemsToDisplay = [];
    if (selectedCategoryId === 'all') {
        itemsToDisplay = appData.dataProgress.items;
    } else {
        itemsToDisplay = appData.dataProgress.items.filter(item => item.categoryId === selectedCategoryId);
    }

    // 更新统计信息
    updateStatistics(itemsToDisplay);

    // 如果没有数据，显示优化后的空状态提示
    if (!itemsToDisplay || itemsToDisplay.length === 0) {
        const isFiltered = selectedCategoryId !== 'all';
        container.innerHTML = `
            <div class="col-12 empty-state">
                <i class="fas fa-${isFiltered ? 'filter' : 'chart-line'}"></i>
                <h5>${isFiltered ? '筛选结果为空' : '开始您的参数追踪之旅'}</h5>
                <p>${isFiltered ? '该分类下暂无参数比较数据，试试其他分类或添加新数据' : '添加您想要追踪的参数，如体重、存款、学习时长等'}</p>
                <div class="mt-3">
                    <button class="btn btn-primary btn-sm me-2" onclick="document.getElementById('toggleFormBtn').click();">
                        <i class="fas fa-plus me-1"></i> 添加参数
                    </button>
                    ${isFiltered ? `
                    <button class="btn btn-outline-secondary btn-sm" onclick="document.getElementById('categoryFilter').value='all'; filterByCategory();">
                        <i class="fas fa-eye me-1"></i> 查看全部
                    </button>
                    ` : ''}
                </div>
            </div>
        `;
        return;
    }

    // 创建行元素用于包装卡片
    const row = document.createElement('div');
    row.className = 'row';
    container.appendChild(row);

    // 渲染每个参数比较卡片
    itemsToDisplay.forEach((item, index) => {
        // 找到item在原数组中的索引，以便编辑和删除功能正常工作
        const originalIndex = appData.dataProgress.items.findIndex(i => i.id === item.id);

        // 创建列元素来容纳卡片 - 优化响应式布局
        const col = document.createElement('div');
        col.className = 'col-12 col-sm-6 col-lg-4 col-xl-3 mb-2';

        // 添加卡片到列
        col.appendChild(createProgressCard(item, originalIndex));
        row.appendChild(col);

        // 移除延迟显示特效
    });
}

// 创建参数比较卡片 - 优化版
function createProgressCard(item, index) {
    // 创建卡片元素
    const card = document.createElement('div');
    card.className = 'card progress-card';
    card.dataset.progressId = item.id;

    // 计算进度百分比和类型
    let progressPercent = calculateProgress(item);
    let progressType = getProgressType(item);

    // 获取参数类型和颜色
    let paramType = getParamType(item.name, item.unit);
    let progressColorClass = getProgressColorClass(progressPercent, paramType);

    // 获取分类名称
    const categoryName = getCategoryName(item.categoryId);

    // 格式化单位显示
    const unitDisplay = item.unit ? item.unit : '';

    // 设置卡片内容 - 紧凑优化版本
    card.innerHTML = `
        <div class="card-header">
            <div class="d-flex align-items-center justify-content-between">
                <div class="flex-grow-1">
                    <h6 class="mb-1 fw-bold" style="font-size: 1.1rem; color: var(--text-color);">
                        <i class="fas fa-chart-line me-2 text-primary"></i>
                        ${item.name}
                    </h6>
                    <div class="d-flex align-items-center gap-2">
                        ${categoryName ?
                          `<span class="param-type-badge param-type-${paramType}">
                             ${categoryName}
                           </span>` : ''}
                    </div>
                    ${item.targetName ?
                      `<div class="mt-1" style="font-size: 0.9rem; color: #64748b;">
                         <i class="fas fa-bullseye me-1"></i> ${item.targetName}
                       </div>` : ''}
                </div>
                <!-- 操作按钮 -->
                <div class="card-actions d-flex gap-1">
                    <button class="btn btn-outline-primary edit-progress-btn" data-index="${index}" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-danger delete-progress-btn" data-index="${index}" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body" style="padding: 0.75rem;">
            <div class="progress-info mb-2">
                <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                    <span style="font-size: 1rem; flex: 0 0 auto;">
                        当前: <strong class="fw-bold quick-edit-value" data-field="userValue" data-index="${index}" style="cursor: pointer; color: #1e40af;">${item.userValue}${unitDisplay}</strong>
                        <input type="number" class="quick-edit-input" data-field="userValue" data-index="${index}" value="${item.userValue}" style="display: none;">
                    </span>
                    <span style="font-size: 1rem; color: #64748b; flex: 0 0 auto; text-align: right;">
                        目标: <strong class="fw-bold">${item.targetValue}${unitDisplay}</strong>
                    </span>
                </div>
            </div>

            <div class="progress mb-2">
                <div class="progress-bar ${progressColorClass}" role="progressbar"
                    style="width: ${progressPercent}%"
                    aria-valuenow="${progressPercent}"
                    aria-valuemin="0"
                    aria-valuemax="100">
                    ${progressPercent}%
                </div>
            </div>

            <div class="d-flex justify-content-between align-items-center" style="font-size: 0.875rem; color: #64748b;">
                <span>${getProgressDescription(progressPercent, progressType)}</span>
                <span>${progressPercent}% 完成</span>
            </div>
        </div>
    `;
    
    // 添加事件监听器
    // 编辑按钮
    card.querySelector('.edit-progress-btn').addEventListener('click', function() {
        editProgressItem(index);
    });

    // 删除按钮
    card.querySelector('.delete-progress-btn').addEventListener('click', function() {
        deleteProgressItem(index);
    });

    // 快速编辑功能
    const quickEditValue = card.querySelector('.quick-edit-value');
    const quickEditInput = card.querySelector('.quick-edit-input');

    if (quickEditValue && quickEditInput) {
        // 点击数值进入编辑模式
        quickEditValue.addEventListener('click', function() {
            quickEditValue.style.display = 'none';
            quickEditInput.style.display = 'inline-block';
            quickEditInput.focus();
            quickEditInput.select();
        });

        // 失去焦点或按回车保存
        quickEditInput.addEventListener('blur', function() {
            saveQuickEdit(quickEditInput, quickEditValue, index);
        });

        quickEditInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                saveQuickEdit(quickEditInput, quickEditValue, index);
            } else if (e.key === 'Escape') {
                cancelQuickEdit(quickEditInput, quickEditValue);
            }
        });
    }

    // 移除延迟动画，直接设置进度条宽度（已在HTML中设置）

    return card;
}

// 计算进度百分比 - 简化版
function calculateProgress(item) {
    let progressPercent = 0;

    if (item.targetValue > 0) {
        if (item.targetValue >= item.userValue) {
            // 递增型目标（目标值大于等于当前值）
            progressPercent = Math.min(100, Math.round((item.userValue / item.targetValue) * 100));
        } else {
            // 递减型目标（目标值小于当前值）
            // 简化计算：假设起始值为当前值的1.5倍
            const startValue = item.userValue * 1.5;
            const totalReduction = startValue - item.targetValue;
            const currentReduction = startValue - item.userValue;
            progressPercent = Math.min(100, Math.max(0, Math.round((currentReduction / totalReduction) * 100)));
        }
    }

    return progressPercent;
}

// 获取进度类型
function getProgressType(item) {
    if (item.targetValue > item.userValue) {
        return 'increase'; // 递增型
    } else if (item.targetValue < item.userValue) {
        return 'decrease'; // 递减型
    } else {
        return 'equal'; // 已达成
    }
}

// 获取参数类型（基于名称和单位智能识别）
function getParamType(name, unit) {
    const nameStr = name.toLowerCase();
    const unitStr = (unit || '').toLowerCase();

    // 健康相关
    if (nameStr.includes('体重') || nameStr.includes('血压') || nameStr.includes('心率') ||
        nameStr.includes('体脂') || unitStr.includes('kg') || unitStr.includes('bpm')) {
        return 'health';
    }

    // 健身相关
    if (nameStr.includes('跑步') || nameStr.includes('健身') || nameStr.includes('运动') ||
        nameStr.includes('肌肉') || unitStr.includes('km') || unitStr.includes('次')) {
        return 'fitness';
    }

    // 财务相关
    if (nameStr.includes('存款') || nameStr.includes('收入') || nameStr.includes('投资') ||
        nameStr.includes('理财') || unitStr.includes('元') || unitStr.includes('$')) {
        return 'finance';
    }

    // 学习相关
    if (nameStr.includes('学习') || nameStr.includes('阅读') || nameStr.includes('考试') ||
        nameStr.includes('分数') || unitStr.includes('分') || unitStr.includes('小时')) {
        return 'study';
    }

    // 工作相关
    if (nameStr.includes('工作') || nameStr.includes('项目') || nameStr.includes('任务') ||
        nameStr.includes('绩效') || unitStr.includes('个')) {
        return 'work';
    }

    return 'default';
}

// 根据进度百分比和参数类型获取颜色类
function getProgressColorClass(percent, paramType) {
    // 基础颜色根据参数类型确定
    let baseClass = `progress-bar-${paramType}`;

    // 根据进度调整透明度或亮度
    if (percent >= 100) {
        return baseClass; // 完成时使用完整颜色
    } else if (percent >= 75) {
        return baseClass; // 75%以上使用完整颜色
    } else if (percent >= 50) {
        return baseClass; // 50%以上使用完整颜色
    } else if (percent >= 25) {
        return baseClass; // 25%以上使用完整颜色
    } else {
        return 'progress-bar-default'; // 25%以下使用默认颜色
    }
}

// 获取进度描述
function getProgressDescription(percent, progressType) {
    if (percent >= 100) {
        return progressType === 'decrease' ? '目标已达成！' : '目标已完成！';
    } else if (percent >= 75) {
        return '即将完成';
    } else if (percent >= 50) {
        return '进展良好';
    } else if (percent >= 25) {
        return '继续努力';
    } else {
        return '刚刚开始';
    }
}

// 获取分类名称
function getCategoryName(categoryId) {
    if (!categoryId) return '';
    
    // 确保分类数组存在
    if (!appData.dataProgress.categories) {
        appData.dataProgress.categories = [];
        return '';
    }
    
    const category = appData.dataProgress.categories.find(cat => cat.id === categoryId);
    return category ? category.name : '';
}

// 添加新的参数比较
function addProgressItem() {
    // 获取表单数据
    const paramName = document.getElementById('progressName').value;
    const paramUnit = document.getElementById('progressUnit').value;
    const userValue = parseFloat(document.getElementById('progressUserValue').value);
    const targetValue = parseFloat(document.getElementById('progressTargetValue').value);
    const targetName = document.getElementById('progressTargetName').value;
    const categoryId = document.getElementById('progressCategory').value;
    
    // 验证必要的数据
    if (!paramName || isNaN(userValue) || isNaN(targetValue)) {
        showToast('请填写必要的参数信息', 'warning');
        return;
    }
    
    // 创建新的参数比较项
    const newItem = {
        id: Date.now(),
        name: paramName,
        unit: paramUnit || '',
        userValue: userValue,
        targetValue: targetValue,
        targetName: targetName || '',
        createdAt: new Date().toISOString()
    };
    
    // 如果选择了分类，添加分类ID
    if (categoryId) {
        newItem.categoryId = categoryId;
    }
    
    // 添加到数据数组
    appData.dataProgress.items.push(newItem);
    
    // 保存数据到localStorage
    saveData();
    
    // 重新渲染列表
    renderProgressItems();
    
    // 手动重置所有表单字段
    document.getElementById('progressName').value = '';
    document.getElementById('progressUnit').value = '';
    document.getElementById('progressUserValue').value = '';
    document.getElementById('progressTargetValue').value = '';
    document.getElementById('progressTargetName').value = '';
    document.getElementById('progressCategory').value = '';
    
    // 隐藏表单
    const addProgressForm = document.getElementById('addProgressForm');
    if (addProgressForm) {
        const addFormContent = document.querySelector('#addProgressForm .row');
        if (addFormContent) {
            addFormContent.style.display = 'none';
        }
    }
    
    // 重置添加按钮状态
    const toggleFormBtn = document.getElementById('toggleFormBtn');
    if (toggleFormBtn) {
        toggleFormBtn.innerHTML = '<i class="fas fa-plus me-1"></i>添加比较';
    }
    
    // 显示提示
    showToast('参数比较添加成功', 'success');
}

// 编辑参数比较项
function editProgressItem(index) {
    // 获取要编辑的项
    const item = appData.dataProgress.items[index];
    
    // 填充编辑表单
    document.getElementById('editProgressName').value = item.name;
    document.getElementById('editProgressUnit').value = item.unit || '';
    document.getElementById('editProgressUserValue').value = item.userValue;
    document.getElementById('editProgressTargetValue').value = item.targetValue;
    document.getElementById('editProgressTargetName').value = item.targetName || '';
    
    // 设置分类选择框的值
    const editCategorySelect = document.getElementById('editProgressCategory');
    if (editCategorySelect) {
        if (item.categoryId) {
            editCategorySelect.value = item.categoryId;
        } else {
            editCategorySelect.value = '';
        }
    }
    
    // 存储正在编辑的项的索引 - 使用数据属性
    document.getElementById('updateProgressBtn').dataset.index = index;
    
    // 显示编辑模态框
    editProgressModal.show();
}

// 保存编辑的参数比较项
function saveProgressEdit() {
    // 获取表单数据
    const index = parseInt(document.getElementById('updateProgressBtn').dataset.index);
    const paramName = document.getElementById('editProgressName').value;
    const paramUnit = document.getElementById('editProgressUnit').value;
    const userValue = parseFloat(document.getElementById('editProgressUserValue').value);
    const targetValue = parseFloat(document.getElementById('editProgressTargetValue').value);
    const targetName = document.getElementById('editProgressTargetName').value;
    const categoryId = document.getElementById('editProgressCategory').value;
    
    // 验证必要的数据
    if (!paramName || isNaN(userValue) || isNaN(targetValue) || index < 0 || index >= appData.dataProgress.items.length) {
        showToast('请填写必要的参数信息', 'warning');
        return;
    }
    
    // 更新参数比较项
    const item = appData.dataProgress.items[index];
    item.name = paramName;
    item.unit = paramUnit || '';
    item.userValue = userValue;
    item.targetValue = targetValue;
    item.targetName = targetName || '';
    item.updatedAt = new Date().toISOString();
    
    // 更新分类ID
    if (categoryId) {
        item.categoryId = categoryId;
    } else {
        delete item.categoryId;
    }
    
    // 保存数据到localStorage
    saveData();
    
    // 重新渲染列表
    renderProgressItems();
    
    // 关闭编辑模态框
    editProgressModal.hide();
    
    // 显示成功消息
    showToast('参数比较更新成功！', 'success');
}

// 删除参数比较项 - 显示确认对话框
function deleteProgressItem(index) {
    // 保存要删除的项的索引
    currentDeleteIndex = index;
    
    // 显示删除确认模态框
    deleteConfirmModal.show();
}

// 确认删除参数比较项
function confirmDeleteProgressItem(index) {
    // 从数组中移除该项
    appData.dataProgress.items.splice(index, 1);
    
    // 保存数据到localStorage
    saveData();
    
    // 重置当前删除索引
    currentDeleteIndex = -1;
    
    // 关闭删除确认模态框
    deleteConfirmModal.hide();
    
    // 重新渲染列表
    renderProgressItems();
    
    // 显示成功消息
    showToast('参数比较已删除！', 'success');
}

// 保存数据到localStorage
function saveData() {
    // 使用app.js中的saveAppData函数保存数据
    if (typeof saveAppData === 'function') {
        saveAppData();
    } else {
        // 如果app.js中的函数不可用，则直接保存
        localStorage.setItem('savingsData', JSON.stringify(appData));
        
        // 触发数据更改事件
        const dataChangeEvent = new CustomEvent('appDataChanged', {
            detail: { source: 'dataProgress' }
        });
        window.dispatchEvent(dataChangeEvent);
    }
}

// 监听来自其他页面的数据变化事件
window.addEventListener('appDataChanged', function(event) {
    // 如果数据变化不是由本页面引起的，则重新加载数据
    if (event.detail && event.detail.source !== 'dataProgress') {
        initData();
        renderProgressItems();
    }
});

// 显示提示消息 - 使用app.js中的showToast函数
function showToast(message, type = 'info') {
    // 如果app.js中的showToast函数可用，则使用它
    if (typeof window.showToast === 'function') {
        window.showToast(message, type);
    } else {
        // 检查是否已经有toast容器
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            // 创建toast容器
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            toastContainer.style.zIndex = "1050";
            document.body.appendChild(toastContainer);
        }
        
        // 创建toast元素
        const toastId = `toast-${Date.now()}`;
        const toastEl = document.createElement('div');
        toastEl.className = 'toast';
        toastEl.id = toastId;
        toastEl.setAttribute('role', 'alert');
        toastEl.setAttribute('aria-live', 'assertive');
        toastEl.setAttribute('aria-atomic', 'true');
        
        // 根据类型选择图标和颜色
        let icon, colorClass;
        switch (type) {
            case 'success':
                icon = '<i class="fas fa-check-circle me-2"></i>';
                colorClass = 'text-success';
                break;
            case 'error':
                icon = '<i class="fas fa-exclamation-circle me-2"></i>';
                colorClass = 'text-danger';
                break;
            case 'warning':
                icon = '<i class="fas fa-exclamation-triangle me-2"></i>';
                colorClass = 'text-warning';
                break;
            case 'info':
            default:
                icon = '<i class="fas fa-info-circle me-2"></i>';
                colorClass = 'text-primary';
        }
        
        toastEl.innerHTML = `
            <div class="toast-header">
                <span class="${colorClass}">${icon}</span>
                <strong class="me-auto">通知</strong>
                <small class="text-muted">刚刚</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;
        
        // 添加到容器
        toastContainer.appendChild(toastEl);
        
        // 初始化并显示toast
        const toast = new bootstrap.Toast(toastEl, {
            animation: true,
            autohide: true,
            delay: 3000
        });
        toast.show();
        
        // 自动移除toast元素
        toastEl.addEventListener('hidden.bs.toast', function() {
            toastEl.remove();
        });
    }
}

// 打开分类管理模态框
function openCategoryManagement() {
    // 确保分类管理模态框存在
    if (!categoryManagementModal) {
        showToast('分类管理功能暂不可用', 'warning');
        return;
    }
    
    // 渲染分类列表
    renderCategoriesList();
    
    // 显示模态框
    categoryManagementModal.show();
}

// 渲染分类列表
function renderCategoriesList() {
    const categoriesList = document.getElementById('categoriesList');
    if (!categoriesList) return;
    
    categoriesList.innerHTML = '';
    
    // 如果没有分类，显示提示信息
    if (!appData.dataProgress.categories || appData.dataProgress.categories.length === 0) {
        categoriesList.innerHTML = '<li class="list-group-item text-muted">暂无分类</li>';
        return;
    }
    
    // 渲染每个分类
    appData.dataProgress.categories.forEach((category, index) => {
        const listItem = document.createElement('li');
        listItem.className = 'list-group-item d-flex justify-content-between align-items-center';
        
        listItem.innerHTML = `
            <span>${category.name}</span>
            <button class="btn btn-sm btn-outline-danger delete-category-btn" data-index="${index}">
                <i class="fas fa-trash"></i>
            </button>
        `;
        
        // 添加删除分类的事件监听器
        listItem.querySelector('.delete-category-btn').addEventListener('click', function() {
            deleteCategory(index);
        });
        
        categoriesList.appendChild(listItem);
    });
}

// 添加新分类
function addCategory() {
    const newCategoryName = document.getElementById('newCategoryName').value.trim();
    
    // 验证分类名称
    if (!newCategoryName) {
        showToast('请输入分类名称', 'warning');
        return;
    }
    
    // 检查是否已存在相同名称的分类
    if (appData.dataProgress.categories.some(category => category.name === newCategoryName)) {
        showToast('该分类名称已存在', 'warning');
        return;
    }
    
    // 创建新分类
    const newCategory = {
        id: Date.now().toString(),
        name: newCategoryName
    };
    
    // 添加到分类数组
    appData.dataProgress.categories.push(newCategory);
    
    // 保存数据
    saveData();
    
    // 重新渲染分类列表
    renderCategoriesList();
    
    // 更新添加表单和编辑表单中的分类选项
    updateCategoryOptions();
    
    // 更新分类筛选器
    updateCategoryFilter();
    
    // 清空输入框
    document.getElementById('newCategoryName').value = '';
    
    // 显示提示
    showToast('分类添加成功', 'success');
}

// 删除分类
function deleteCategory(index) {
    // 获取要删除的分类ID
    const categoryId = appData.dataProgress.categories[index].id;
    
    // 确认是否要删除
    if (!confirm('删除此分类将会清空该分类下所有项目的分类信息，确定要删除吗？')) {
        return;
    }
    
    // 从分类数组中删除
    appData.dataProgress.categories.splice(index, 1);
    
    // 清除所有使用此分类的项目的分类ID
    appData.dataProgress.items.forEach(item => {
        if (item.categoryId === categoryId) {
            delete item.categoryId;
        }
    });
    
    // 保存数据
    saveData();
    
    // 重新渲染分类列表
    renderCategoriesList();
    
    // 更新添加表单和编辑表单中的分类选项
    updateCategoryOptions();
    
    // 更新分类筛选器
    updateCategoryFilter();
    
    // 重新渲染进度项
    renderProgressItems();
    
    // 显示提示
    showToast('分类删除成功', 'success');
}

// 更新所有分类选择框的选项
function updateCategoryOptions() {
    // 获取添加表单和编辑表单中的分类选择框
    const categorySelect = document.getElementById('progressCategory');
    const editCategorySelect = document.getElementById('editProgressCategory');
    
    if (!categorySelect && !editCategorySelect) return;
    
    // 确保分类数组存在
    if (!appData.dataProgress.categories) {
        appData.dataProgress.categories = [];
    }
    
    // 更新添加表单中的分类选择框
    if (categorySelect) {
        // 清空现有选项，保留默认选项
        categorySelect.innerHTML = '<option value="" selected>-- 选择分类 --</option>';
        
        // 添加分类选项
        appData.dataProgress.categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            categorySelect.appendChild(option);
        });
    }
    
    // 更新编辑表单中的分类选择框
    if (editCategorySelect) {
        // 清空现有选项，保留默认选项
        editCategorySelect.innerHTML = '<option value="">-- 选择分类 --</option>';
        
        // 添加分类选项
        appData.dataProgress.categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            editCategorySelect.appendChild(option);
        });
    }
}

// 更新分类筛选器
function updateCategoryFilter() {
    const categoryFilter = document.getElementById('categoryFilter');
    
    if (!categoryFilter) return;
    
    // 保存当前选中的值
    const currentValue = categoryFilter.value;
    
    // 清空现有选项，保留"全部显示"选项
    categoryFilter.innerHTML = '<option value="all" selected>全部显示</option>';
    
    // 确保分类数组存在
    if (!appData.dataProgress.categories) {
        appData.dataProgress.categories = [];
        return;
    }
    
    // 添加分类选项
    appData.dataProgress.categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.id;
        option.textContent = category.name;
        categoryFilter.appendChild(option);
    });
    
    // 如果之前选中的值仍然存在，则恢复选中状态
    if (currentValue !== 'all') {
        const exists = Array.from(categoryFilter.options).some(option => option.value === currentValue);
        if (exists) {
            categoryFilter.value = currentValue;
        }
    }
}

// 根据分类筛选进度项
function filterByCategory() {
    // 重新渲染列表（renderProgressItems 函数会根据筛选值过滤数据）
    renderProgressItems();
}

// 快速编辑功能
function saveQuickEdit(inputElement, valueElement, index) {
    const newValue = parseFloat(inputElement.value);
    const field = inputElement.dataset.field;

    // 验证输入值
    if (isNaN(newValue) || newValue < 0) {
        showToast('请输入有效的数值', 'warning');
        cancelQuickEdit(inputElement, valueElement);
        return;
    }

    // 更新数据
    if (field === 'userValue' && index >= 0 && index < appData.dataProgress.items.length) {
        const item = appData.dataProgress.items[index];
        const oldValue = item.userValue;
        item.userValue = newValue;
        item.updatedAt = new Date().toISOString();

        // 保存数据
        saveData();

        // 更新显示
        const unitDisplay = item.unit ? item.unit : '';
        valueElement.innerHTML = `<strong class="fw-bold" style="cursor: pointer; color: var(--primary-color);">${newValue}${unitDisplay}</strong>`;

        // 重新渲染该卡片以更新进度条
        renderProgressItems();

        // 显示成功提示
        showToast(`数值已更新：${oldValue} → ${newValue}${unitDisplay}`, 'success');
    }

    // 恢复显示状态
    inputElement.style.display = 'none';
    valueElement.style.display = 'inline';
}

function cancelQuickEdit(inputElement, valueElement) {
    // 恢复原值
    const index = parseInt(inputElement.dataset.index);
    const field = inputElement.dataset.field;

    if (field === 'userValue' && index >= 0 && index < appData.dataProgress.items.length) {
        inputElement.value = appData.dataProgress.items[index].userValue;
    }

    // 恢复显示状态
    inputElement.style.display = 'none';
    valueElement.style.display = 'inline';
}

// 更新统计信息
function updateStatistics(itemsToDisplay = null) {
    const statsContainer = document.getElementById('statsContainer');
    if (!statsContainer) return;

    // 如果没有传入显示项目，使用全部项目
    const items = itemsToDisplay || appData.dataProgress.items || [];

    // 显示或隐藏统计容器
    if (items.length > 0) {
        statsContainer.style.display = 'block';
    } else {
        statsContainer.style.display = 'none';
        return;
    }

    // 计算统计数据
    const totalItems = items.length;
    let completedItems = 0;
    let totalProgress = 0;
    const usedCategories = new Set();

    items.forEach(item => {
        const progress = calculateProgress(item);
        totalProgress += progress;

        if (progress >= 100) {
            completedItems++;
        }

        if (item.categoryId) {
            usedCategories.add(item.categoryId);
        }
    });

    const avgProgress = totalItems > 0 ? Math.round(totalProgress / totalItems) : 0;
    const activeCategories = usedCategories.size;

    // 更新显示
    const totalItemsEl = document.getElementById('totalItems');
    const completedItemsEl = document.getElementById('completedItems');
    const avgProgressEl = document.getElementById('avgProgress');
    const activeCategoriesEl = document.getElementById('activeCategories');

    if (totalItemsEl) totalItemsEl.textContent = totalItems;
    if (completedItemsEl) completedItemsEl.textContent = completedItems;
    if (avgProgressEl) avgProgressEl.textContent = avgProgress + '%';
    if (activeCategoriesEl) activeCategoriesEl.textContent = activeCategories;
}

// 参数模板功能
function applyTemplate(templateType) {
    const templates = {
        weight: {
            name: '体重',
            unit: 'kg',
            userValue: 70,
            targetValue: 65,
            targetName: '理想体重'
        },
        savings: {
            name: '存款',
            unit: '元',
            userValue: 10000,
            targetValue: 50000,
            targetName: '年度存款目标'
        },
        reading: {
            name: '阅读',
            unit: '本',
            userValue: 5,
            targetValue: 24,
            targetName: '年度阅读计划'
        },
        exercise: {
            name: '跑步',
            unit: 'km',
            userValue: 0,
            targetValue: 100,
            targetName: '月度跑步目标'
        },
        study: {
            name: '学习时长',
            unit: '小时',
            userValue: 20,
            targetValue: 100,
            targetName: '月度学习目标'
        }
    };

    const template = templates[templateType];
    if (!template) return;

    // 填充表单
    document.getElementById('progressName').value = template.name;
    document.getElementById('progressUnit').value = template.unit;
    document.getElementById('progressUserValue').value = template.userValue;
    document.getElementById('progressTargetValue').value = template.targetValue;
    document.getElementById('progressTargetName').value = template.targetName;

    // 显示表单（如果隐藏的话）
    const addProgressForm = document.getElementById('addProgressForm');
    const addFormContent = document.querySelector('#addProgressForm .row');
    const toggleFormBtn = document.getElementById('toggleFormBtn');

    if (addFormContent && addFormContent.style.display === 'none') {
        addProgressForm.style.display = 'block';
        addFormContent.style.display = 'flex';
        toggleFormBtn.innerHTML = '<i class="fas fa-times me-1"></i>取消添加';
    }

    // 聚焦到参数名称字段
    document.getElementById('progressName').focus();

    showToast(`已应用${template.name}模板，请根据需要调整参数`, 'info');
}

