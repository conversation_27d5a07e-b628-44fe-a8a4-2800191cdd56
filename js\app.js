// app.js - 核心应用功能
// 提供全局数据管理、主题切换、动画效果和通用UI增强

// 全局应用数据对象，用于在不同模块间共享数据
const appData = {
    // 基础设置
    settings: {
        theme: localStorage.getItem('theme') || 'light',
        animations: localStorage.getItem('animations') !== 'false',
        toastDuration: 3000,
    },
    
    // 全局状态管理
    state: {
        isLoading: false,
        initialized: false
    }
};

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化应用
    initializeApp();
    
    // 增强UI
    enhanceUI();
    
    // 应用动画效果
    applyAnimations();
    
    // 设置主题切换功能
    setupThemeToggle();
});

// 初始化应用
function initializeApp() {
    console.log('初始化应用...');
    
    // 加载保存的应用数据
    loadAppData();
    
    // 标记应用已初始化
    appData.state.initialized = true;
    
    // 触发应用初始化完成事件
    document.dispatchEvent(new CustomEvent('appInitialized'));
}

// 加载应用数据
function loadAppData() {
    try {
        const savedData = localStorage.getItem('savingsData');
        if (savedData) {
            const parsedData = JSON.parse(savedData);
            
            // 合并保存的数据到appData
            Object.keys(parsedData).forEach(key => {
                appData[key] = parsedData[key];
            });
            
            console.log('应用数据加载成功');
        }
    } catch (error) {
        console.error('加载应用数据失败:', error);
    }
}

// 保存应用数据
function saveAppData() {
    try {
        localStorage.setItem('savingsData', JSON.stringify(appData));
        console.log('应用数据保存成功');
        
        // 触发数据更改事件
        const dataChangeEvent = new CustomEvent('appDataChanged', {
            detail: { source: 'appCore' }
        });
        window.dispatchEvent(dataChangeEvent);
    } catch (error) {
        console.error('保存应用数据失败:', error);
    }
}

// 添加新的参数比较项目
function addNewParameterComparison(item) {
    // 确保dataProgress对象存在
    if (!appData.dataProgress) {
        appData.dataProgress = {
            items: [],
            categories: []
        };
    }
    
    // 添加新项目
    appData.dataProgress.items.push(item);
    
    // 保存数据
    saveAppData();
    
    // 显示成功通知
    showToast('参数比较添加成功');
}

// 显示Toast通知
function showToast(message, type = 'info') {
    // 检查是否已经有toast容器
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        // 创建toast容器
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        toastContainer.style.zIndex = "1050";
        document.body.appendChild(toastContainer);
    }
    
    // 创建toast元素
    const toastId = `toast-${Date.now()}`;
    const toastEl = document.createElement('div');
    toastEl.className = 'toast';
    toastEl.id = toastId;
    toastEl.setAttribute('role', 'alert');
    toastEl.setAttribute('aria-live', 'assertive');
    toastEl.setAttribute('aria-atomic', 'true');
    
    // 根据类型选择图标和颜色
    let icon, colorClass;
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle me-2"></i>';
            colorClass = 'text-success';
            break;
        case 'error':
            icon = '<i class="fas fa-exclamation-circle me-2"></i>';
            colorClass = 'text-danger';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle me-2"></i>';
            colorClass = 'text-warning';
            break;
        case 'info':
        default:
            icon = '<i class="fas fa-info-circle me-2"></i>';
            colorClass = 'text-primary';
    }
    
    toastEl.innerHTML = `
        <div class="toast-header">
            <span class="${colorClass}">${icon}</span>
            <strong class="me-auto">通知</strong>
            <small class="text-muted">刚刚</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">
            ${message}
        </div>
    `;
    
    // 添加到容器
    toastContainer.appendChild(toastEl);
    
    // 初始化并显示toast
    const toast = new bootstrap.Toast(toastEl, {
        animation: true,
        autohide: true,
        delay: appData.settings.toastDuration
    });
    toast.show();
    
    // 自动移除toast元素
    toastEl.addEventListener('hidden.bs.toast', function() {
        toastEl.remove();
    });
}

// 增强UI
function enhanceUI() {
    // 添加标题打字机效果
    addTypingEffect();
    
    // 添加页面头部波浪效果
    addHeaderWaveEffect();
    
    // 添加进度卡片悬停效果
    addCardHoverEffects();
    
    // 添加主题切换开关到页面顶部
    addThemeToggle();
}

// 标题打字机效果
function addTypingEffect() {
    const headerTitle = document.querySelector('.header-title');
    if (!headerTitle) return;
    
    const originalText = headerTitle.textContent;
    headerTitle.textContent = '';
    
    const typeText = (text, i = 0) => {
        if (i < text.length) {
            headerTitle.textContent += text.charAt(i);
            setTimeout(() => typeText(text, i + 1), 100);
        }
    };
    
    // 延迟一小段时间开始打字效果
    setTimeout(() => typeText(originalText), 300);
}

// 页面头部波浪效果
function addHeaderWaveEffect() {
    const pageHeader = document.querySelector('.page-header');
    if (!pageHeader) return;
    
    // 创建波浪SVG
    const waveSvg = document.createElement('div');
    waveSvg.className = 'header-wave';
    waveSvg.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
            <path fill="rgba(255, 255, 255, 0.2)" fill-opacity="1" d="M0,192L48,176C96,160,192,128,288,122.7C384,117,480,139,576,154.7C672,171,768,181,864,154.7C960,128,1056,64,1152,48C1248,32,1344,64,1392,80L1440,96L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
        </svg>
    `;
    
    // 添加到页面头部
    pageHeader.appendChild(waveSvg);
    
    // 添加CSS样式
    const style = document.createElement('style');
    style.textContent = `
        .header-wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            overflow: hidden;
            line-height: 0;
            pointer-events: none;
        }
        
        .header-wave svg {
            position: relative;
            display: block;
            width: calc(100% + 1.3px);
            height: 40px;
            animation: waveAnimation 15s ease-in-out infinite alternate;
        }
        
        @keyframes waveAnimation {
            0% {
                transform: translateX(-25%);
            }
            50% {
                transform: translateX(0%);
            }
            100% {
                transform: translateX(-25%);
            }
        }
    `;
    document.head.appendChild(style);
}

// 添加进度卡片悬停效果
function addCardHoverEffects() {
    // 监听新添加的卡片，应用悬停效果
    const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            if (mutation.addedNodes.length) {
                mutation.addedNodes.forEach(node => {
                    if (node.classList && node.classList.contains('progress-card')) {
                        addEffectToCard(node);
                    }
                });
            }
        });
    });
    
    const container = document.getElementById('dataProgressContainer');
    if (container) {
        observer.observe(container, { childList: true, subtree: true });
    }
    
    // 对已存在的卡片应用效果
    document.querySelectorAll('.progress-card').forEach(card => {
        addEffectToCard(card);
    });
}

// 为单个卡片添加效果
function addEffectToCard(card) {
    // 添加进度条动画
    const progressBar = card.querySelector('.progress-bar');
    if (progressBar) {
        progressBar.style.transition = 'width 1s ease-in-out';
        progressBar.style.width = '0%';
        
        // 延迟一小段时间再显示进度
        setTimeout(() => {
            progressBar.style.width = progressBar.getAttribute('aria-valuenow') + '%';
        }, 300);
    }
    
    // 添加卡片进入动画
    card.style.opacity = '0';
    card.style.transform = 'translateY(20px)';
    card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
    
    setTimeout(() => {
        card.style.opacity = '1';
        card.style.transform = 'translateY(0)';
    }, 200);
}

// 应用动画效果
function applyAnimations() {
    // 添加按钮点击波纹效果
    addButtonRippleEffect();
    
    // 添加表单输入焦点效果
    addInputFocusEffect();
}

// 添加按钮点击波纹效果
function addButtonRippleEffect() {
    // 添加CSS样式
    const style = document.createElement('style');
    style.textContent = `
        .btn {
            position: relative;
            overflow: hidden;
        }
        
        .ripple {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.7);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        }
        
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
    
    // 为所有按钮添加波纹效果
    document.addEventListener('click', function(e) {
        const target = e.target;
        if (target.classList.contains('btn') || target.closest('.btn')) {
            const button = target.classList.contains('btn') ? target : target.closest('.btn');
            const diameter = Math.max(button.clientWidth, button.clientHeight);
            const radius = diameter / 2;
            
            const ripple = document.createElement('span');
            ripple.className = 'ripple';
            ripple.style.width = ripple.style.height = `${diameter}px`;
            
            const rect = button.getBoundingClientRect();
            ripple.style.left = `${e.clientX - rect.left - radius}px`;
            ripple.style.top = `${e.clientY - rect.top - radius}px`;
            
            button.appendChild(ripple);
            
            setTimeout(() => ripple.remove(), 600);
        }
    });
}

// 添加表单输入焦点效果
function addInputFocusEffect() {
    // 添加CSS样式
    const style = document.createElement('style');
    style.textContent = `
        .form-control, .form-select {
            transition: box-shadow 0.3s ease, transform 0.2s ease;
        }
        
        .form-control:focus, .form-select:focus {
            transform: translateY(-2px);
        }
    `;
    document.head.appendChild(style);
}

// 添加主题切换开关
function addThemeToggle() {
    const container = document.querySelector('.container');
    if (!container) return;
    
    // 创建主题切换开关
    const themeToggle = document.createElement('div');
    themeToggle.className = 'theme-toggle';
    themeToggle.innerHTML = `
        <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="themeSwitch">
            <label class="form-check-label" for="themeSwitch">
                <i class="fas fa-moon"></i>
            </label>
        </div>
    `;
    
    // 将开关插入页面
    container.insertBefore(themeToggle, container.firstChild);
    
    // 添加CSS样式
    const style = document.createElement('style');
    style.textContent = `
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 30px;
            padding: 5px 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .theme-toggle:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        }
        
        .form-check-input {
            cursor: pointer;
        }
        
        /* 深色模式样式 */
        body.dark-mode {
            background-color: #121212;
            color: #e0e0e0;
        }
        
        body.dark-mode .page-header {
            background: linear-gradient(135deg, #303f9f 0%, #1a237e 100%);
        }
        
        body.dark-mode .progress-card {
            background-color: #1e1e1e;
            color: #e0e0e0;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(0, 0, 0, 0.3);
        }
        
        body.dark-mode .progress-card .card-header {
            background-color: rgba(30, 30, 30, 0.8);
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        body.dark-mode .add-progress-form {
            background: linear-gradient(to right, #1e1e1e, #121212);
            border-left: 4px solid #303f9f;
            color: #e0e0e0;
        }
        
        body.dark-mode .filter-container {
            background: linear-gradient(to right, rgba(48, 63, 159, 0.15), rgba(30, 30, 30, 0.5));
            border: 1px solid rgba(48, 63, 159, 0.2);
            color: #e0e0e0;
        }
        
        body.dark-mode .form-control,
        body.dark-mode .form-select {
            background-color: #2d2d2d;
            border-color: #444;
            color: #e0e0e0;
        }
        
        body.dark-mode .form-control:focus,
        body.dark-mode .form-select:focus {
            border-color: #5c6bc0;
            box-shadow: 0 0 0 0.25rem rgba(92, 107, 192, 0.25);
            background-color: #333;
        }
        
        body.dark-mode .modal-content {
            background-color: #1e1e1e;
            color: #e0e0e0;
        }
        
        body.dark-mode .modal-header,
        body.dark-mode .modal-footer {
            background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
            border-color: rgba(255, 255, 255, 0.05);
        }
        
        body.dark-mode .toast {
            background-color: #2d2d2d;
            color: #e0e0e0;
        }
        
        body.dark-mode .toast-header {
            background-color: rgba(30, 30, 30, 0.8);
            color: #e0e0e0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        body.dark-mode .empty-state {
            background-color: #1e1e1e;
            color: #e0e0e0;
        }
    `;
    document.head.appendChild(style);
    
    // 获取保存的主题状态
    const savedTheme = localStorage.getItem('theme');
    const themeSwitch = document.getElementById('themeSwitch');
    
    // 根据保存的状态设置开关和主题
    if (savedTheme === 'dark') {
        themeSwitch.checked = true;
        document.body.classList.add('dark-mode');
    }
    
    // 监听主题切换
    themeSwitch.addEventListener('change', function() {
        if (this.checked) {
            document.body.classList.add('dark-mode');
            localStorage.setItem('theme', 'dark');
        } else {
            document.body.classList.remove('dark-mode');
            localStorage.setItem('theme', 'light');
        }
    });
}

// 设置主题切换功能
function setupThemeToggle() {
    // 获取保存的主题设置
    const savedTheme = localStorage.getItem('theme');
    
    // 应用主题
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-mode');
    }
}

// 当窗口加载完成后，为页面添加额外的动态效果
window.addEventListener('load', function() {
    // 为卡片添加交错动画
    const cards = document.querySelectorAll('.progress-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate__animated', 'animate__fadeInUp');
    });
    
    // 进度条加载动画 - 排除数据进度界面
    if (!document.title.includes('参数比较')) {
        setTimeout(() => {
            const progressBars = document.querySelectorAll('.progress-bar');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 100);
            });
        }, 500);
    }
}); 