// Cloudflare Worker代码 - 数据同步服务
// 在Cloudflare控制台创建KV命名空间：USERS 和 USER_DATA

// 处理请求的主函数
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

// 添加CORS头的辅助函数
function corsHeaders(origin) {
  return {
    'Access-Control-Allow-Origin': origin || '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Compressed, X-Description',
    'Access-Control-Max-Age': '86400',
  }
}

// 验证用户登录状态
async function validateAuth(token) {
  if (!token) return null
  
  // 简单的JWT验证 (格式: userid.timestamp.signature)
  try {
    const [userId, timestamp, signature] = token.split('.')
    
    // 获取用户信息
    const user = await USERS.get(userId, { type: 'json' })
    if (!user) return null
    
    // 检查签名 (实际应该使用更安全的方法)
    const expectedSignature = await crypto.subtle.digest(
      'SHA-256',
      new TextEncoder().encode(`${userId}.${timestamp}.${user.passwordHash}`)
    ).then(hash => Array.from(new Uint8Array(hash))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('')
    )
    
    // 验证签名和令牌有效期 (180天 = 半年)
    const now = Date.now()
    const tokenTime = parseInt(timestamp)
    const isValid = signature === expectedSignature &&
                   (now - tokenTime < 180 * 24 * 60 * 60 * 1000)
    
    return isValid ? userId : null
  } catch (e) {
    return null
  }
}

// 生成认证令牌
async function generateToken(userId, passwordHash) {
  const timestamp = Date.now().toString()
  
  // 创建签名
  const signature = await crypto.subtle.digest(
    'SHA-256',
    new TextEncoder().encode(`${userId}.${timestamp}.${passwordHash}`)
  ).then(hash => Array.from(new Uint8Array(hash))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('')
  )
  
  return `${userId}.${timestamp}.${signature}`
}

// 处理OPTIONS预检请求
function handleOptions(request) {
  const origin = request.headers.get('Origin')
  
  return new Response(null, {
    status: 204,
    headers: corsHeaders(origin)
  })
}

// 主请求处理函数
async function handleRequest(request) {
  const url = new URL(request.url)
  const origin = request.headers.get('Origin')
  
  // 处理预检请求
  if (request.method === 'OPTIONS') {
    return handleOptions(request)
  }
  
  // 路由处理
  const path = url.pathname
  
  try {
    // 用户注册
    if (path === '/register' && request.method === 'POST') {
      const { username, password } = await request.json()
      
      // 验证输入
      if (!username || !password || username.length < 3 || password.length < 6) {
        return new Response(JSON.stringify({ 
          error: '用户名至少3个字符，密码至少6个字符' 
        }), {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders(origin)
          }
        })
      }
      
      // 检查用户名是否存在
      const existingUserIds = await USERS.list({ prefix: `username:${username}:` })
      if (existingUserIds.keys.length > 0) {
        return new Response(JSON.stringify({ error: '用户名已存在' }), {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders(origin)
          }
        })
      }
      
      // 生成用户ID和密码哈希
      const userId = crypto.randomUUID()
      const passwordHash = await crypto.subtle.digest(
        'SHA-256',
        new TextEncoder().encode(password)
      ).then(hash => Array.from(new Uint8Array(hash))
        .map(b => b.toString(16).padStart(2, '0'))
        .join('')
      )
      
      // 存储用户信息
      await USERS.put(userId, JSON.stringify({
        username,
        passwordHash,
        createdAt: Date.now()
      }))
      
      // 创建用户名到ID的索引
      await USERS.put(`username:${username}:${userId}`, userId)
      
      // 生成认证令牌
      const token = await generateToken(userId, passwordHash)
      
      return new Response(JSON.stringify({ 
        userId, 
        username, 
        token 
      }), {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders(origin)
        }
      })
    }
    
    // 用户登录
    if (path === '/login' && request.method === 'POST') {
      const { username, password } = await request.json()
      
      // 通过用户名查找用户ID
      const userIdKeys = await USERS.list({ prefix: `username:${username}:` })
      
      if (userIdKeys.keys.length === 0) {
        return new Response(JSON.stringify({ error: '用户名或密码错误' }), {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders(origin)
          }
        })
      }
      
      const userId = await USERS.get(userIdKeys.keys[0].name)
      const user = await USERS.get(userId, { type: 'json' })
      
      if (!user) {
        return new Response(JSON.stringify({ error: '用户名或密码错误' }), {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders(origin)
          }
        })
      }
      
      // 验证密码
      const inputPasswordHash = await crypto.subtle.digest(
        'SHA-256',
        new TextEncoder().encode(password)
      ).then(hash => Array.from(new Uint8Array(hash))
        .map(b => b.toString(16).padStart(2, '0'))
        .join('')
      )
      
      if (inputPasswordHash !== user.passwordHash) {
        return new Response(JSON.stringify({ error: '用户名或密码错误' }), {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders(origin)
          }
        })
      }
      
      // 生成认证令牌
      const token = await generateToken(userId, user.passwordHash)
      
      return new Response(JSON.stringify({ 
        userId, 
        username: user.username, 
        token 
      }), {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders(origin)
        }
      })
    }
    
    // 获取用户数据版本列表
    if (path.startsWith('/data/') && path.endsWith('/versions') && request.method === 'GET') {
      const authToken = request.headers.get('Authorization')
      const userId = await validateAuth(authToken)

      if (!userId) {
        return new Response(JSON.stringify({ error: '未授权' }), {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders(origin)
          }
        })
      }

      // 获取用户的所有数据版本
      const versionsList = await USER_DATA.list({ prefix: `${userId}_v_` })
      const versions = []

      // 添加历史版本
      for (const key of versionsList.keys) {
        const versionId = key.name.replace(`${userId}_v_`, '')
        const metadata = await USER_DATA.getWithMetadata(key.name)

        versions.push({
          id: versionId,
          timestamp: parseInt(versionId),
          date: new Date(parseInt(versionId)).toISOString(),
          size: metadata.metadata?.size || 0,
          description: metadata.metadata?.description || 'Auto Backup'
        })
      }

      // 检查是否有主版本数据（兼容旧数据）
      const mainData = await USER_DATA.getWithMetadata(userId)
      if (mainData && mainData.value) {
        const mainDataSize = new Blob([mainData.value]).size
        const mainTimestamp = Date.now() // 使用当前时间作为主版本时间戳

        versions.push({
          id: 'latest',
          timestamp: mainTimestamp,
          date: new Date(mainTimestamp).toISOString(),
          size: mainDataSize,
          description: 'Current Latest Version'
        })
      }

      // 按时间倒序排列，最新的在前面
      versions.sort((a, b) => b.timestamp - a.timestamp)

      return new Response(JSON.stringify({ versions }), {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders(origin)
        }
      })
    }

    // 获取特定版本的用户数据
    if (path.startsWith('/data/') && path.includes('/version/') && request.method === 'GET') {
      const authToken = request.headers.get('Authorization')
      const userId = await validateAuth(authToken)

      if (!userId) {
        return new Response(JSON.stringify({ error: '未授权' }), {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders(origin)
          }
        })
      }

      // 从URL中提取版本ID
      const versionId = path.split('/version/')[1]
      let data = null

      if (versionId === 'latest') {
        // 获取主版本数据（最新版本）
        data = await USER_DATA.get(userId)
      } else {
        // 获取历史版本数据
        const versionKey = `${userId}_v_${versionId}`
        data = await USER_DATA.get(versionKey)
      }

      if (!data) {
        return new Response(JSON.stringify({ error: '版本不存在' }), {
          status: 404,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders(origin)
          }
        })
      }

      return new Response(data, {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders(origin)
        }
      })
    }

    // 获取用户数据（最新版本）
    if (path.startsWith('/data/') && request.method === 'GET') {
      const authToken = request.headers.get('Authorization')
      const userId = await validateAuth(authToken)

      if (!userId) {
        return new Response(JSON.stringify({ error: '未授权' }), {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders(origin)
          }
        })
      }

      // 获取用户数据（最新版本）
      const data = await USER_DATA.get(userId)

      return new Response(data || '{}', {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders(origin)
        }
      })
    }
    
    // 保存用户数据（支持版本化）
    if (path.startsWith('/data/') && request.method === 'POST') {
      const authToken = request.headers.get('Authorization')
      const userId = await validateAuth(authToken)

      if (!userId) {
        return new Response(JSON.stringify({ error: '未授权' }), {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders(origin)
          }
        })
      }

      const data = await request.text()
      const timestamp = Date.now()

      // 获取请求头中的描述信息
      const description = request.headers.get('X-Description') || 'Auto Backup'
      const dataSize = new Blob([data]).size

      try {
        // 1. 保存为最新版本
        await USER_DATA.put(userId, data)

        // 2. 保存为历史版本
        const versionKey = `${userId}_v_${timestamp}`
        await USER_DATA.put(versionKey, data, {
          metadata: {
            size: dataSize,
            description: description,
            timestamp: timestamp
          }
        })

        // 3. 清理旧版本（保留最近3个版本）
        const versionsList = await USER_DATA.list({ prefix: `${userId}_v_` })
        if (versionsList.keys.length > 3) {
          // 按时间排序，删除最旧的版本
          const sortedVersions = versionsList.keys
            .map(key => ({
              key: key.name,
              timestamp: parseInt(key.name.replace(`${userId}_v_`, ''))
            }))
            .sort((a, b) => b.timestamp - a.timestamp)

          // 删除超过3个的旧版本
          for (let i = 3; i < sortedVersions.length; i++) {
            await USER_DATA.delete(sortedVersions[i].key)
          }
        }

        return new Response(JSON.stringify({
          success: true,
          versionId: timestamp,
          versionsCount: Math.min(versionsList.keys.length + 1, 3)
        }), {
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders(origin)
          }
        })
      } catch (error) {
        return new Response(JSON.stringify({
          error: '保存数据失败: ' + error.message
        }), {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders(origin)
          }
        })
      }
    }
    
    // 未找到路由
    return new Response(JSON.stringify({ error: '未找到' }), {
      status: 404,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders(origin)
      }
    })
    
  } catch (error) {
    return new Response(JSON.stringify({ error: '服务器错误', message: error.message }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders(origin)
      }
    })
  }
} 