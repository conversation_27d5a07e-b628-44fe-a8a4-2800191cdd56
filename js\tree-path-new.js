// 树状分支图 - JavaScript 功能实现
console.log('tree-path.js loading...');

// 全局变量
let treeData = [];
let selectedNode = null;
let nodeIdCounter = 1;
let currentZoom = 1;
let panX = 0;
let panY = 0;
let isDragging = false;
let isPathMode = false;
let currentPath = [];
let searchResults = [];
let maxDepthFilter = null;
let editingNode = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing...');
    initializeTree();
    setupEventListeners();
});

// 初始化树状图
function initializeTree() {
    console.log('Initializing tree...');

    // 尝试从主系统数据中加载
    loadTreeFromMainSystem();

    // 如果没有数据，初始化为空
    if (treeData.length === 0) {
        treeData = [];
        nodeIdCounter = 1;
    }

    selectedNode = null;
    editingNode = null;
    currentPath = [];
    searchResults = [];
    maxDepthFilter = null;
    currentZoom = 1;
    panX = 0;
    panY = 0;
    isPathMode = false;
    renderTree();
}

// 从主系统加载树状图数据
function loadTreeFromMainSystem() {
    try {
        const savedData = localStorage.getItem('savingsData');
        if (savedData) {
            const parsedData = JSON.parse(savedData);
            if (parsedData.lifePathTree) {
                treeData = parsedData.lifePathTree.data || [];
                nodeIdCounter = parsedData.lifePathTree.nodeIdCounter || 1;

                // 恢复路径模式状态
                if (parsedData.lifePathTree.isPathMode !== undefined) {
                    isPathMode = parsedData.lifePathTree.isPathMode;

                    // 更新路径模式按钮状态
                    const pathModeButton = document.querySelector('button[onclick="togglePathMode()"]');
                    if (pathModeButton) {
                        if (isPathMode) {
                            pathModeButton.classList.add('btn-primary');
                            pathModeButton.classList.remove('btn-outline-primary');
                        } else {
                            pathModeButton.classList.remove('btn-primary');
                            pathModeButton.classList.add('btn-outline-primary');
                        }
                    }
                }

                // 恢复当前路径和选中节点
                if (parsedData.lifePathTree.currentPath && parsedData.lifePathTree.currentPath.length > 0) {
                    currentPath = parsedData.lifePathTree.currentPath;

                    // 恢复选中的节点（路径的最后一个节点）
                    if (currentPath.length > 0) {
                        const lastNodeId = currentPath[currentPath.length - 1];
                        selectedNode = findNodeById(lastNodeId);

                        // 如果在路径模式下，显示路径信息
                        if (isPathMode && selectedNode) {
                            updatePathInfo();
                        }
                    }
                }

                console.log('从主系统加载树状图数据成功');
            }
        }
    } catch (error) {
        console.error('从主系统加载树状图数据失败:', error);
    }
}

// 保存树状图数据到主系统
function saveTreeToMainSystem() {
    try {
        // 获取现有的主系统数据
        let savedData = {};
        const existingData = localStorage.getItem('savingsData');
        if (existingData) {
            savedData = JSON.parse(existingData);
        }

        // 更新树状图数据
        savedData.lifePathTree = {
            data: treeData,
            nodeIdCounter: nodeIdCounter,
            isPathMode: isPathMode,
            currentPath: currentPath,
            selectedNodeId: selectedNode ? selectedNode.id : null,
            lastUpdated: new Date().toISOString()
        };

        // 保存回主系统
        localStorage.setItem('savingsData', JSON.stringify(savedData));
        console.log('树状图数据已保存到主系统');

        // 更新同步状态
        updateSyncStatus('success');

        // 触发数据更改事件
        const dataChangeEvent = new CustomEvent('appDataChanged', {
            detail: { source: 'lifePathTree' }
        });
        document.dispatchEvent(dataChangeEvent);

        return true;
    } catch (error) {
        console.error('保存树状图数据到主系统失败:', error);
        return false;
    }
}

// 渲染树状图
function renderTree() {
    console.log('Rendering tree...');
    const svg = document.getElementById('treeSvg');
    const treeGroup = document.getElementById('treeGroup');
    const emptyState = document.getElementById('emptyState');
    
    if (!svg || !treeGroup || !emptyState) {
        console.error('Required elements not found');
        return;
    }
    
    if (treeData.length === 0) {
        emptyState.style.display = 'block';
        treeGroup.innerHTML = '';
        return;
    }
    
    emptyState.style.display = 'none';
    
    // 自动布局
    if (treeData.length > 0) {
        calculateLayout(treeData[0]);
    }
    
    // 清空并重新渲染
    treeGroup.innerHTML = '';
    
    // 设置变换
    treeGroup.setAttribute('transform', `translate(${panX}, ${panY}) scale(${currentZoom})`);
    
    // 渲染连线
    if (treeData.length > 0) {
        renderLinks(treeData[0], treeGroup);
    }
    
    // 渲染节点
    if (treeData.length > 0) {
        renderNodes(treeData[0], treeGroup);
    }
    
    // 更新缩放显示
    updateZoomDisplay();
}

// 计算自动布局
function calculateLayout(node, depth = 0, parentX = 0, parentY = 0) {
    const levelHeight = 140; // 增加层级间距
    const minNodeSpacing = 180; // 最小节点间距

    // 设置当前节点的深度
    node.depth = depth;

    if (depth === 0) {
        // 根节点居中
        node.x = 400;
        node.y = 80;
    }

    if (node.children && node.children.length > 0) {
        // 先递归计算所有子树的宽度
        const childWidths = node.children.map(child => calculateSubtreeWidth(child));
        const totalWidth = childWidths.reduce((sum, width) => sum + width, 0) +
                          (node.children.length - 1) * minNodeSpacing;

        // 计算起始位置
        let currentX = node.x - totalWidth / 2;

        node.children.forEach((child, index) => {
            // 计算子节点位置
            child.x = currentX + childWidths[index] / 2;
            child.y = node.y + levelHeight;

            // 递归布局子树
            calculateLayout(child, depth + 1, child.x, child.y);

            // 移动到下一个位置
            currentX += childWidths[index] + minNodeSpacing;
        });
    }
}

// 计算子树宽度
function calculateSubtreeWidth(node) {
    if (!node.children || node.children.length === 0) {
        return 160; // 叶子节点的基础宽度
    }

    const minNodeSpacing = 180;
    const childWidths = node.children.map(child => calculateSubtreeWidth(child));
    const subtreeWidth = childWidths.reduce((sum, width) => sum + width, 0) +
                        (node.children.length - 1) * minNodeSpacing;

    return Math.max(160, subtreeWidth); // 至少保证节点本身的宽度
}

// 渲染连线
function renderLinks(node, container) {
    if (node.children && node.children.length > 0) {
        node.children.forEach(child => {
            // 检查深度过滤
            if (maxDepthFilter && child.depth > maxDepthFilter) return;
            
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            
            // 创建连接路径
            const startX = node.x;
            const startY = node.y + 20; // 节点底部
            const endX = child.x;
            const endY = child.y - 20; // 节点顶部

            let pathData;

            // 如果是垂直对齐，使用直线；否则使用贝塞尔曲线
            if (Math.abs(startX - endX) < 5) {
                // 垂直直线
                pathData = `M ${startX} ${startY} L ${endX} ${endY}`;
            } else {
                // 贝塞尔曲线
                const controlY = startY + (endY - startY) / 2;
                pathData = `M ${startX} ${startY} C ${startX} ${controlY} ${endX} ${controlY} ${endX} ${endY}`;
            }
            
            path.setAttribute('d', pathData);
            path.setAttribute('class', 'tree-link');
            
            // 路径高亮
            if (isPathMode && isNodeInCurrentPath(node) && isNodeInCurrentPath(child)) {
                path.classList.add('highlighted');
            } else if (isPathMode) {
                path.classList.add('dimmed');
            }
            
            container.appendChild(path);
            
            // 递归渲染子连线
            renderLinks(child, container);
        });
    }
}

// 渲染节点
function renderNodes(node, container) {
    // 检查深度过滤
    if (maxDepthFilter && node.depth > maxDepthFilter) {
        console.log(`节点 ${node.text} 被深度过滤器过滤，深度: ${node.depth}, 过滤器: ${maxDepthFilter}`);
        return;
    }
    
    const nodeGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    nodeGroup.setAttribute('class', 'tree-node');
    nodeGroup.setAttribute('transform', `translate(${node.x - 60}, ${node.y - 20})`);
    nodeGroup.setAttribute('data-node-id', node.id);
    
    // 添加编辑状态类
    if (editingNode && editingNode.id === node.id) {
        nodeGroup.classList.add('editing');
    }
    
    // 节点背景
    const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    rect.setAttribute('class', 'node-rect');
    rect.setAttribute('width', '120');
    rect.setAttribute('height', '40');
    rect.setAttribute('rx', '20');
    
    // 根据节点类型和状态设置颜色
    let fillColor = node.isRoot ? '#667eea' : '#28a745';
    if (selectedNode && selectedNode.id === node.id) {
        nodeGroup.classList.add('selected');
    }
    if (searchResults.includes(node.id)) {
        fillColor = '#ffc107';
    }
    if (isPathMode) {
        if (isNodeInCurrentPath(node)) {
            nodeGroup.classList.add('highlighted');
        } else {
            nodeGroup.classList.add('dimmed');
        }
    }
    
    rect.setAttribute('fill', fillColor);
    rect.setAttribute('stroke', '#ffffff');
    rect.setAttribute('stroke-width', '2');
    nodeGroup.appendChild(rect);
    
    // 节点文本或输入框
    if (editingNode && editingNode.id === node.id) {
        // 编辑模式：显示输入框
        const foreignObject = document.createElementNS('http://www.w3.org/2000/svg', 'foreignObject');
        foreignObject.setAttribute('x', '5');
        foreignObject.setAttribute('y', '10');
        foreignObject.setAttribute('width', '110');
        foreignObject.setAttribute('height', '20');
        
        const input = document.createElement('input');
        input.className = 'node-input';
        input.value = node.text;
        input.addEventListener('blur', () => finishEditing(node, input.value));
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                finishEditing(node, input.value);
            } else if (e.key === 'Escape') {
                cancelEditing();
            }
        });
        
        foreignObject.appendChild(input);
        nodeGroup.appendChild(foreignObject);
        
        // 自动聚焦
        setTimeout(() => {
            input.focus();
            input.select();
        }, 100);
    } else {
        // 正常模式：显示文本
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('class', 'node-label');
        text.setAttribute('x', '60');
        text.setAttribute('y', '25');
        text.textContent = node.text.length > 12 ? node.text.substring(0, 12) + '...' : node.text;
        nodeGroup.appendChild(text);
    }
    
    // 深度指示器
    if (node.depth > 0) {
        const depthText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        depthText.setAttribute('class', 'depth-indicator');
        depthText.setAttribute('x', '110');
        depthText.setAttribute('y', '15');
        depthText.textContent = `L${node.depth}`;
        nodeGroup.appendChild(depthText);
    }

    // 控制按钮组（悬停时显示）
    if (!editingNode || editingNode.id !== node.id) {
        const controlsGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        controlsGroup.setAttribute('class', 'node-controls');

        // 添加子节点按钮
        const addBtnGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        addBtnGroup.style.cursor = 'pointer';

        const addBtn = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        addBtn.setAttribute('class', 'node-control-btn add-child-btn');
        addBtn.setAttribute('cx', '130');
        addBtn.setAttribute('cy', '10');
        addBtn.setAttribute('r', '8');
        addBtn.setAttribute('fill', '#28a745');
        addBtnGroup.appendChild(addBtn);

        const addIcon = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        addIcon.setAttribute('x', '130');
        addIcon.setAttribute('y', '14');
        addIcon.setAttribute('text-anchor', 'middle');
        addIcon.setAttribute('fill', 'white');
        addIcon.setAttribute('font-size', '10');
        addIcon.setAttribute('font-weight', 'bold');
        addIcon.textContent = '+';
        addBtnGroup.appendChild(addIcon);

        addBtnGroup.addEventListener('click', (e) => {
            e.stopPropagation();
            addChildNodeInline(node);
        });
        controlsGroup.appendChild(addBtnGroup);

        // 编辑按钮
        const editBtnGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        editBtnGroup.style.cursor = 'pointer';

        const editBtn = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        editBtn.setAttribute('class', 'node-control-btn edit-btn');
        editBtn.setAttribute('cx', '130');
        editBtn.setAttribute('cy', '25');
        editBtn.setAttribute('r', '8');
        editBtn.setAttribute('fill', '#ffc107');
        editBtnGroup.appendChild(editBtn);

        const editIcon = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        editIcon.setAttribute('x', '130');
        editIcon.setAttribute('y', '29');
        editIcon.setAttribute('text-anchor', 'middle');
        editIcon.setAttribute('fill', 'white');
        editIcon.setAttribute('font-size', '8');
        editIcon.textContent = '✎';
        editBtnGroup.appendChild(editIcon);

        editBtnGroup.addEventListener('click', (e) => {
            e.stopPropagation();
            startEditing(node);
        });
        controlsGroup.appendChild(editBtnGroup);

        // 删除按钮（非根节点）
        if (!node.isRoot) {
            const deleteBtnGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
            deleteBtnGroup.style.cursor = 'pointer';

            const deleteBtn = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            deleteBtn.setAttribute('class', 'node-control-btn delete-btn');
            deleteBtn.setAttribute('cx', '130');
            deleteBtn.setAttribute('cy', '40');
            deleteBtn.setAttribute('r', '8');
            deleteBtn.setAttribute('fill', '#dc3545');
            deleteBtnGroup.appendChild(deleteBtn);

            const deleteIcon = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            deleteIcon.setAttribute('x', '130');
            deleteIcon.setAttribute('y', '44');
            deleteIcon.setAttribute('text-anchor', 'middle');
            deleteIcon.setAttribute('fill', 'white');
            deleteIcon.setAttribute('font-size', '10');
            deleteIcon.textContent = '×';
            deleteBtnGroup.appendChild(deleteIcon);

            deleteBtnGroup.addEventListener('click', (e) => {
                e.stopPropagation();
                deleteNodeInline(node);
            });
            controlsGroup.appendChild(deleteBtnGroup);
        }

        nodeGroup.appendChild(controlsGroup);
    }

    // 添加事件监听
    nodeGroup.addEventListener('click', () => selectNode(node));
    nodeGroup.addEventListener('dblclick', () => startEditing(node));

    container.appendChild(nodeGroup);
    
    // 递归渲染子节点
    if (node.children && node.children.length > 0) {
        node.children.forEach(child => renderNodes(child, container));
    }
}

// 添加根节点
function addRootNode() {
    console.log('addRootNode called');
    if (treeData.length > 0) {
        if (!confirm('已存在根节点，是否要替换？这将清空所有数据。')) {
            return;
        }
    }

    const newNode = {
        id: nodeIdCounter++,
        text: '人生起点',
        isRoot: true,
        children: []
    };

    treeData = [newNode];
    selectedNode = newNode;
    editingNode = null;
    currentPath = [];

    renderTree();

    // 自动保存到主系统
    saveTreeToMainSystem();

    // 立即开始编辑新根节点
    setTimeout(() => {
        startEditing(newNode);
    }, 100);
}

// 其他必要的辅助函数
function selectNode(node) {
    selectedNode = node;

    if (isPathMode) {
        // 路径模式下，构建从根到当前节点的路径
        currentPath = getPathToNode(node);
        updatePathInfo();
    }

    renderTree();
}

// 内联添加子节点
function addChildNodeInline(parentNode) {
    const newNode = {
        id: nodeIdCounter++,
        text: '新选择',
        children: []
    };

    if (!parentNode.children) {
        parentNode.children = [];
    }
    parentNode.children.push(newNode);

    // 重新计算布局
    calculateLayout(treeData[0]);
    renderTree();

    // 自动保存到主系统
    saveTreeToMainSystem();

    // 立即开始编辑新节点
    setTimeout(() => {
        startEditing(newNode);
    }, 100);
}

// 内联删除节点
function deleteNodeInline(nodeToDelete) {
    if (nodeToDelete.isRoot) {
        return; // 不能删除根节点
    }

    // 确认删除
    if (nodeToDelete.children && nodeToDelete.children.length > 0) {
        if (!confirm(`删除"${nodeToDelete.text}"将同时删除其所有子节点，确定继续吗？`)) {
            return;
        }
    }

    // 从树中删除节点
    deleteNodeFromTree(treeData[0], nodeToDelete.id);

    // 如果删除的是选中节点，清除选择
    if (selectedNode && selectedNode.id === nodeToDelete.id) {
        selectedNode = null;
    }

    // 如果删除的是编辑节点，取消编辑
    if (editingNode && editingNode.id === nodeToDelete.id) {
        editingNode = null;
    }

    renderTree();
}

// 从树中删除指定节点
function deleteNodeFromTree(node, targetId) {
    if (node.children) {
        node.children = node.children.filter(child => {
            if (child.id === targetId) {
                return false;
            }
            deleteNodeFromTree(child, targetId);
            return true;
        });
    }
}

function startEditing(node) {
    editingNode = node;
    renderTree();

    // 自动保存到主系统
    saveTreeToMainSystem();
}

function finishEditing(node, newText) {
    if (newText && newText.trim() && newText.trim() !== node.text) {
        node.text = newText.trim();
        // 自动保存到主系统
        saveTreeToMainSystem();
    }
    editingNode = null;
    renderTree();
}

function cancelEditing() {
    editingNode = null;
    renderTree();
}

function isNodeInCurrentPath(node) {
    return currentPath.includes(node.id);
}

function updateZoomDisplay() {
    const zoomLevel = document.getElementById('zoomLevel');
    if (zoomLevel) {
        zoomLevel.textContent = Math.round(currentZoom * 100) + '%';
    }
}

function setupEventListeners() {
    console.log('Setting up event listeners...');
    const svg = document.getElementById('treeSvg');

    // 鼠标拖拽平移
    let isMouseDown = false;
    let lastMouseX = 0;
    let lastMouseY = 0;

    svg.addEventListener('mousedown', (e) => {
        // 检查是否点击的是节点或控制按钮
        const clickedNode = e.target.closest('.tree-node');
        const clickedControl = e.target.closest('.node-controls');

        if (!clickedNode && !clickedControl) {
            // 点击空白区域，取消选择
            selectedNode = null;
            renderTree();
        }

        if (e.target === svg || e.target.closest('#treeGroup')) {
            isMouseDown = true;
            lastMouseX = e.clientX;
            lastMouseY = e.clientY;
            svg.style.cursor = 'grabbing';
        }
    });

    svg.addEventListener('mousemove', (e) => {
        if (isMouseDown) {
            const deltaX = e.clientX - lastMouseX;
            const deltaY = e.clientY - lastMouseY;

            panX += deltaX;
            panY += deltaY;

            lastMouseX = e.clientX;
            lastMouseY = e.clientY;

            renderTree();
        }
    });

    svg.addEventListener('mouseup', () => {
        isMouseDown = false;
        svg.style.cursor = 'grab';
    });

    svg.addEventListener('mouseleave', () => {
        isMouseDown = false;
        svg.style.cursor = 'grab';
    });

    // 鼠标滚轮缩放
    svg.addEventListener('wheel', (e) => {
        e.preventDefault();

        const rect = svg.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
        const newZoom = Math.max(0.3, Math.min(3, currentZoom * zoomFactor));

        if (newZoom !== currentZoom) {
            // 以鼠标位置为中心缩放
            const zoomRatio = newZoom / currentZoom;
            panX = mouseX - (mouseX - panX) * zoomRatio;
            panY = mouseY - (mouseY - panY) * zoomRatio;

            currentZoom = newZoom;
            renderTree();
        }
    });
}

// 适应屏幕功能
function fitToScreen() {
    console.log('fitToScreen called');
    if (treeData.length === 0) return;

    // 计算所有节点的边界
    let minX = Infinity, maxX = -Infinity;
    let minY = Infinity, maxY = -Infinity;

    function getBounds(node) {
        minX = Math.min(minX, node.x - 80); // 增加边距
        maxX = Math.max(maxX, node.x + 80);
        minY = Math.min(minY, node.y - 30);
        maxY = Math.max(maxY, node.y + 30);

        if (node.children) {
            node.children.forEach(child => getBounds(child));
        }
    }

    getBounds(treeData[0]);

    const svg = document.getElementById('treeSvg');
    const svgRect = svg.getBoundingClientRect();

    const contentWidth = maxX - minX;
    const contentHeight = maxY - minY;

    // 增加边距
    const margin = 50;
    const scaleX = (svgRect.width - margin * 2) / contentWidth;
    const scaleY = (svgRect.height - margin * 2) / contentHeight;

    // 限制最大缩放比例，避免节点过大
    currentZoom = Math.min(scaleX, scaleY, 1.5);

    // 居中显示
    panX = (svgRect.width / 2) - ((minX + maxX) / 2) * currentZoom;
    panY = (svgRect.height / 2) - ((minY + maxY) / 2) * currentZoom;

    renderTree();
}

function togglePathMode() {
    console.log('togglePathMode called');
    isPathMode = !isPathMode;
    const button = event.target.closest('button');

    if (isPathMode) {
        button.classList.add('btn-primary');
        button.classList.remove('btn-outline-primary');
        // 如果有保存的路径，显示它；否则使用当前选中节点的路径
        if (currentPath.length > 0) {
            updatePathInfo();
        } else if (selectedNode) {
            currentPath = getPathToNode(selectedNode);
            updatePathInfo();
        }
    } else {
        button.classList.remove('btn-primary');
        button.classList.add('btn-outline-primary');
        // 不清空路径，只是隐藏路径信息面板
        const pathInfo = document.getElementById('pathInfo');
        if (pathInfo) {
            pathInfo.classList.remove('show');
        }
    }

    renderTree();
}

// 根据ID查找节点
function findNodeById(nodeId) {
    function searchNode(node) {
        if (node.id === nodeId) {
            return node;
        }

        if (node.children) {
            for (let child of node.children) {
                const found = searchNode(child);
                if (found) {
                    return found;
                }
            }
        }

        return null;
    }

    if (treeData.length > 0) {
        return searchNode(treeData[0]);
    }

    return null;
}

// 获取到指定节点的路径
function getPathToNode(targetNode) {
    const path = [];

    function findPath(node, currentPath) {
        currentPath.push(node);

        if (node.id === targetNode.id) {
            return true;
        }

        if (node.children) {
            for (let child of node.children) {
                if (findPath(child, currentPath)) {
                    return true;
                }
            }
        }

        currentPath.pop();
        return false;
    }

    if (treeData.length > 0) {
        findPath(treeData[0], path);
    }

    return path.map(node => node.id);
}

// 更新路径信息
function updatePathInfo() {
    const pathInfo = document.getElementById('pathInfo');
    const pathContent = document.getElementById('pathContent');

    if (!pathInfo || !pathContent) return;

    if (currentPath.length > 0) {
        // 将ID数组转换为节点对象数组
        const pathNodes = currentPath.map(nodeId => findNodeById(nodeId)).filter(node => node !== null);
        const pathText = pathNodes.map(node => node.text).join(' → ');
        pathContent.innerHTML = `
            <div class="mb-2"><strong>路径长度:</strong> ${currentPath.length} 步</div>
            <div><strong>路径:</strong> ${pathText}</div>
        `;
        pathInfo.classList.add('show');
    } else {
        pathInfo.classList.remove('show');
    }
}



function resetTree() {
    console.log('resetTree called');
    if (confirm('确定要重置树状图吗？这将清空所有数据。')) {
        // 清空数据
        treeData = [];
        nodeIdCounter = 1;
        selectedNode = null;
        editingNode = null;
        currentPath = [];
        searchResults = [];
        maxDepthFilter = null;
        currentZoom = 1;
        panX = 0;
        panY = 0;
        isPathMode = false;

        // 清除主系统中的数据
        saveTreeToMainSystem();

        renderTree();
    }
}

function searchNodes() {
    console.log('searchNodes called');
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    searchResults = [];

    if (searchTerm.trim() === '') {
        renderTree();
        return;
    }

    function searchInNode(node) {
        if (node.text.toLowerCase().includes(searchTerm)) {
            searchResults.push(node.id);
        }
        if (node.children) {
            node.children.forEach(child => searchInNode(child));
        }
    }

    if (treeData.length > 0) {
        searchInNode(treeData[0]);
    }

    renderTree();
}

function filterByDepth() {
    console.log('filterByDepth called');
    const depthFilter = document.getElementById('depthFilter').value;
    maxDepthFilter = depthFilter ? parseInt(depthFilter) : null;
    renderTree();
}

function zoomIn() {
    console.log('zoomIn called');
    currentZoom = Math.min(3, currentZoom * 1.2);
    renderTree();
}

function zoomOut() {
    console.log('zoomOut called');
    currentZoom = Math.max(0.3, currentZoom / 1.2);
    renderTree();
}

// 自动整理布局
function autoLayout() {
    console.log('autoLayout called');
    if (treeData.length > 0) {
        // 重新计算布局
        calculateLayout(treeData[0]);
        renderTree();

        // 自动适应屏幕
        setTimeout(() => {
            fitToScreen();
        }, 100);
    }
}

// 更新同步状态显示
function updateSyncStatus(status) {
    const syncStatus = document.getElementById('syncStatus');
    if (!syncStatus) return;

    switch(status) {
        case 'success':
            syncStatus.innerHTML = `
                <i class="fas fa-check-circle text-success"></i>
                <small class="text-muted">已同步</small>
            `;
            break;
        case 'saving':
            syncStatus.innerHTML = `
                <i class="fas fa-sync-alt fa-spin text-primary"></i>
                <small class="text-muted">同步中...</small>
            `;
            break;
        case 'error':
            syncStatus.innerHTML = `
                <i class="fas fa-exclamation-circle text-danger"></i>
                <small class="text-muted">同步失败</small>
            `;
            break;
    }
}

console.log('All functions defined');
