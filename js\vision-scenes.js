// 脑中画面子系统的JavaScript功能实现

// 配置常量
const VISION_CONFIG = {
    STORAGE_KEY: 'visionScenesData',
    DEBUG: false  // 开启后会显示更多日志
};

// 数据结构
let visionData = {
    scenes: [],
    lastViewMode: 'tree', // 默认视图模式改为记忆树
    currentTimeFilter: 'past' // 当前时间筛选
};

// DOM 元素缓存
let domElements = {};

// =============== 初始化函数 ===============
function initVisionScenes() {
    if (VISION_CONFIG.DEBUG) console.log('初始化脑中画面子系统');
    
    // 缓存常用 DOM 元素
    cacheElements();
    
    // 加载数据
    loadVisionData();
    
    // 初始化视图之前先设置视图模式
    setInitialViewMode();
    
    // 添加事件监听器
    attachEventListeners();
    
    // 初始化视图
    initializeView();
    
    // 添加返回顶部按钮
    addScrollToTopButton();
}

// 全面初始化视图
function initializeView() {
    // 设置初始视图模式
    setInitialViewMode();

    // 设置初始时间筛选
    setInitialTimeFilter();
    
    // 确保数据已加载并且视图可见
    setTimeout(() => {
        // 如果视图为空而数据不为空，重新渲染
        if (visionData.scenes.length > 0) {
            if (VISION_CONFIG.DEBUG) console.log('初始化视图: tree');

            // 强制清空视图并重新渲染
            if (domElements.treeView) {
                domElements.treeView.innerHTML = '';
                renderTreeView();
            }
        }

        // 初始化父记忆选择列表
        updateParentSceneOptions();
    }, 300);
}

// 设置初始视图模式
function setInitialViewMode() {
    // 从localStorage中读取上次使用的视图模式
    try {
        const savedData = localStorage.getItem(VISION_CONFIG.STORAGE_KEY);
        if (savedData) {
            const parsedData = JSON.parse(savedData);
            if (parsedData.lastViewMode) {
                const viewModeElement = document.getElementById(parsedData.lastViewMode + 'Mode');
                if (viewModeElement) {
                    // 先将所有视图模式的选中状态清除
                    document.querySelectorAll('input[name="viewMode"]').forEach(element => {
                        element.checked = false;
                    });
                    // 设置上次使用的视图模式为选中状态
                    viewModeElement.checked = true;
                    
                    // 保存为当前模式
                    visionData.lastViewMode = parsedData.lastViewMode;
                    
                    if (VISION_CONFIG.DEBUG) console.log('恢复上次的视图模式:', parsedData.lastViewMode);
                }
            }
        }
    } catch (error) {
        console.error('读取上次视图模式失败', error);
    }
    
    // 如果没有选中任何视图，默认选择树状视图
    if (!document.querySelector('input[name="viewMode"]:checked')) {
        domElements.treeMode.checked = true;
        visionData.lastViewMode = 'tree';
    }
    
    // 根据当前选中的视图设置显示/隐藏
    setViewVisibility();
}

// 设置初始时间筛选
function setInitialTimeFilter() {
    // 从保存的数据中恢复时间筛选
    const savedFilter = visionData.currentTimeFilter || 'past';

    // 设置对应的单选按钮为选中状态
    const filterRadio = document.getElementById(savedFilter + 'Filter');
    if (filterRadio) {
        filterRadio.checked = true;
    }

    // 如果没有选中任何筛选，默认选择过去
    if (!document.querySelector('input[name="timeFilter"]:checked')) {
        const pastFilter = document.getElementById('pastFilter');
        if (pastFilter) {
            pastFilter.checked = true;
            visionData.currentTimeFilter = 'past';
        }
    }
}

// 设置视图的可见性
function setViewVisibility() {
    // 只显示树状视图
    if (domElements.treeView) {
        domElements.treeView.style.display = 'block';
    }
}

// 缓存常用DOM元素
function cacheElements() {
    domElements = {
        // 视图容器
        treeView: document.getElementById('treeView'),

        // 视图选择按钮
        treeMode: document.getElementById('treeMode'),

        // 时间筛选按钮
        pastFilter: document.getElementById('pastFilter'),
        presentFilter: document.getElementById('presentFilter'),
        futureFilter: document.getElementById('futureFilter'),

        // 表单元素
        sceneForm: document.getElementById('sceneForm'),
        sceneTitle: document.getElementById('sceneTitle'),
        sceneTime: document.getElementById('sceneTime'),
        sceneDescription: document.getElementById('sceneDescription'),
        parentScene: document.getElementById('parentScene'),
        positionGroup: document.getElementById('positionGroup'),

        // 模态框元素
        sceneDetailModal: document.getElementById('sceneDetailModal'),
        modalSceneTitle: document.getElementById('modalSceneTitle'),
        modalSceneContent: document.getElementById('modalSceneContent'),
        editSceneBtn: document.getElementById('editSceneBtn'),

        // 编辑模态框元素
        editSceneModal: document.getElementById('editSceneModal'),
        editSceneForm: document.getElementById('editSceneForm'),
        editSceneId: document.getElementById('editSceneId'),
        editSceneTitle: document.getElementById('editSceneTitle'),
        editSceneTime: document.getElementById('editSceneTime'),
        editSceneDescription: document.getElementById('editSceneDescription'),
        saveEditSceneBtn: document.getElementById('saveEditSceneBtn'),

        // 导出按钮
        exportScenesBtn: document.getElementById('exportScenesBtn')
    };
}

// 加载数据
function loadVisionData() {
    try {
        const savedData = localStorage.getItem(VISION_CONFIG.STORAGE_KEY);
        if (savedData) {
            visionData = JSON.parse(savedData);
            
            // 确保所有必要的属性都存在
            if (!visionData.scenes) {
                visionData.scenes = [];
            }
            if (!visionData.lastViewMode) {
                visionData.lastViewMode = 'tree';
            }
            if (!visionData.currentTimeFilter) {
                visionData.currentTimeFilter = 'past';
            }
            
            console.log('脑中画面数据加载成功', visionData);
            
            // 立即根据数据渲染视图
            setTimeout(() => {
                // 确保视图内容清空，强制重新渲染
                if (domElements.treeView) {
                    domElements.treeView.innerHTML = '';
                    renderTreeView();
                }
            }, 100);
        }
    } catch (error) {
        console.error('加载脑中画面数据失败', error);
        visionData = {
            scenes: [],
            lastViewMode: 'tree',
            currentTimeFilter: 'past'
        };
    }
    
    // 检查数据版本，未来可能的数据迁移
    migrateDataIfNeeded();
}

// 数据迁移函数，用于未来的数据结构更新
function migrateDataIfNeeded() {
    // 目前是第一版，无需迁移
    // 未来如果数据结构变化，在这里添加迁移代码
}

// 保存数据
function saveVisionData() {
    try {
        localStorage.setItem(VISION_CONFIG.STORAGE_KEY, JSON.stringify(visionData));
        console.log('脑中画面数据保存成功');
        
        // 如果appData存在，添加到appData.visionScenes中用于同步
        if (typeof window.appData !== 'undefined') {
            // 确保visionScenes属性存在
            if (!window.appData.hasOwnProperty('visionScenes')) {
                window.appData.visionScenes = [];
            }
            window.appData.visionScenes = [...visionData.scenes];
            // 尝试触发主系统的保存
            if (typeof window.saveData === 'function') {
                window.saveData();
            }
        }
    } catch (error) {
        console.error('保存脑中画面数据失败', error);
        showToast('保存数据失败，请尝试清理浏览器存储空间', 'danger');
    }
}

// 检查主系统数据
function checkMainAppData() {
    if (typeof window.appData !== 'undefined') {
        // 检查主系统appData中是否有visionScenes属性
        if (window.appData.hasOwnProperty('visionScenes') && Array.isArray(window.appData.visionScenes)) {
            // 如果主系统有数据但本地没有，使用主系统数据
            if (visionData.scenes.length === 0 && window.appData.visionScenes.length > 0) {
                visionData.scenes = [...window.appData.visionScenes];
                saveVisionData();
                renderCurrentView();
            }
        }
    }
}

// =============== 事件处理 ===============
function attachEventListeners() {
    // 时间筛选事件监听
    document.querySelectorAll('input[name="timeFilter"]').forEach(radio => {
        radio.addEventListener('change', handleTimeFilterChange);
    });
    
    // 添加新记忆表单提交 - 修复页面刷新问题
    domElements.sceneForm.addEventListener('submit', handleAddScene, { passive: false });

    // 父记忆选择变化事件
    domElements.parentScene.addEventListener('change', handleParentSceneChange);


    
    // 编辑按钮 - 使用被动事件监听器提高性能
    if (domElements.editSceneBtn) {
        domElements.editSceneBtn.addEventListener('click', openEditSceneModal, { passive: true });
    }
    
    // 保存编辑按钮
    if (domElements.saveEditSceneBtn) {
        domElements.saveEditSceneBtn.addEventListener('click', saveEditedScene, { passive: true });
    }

    // 监听模态框关闭事件，确保背景正确清理
    if (domElements.editSceneModal) {
        domElements.editSceneModal.addEventListener('hidden.bs.modal', function() {
            // 使用统一的清理函数
            cleanupModalState();
        });
    }
    
    // 导出按钮
    if (domElements.exportScenesBtn) {
        domElements.exportScenesBtn.addEventListener('click', exportSceneData, { passive: true });
    }
    
    // 使用事件委托处理动态添加的元素
    document.addEventListener('click', function(event) {
        // 处理操作按钮点击
        if (event.target.classList.contains('action-btn') || event.target.closest('.action-btn')) {
            event.preventDefault(); // 阻止默认行为
            event.stopPropagation(); // 阻止事件冒泡
            const target = event.target.classList.contains('action-btn') ?
                event.target : event.target.closest('.action-btn');
            const sceneId = target.getAttribute('data-scene-id');
            const action = target.getAttribute('data-action');

            if (sceneId && action) {
                if (action === 'edit') {
                    openEditSceneModalDirect(sceneId);
                } else if (action === 'delete') {
                    deleteSceneDirect(sceneId);
                }
            }
            return false; // 确保事件不会继续传播
        }

        // 移除卡片点击事件 - 不再弹出详情窗口
        // 如果需要查看详情，用户可以通过其他方式访问
    }, { passive: false }); // 改为非被动事件以支持preventDefault
    
    // 减少页面可见性变更事件处理的复杂度
    document.addEventListener('visibilitychange', function() {
        if (document.visibilityState === 'visible') {
            // 简化处理逻辑，减少不必要的DOM操作
            setTimeout(checkAndRenderCurrentView, 200);
        }
    }, { passive: true });
    
    // 优化页面刷新或关闭前的事件处理
    window.addEventListener('beforeunload', function() {
        // 获取当前视图模式并保存
        saveCurrentViewMode();
    }, { passive: true });
    
    // 监听主数据变化
    window.addEventListener('appDataUpdated', checkMainAppData, { passive: true });
    
    // 初始检查主数据 - 延迟执行以降低初始加载压力
    setTimeout(checkMainAppData, 1500);
}

// 辅助函数：检查并渲染当前视图
function checkAndRenderCurrentView() {
    // 只渲染树状视图
    if (domElements.treeView && !domElements.treeView.hasChildNodes() && visionData.scenes.length > 0) {
        renderTreeView();
    }
}

// 辅助函数：获取当前视图模式
function getCurrentViewMode() {
    // 现在只有树状视图
    return 'tree';
}

// 辅助函数：保存当前视图模式
function saveCurrentViewMode() {
    let currentMode = getCurrentViewMode();
    // 确保视图模式被正确保存
    if (currentMode !== visionData.lastViewMode) {
        visionData.lastViewMode = currentMode;
        saveVisionData();
    }
}

// 视图模式切换处理
function handleViewModeChange(event) {
    if (!event.target.checked) return;
    
    const newMode = event.target.id.replace('Mode', '');
    if (VISION_CONFIG.DEBUG) console.log('视图模式切换:', newMode);
    
    // 更新视图可见性
    setViewVisibility();
    
    // 保存最后使用的视图模式
    visionData.lastViewMode = newMode;
    saveVisionData();
    
    // 检查当前视图是否已有内容，如果没有则渲染
    switch (newMode) {
        case 'timeline':
            if (!domElements.timelineView.hasChildNodes() && visionData.scenes.length > 0) {
                domElements.timelineView.innerHTML = ''; // 确保视图为空
                renderTimelineView();
            }
            break;
        case 'grid':
            if (!domElements.gridView.hasChildNodes() && visionData.scenes.length > 0) {
                domElements.gridView.innerHTML = ''; // 确保视图为空
                renderGridView();
            }
            break;
    }
}

// 添加新记忆处理
function handleAddScene(event) {
    event.preventDefault();
    event.stopPropagation(); // 阻止事件冒泡

    if (VISION_CONFIG.DEBUG) console.log('正在添加新记忆...');

    try {
        // 获取当前选中的时间维度
        const currentTimeFilter = getCurrentTimeFilter();

        // 获取父记忆和位置
        const parentId = domElements.parentScene.value || null;
        const position = parentId ? document.querySelector('input[name="position"]:checked')?.value || 'left' : null;

        // 获取表单数据
        const newScene = {
            id: generateId(),
            title: domElements.sceneTitle.value.trim(),
            time: domElements.sceneTime.value, // 年月格式
            timeType: currentTimeFilter, // 过去、现在、未来
            description: domElements.sceneDescription.value.trim(),
            parentId: parentId,
            position: position,
            createdAt: Date.now()
        };

        if (VISION_CONFIG.DEBUG) console.log('新记忆数据:', newScene);

        // 验证数据
        if (!newScene.title) {
            throw new Error('请填写记忆标题');
        }
        if (!newScene.time) {
            throw new Error('请选择时间');
        }
        if (!newScene.description) {
            throw new Error('请填写记忆描述');
        }

        // 添加到数据中
        visionData.scenes.push(newScene);

        // 按时间排序（从新到旧）
        sortScenesByTime();

        if (VISION_CONFIG.DEBUG) console.log('当前记忆数量:', visionData.scenes.length);

        // 保存数据
        saveVisionData();

        // 重置表单
        domElements.sceneForm.reset();

        // 隐藏位置选择组
        domElements.positionGroup.style.display = 'none';

        // 重新渲染视图
        renderTreeView();

        // 更新父记忆选择列表
        updateParentSceneOptions();

    } catch (error) {
        console.error('添加记忆失败', error);
        showToast(error.message || '添加记忆失败，请重试', 'danger');
    }

    return false; // 确保阻止表单默认提交行为
}

// 生成唯一ID
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
}

// 获取当前时间筛选
function getCurrentTimeFilter() {
    const checkedFilter = document.querySelector('input[name="timeFilter"]:checked');
    return checkedFilter ? checkedFilter.id.replace('Filter', '') : 'past';
}

// 时间筛选变化处理
function handleTimeFilterChange(event) {
    visionData.currentTimeFilter = event.target.id.replace('Filter', '');
    saveVisionData();
    renderTreeView();
}

// 按时间排序记忆（从新到旧）
function sortScenesByTime() {
    visionData.scenes.sort((a, b) => {
        // 首先按时间字段排序
        if (a.time && b.time) {
            return new Date(b.time) - new Date(a.time);
        }
        // 如果没有时间字段，按创建时间排序
        return b.createdAt - a.createdAt;
    });
}

// 获取筛选后的记忆
function getFilteredScenes() {
    const currentFilter = getCurrentTimeFilter();
    const now = new Date();
    const currentYearMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;

    return visionData.scenes.filter(scene => {
        if (!scene.time) return true; // 没有时间的记忆总是显示

        switch (currentFilter) {
            case 'past':
                return scene.time < currentYearMonth;
            case 'present':
                return scene.time === currentYearMonth;
            case 'future':
                return scene.time > currentYearMonth;
            default:
                return true;
        }
    });
}

// 渲染当前视图
function renderCurrentView() {
    if (VISION_CONFIG.DEBUG) console.log('渲染当前视图: tree');

    // 确保数据已加载
    if (visionData.scenes.length === 0) {
        loadVisionData();
    }

    // 只渲染树状视图
    renderTreeView();
}

// 渲染时间线视图
function renderTimelineView(isLightweight = false) {
    // 检测是否在移动设备上
    const isMobile = window.innerWidth < 768;

    const scenes = visionData.scenes;
    const timelineView = domElements.timelineView;

    // 清空当前内容
    timelineView.innerHTML = '';

    // 检查是否有数据
    if (scenes.length === 0) {
        return;
    }
    
    // 使用文档片段减少DOM重绘次数
    const fragment = document.createDocumentFragment();
    
    // 限制移动设备上一次渲染的项目数量以提高性能
    const maxItemsToRender = (isMobile && isLightweight) ? 10 : scenes.length;
    
    // 创建时间线项目
    for (let i = 0; i < Math.min(maxItemsToRender, scenes.length); i++) {
        const scene = scenes[i];
        const timelineItem = document.createElement('div');
        timelineItem.className = 'timeline-item';
        
        // 简化移动设备上的HTML结构
        if (isMobile) {
            timelineItem.innerHTML = `
                <div class="timeline-card">
                    <div class="timeline-header">
                        <div class="time-type-badge">${scene.timeType}</div>
                    </div>
                    <div class="timeline-content">
                        <div class="title-row">
                            <h5 class="timeline-title">${scene.title}</h5>
                            <div class="title-actions">
                                <button class="action-btn edit-btn" data-scene-id="${scene.id}" data-action="edit" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn delete-btn" data-scene-id="${scene.id}" data-action="delete" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 使用事件委托，无需为每个项添加监听器
            timelineItem.setAttribute('data-scene-id', scene.id);
        } else {
            timelineItem.innerHTML = `
                <div class="timeline-card">
                    <div class="timeline-header">
                        <div class="time-type-badge">${scene.timeType}</div>
                        <div class="timeline-date">${formatDate(scene.createdAt)}</div>
                    </div>
                    <div class="timeline-content">
                        <div class="title-row">
                            <h5 class="timeline-title">${scene.title}</h5>
                            <div class="title-actions">
                                <button class="action-btn edit-btn" data-scene-id="${scene.id}" data-action="edit" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn delete-btn" data-scene-id="${scene.id}" data-action="delete" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="timeline-description" data-scene-id="${scene.id}">
                            ${scene.description.length > 100 ? scene.description.substring(0, 100) + '...' : scene.description}
                        </div>
                    </div>
                </div>
            `;
        }
        
        // 添加到片段中
        fragment.appendChild(timelineItem);
    }
    
    // 一次性添加所有元素到DOM
    timelineView.appendChild(fragment);
    
    // 如果是轻量级渲染且有更多数据，使用IntersectionObserver延迟加载
    if (isLightweight && scenes.length > maxItemsToRender && 'IntersectionObserver' in window) {
        // 添加"加载更多"按钮
        const loadMoreBtn = document.createElement('button');
        loadMoreBtn.className = 'btn btn-outline-primary w-100 mt-3 mb-3';
        loadMoreBtn.textContent = '加载更多...';
        loadMoreBtn.addEventListener('click', function() {
            // 移除加载更多按钮
            this.remove();
            // 完全渲染所有场景
            renderTimelineView(false);
        });
        timelineView.appendChild(loadMoreBtn);
    }
}

// 渲染卡片墙视图
function renderGridView(isLightweight = false) {
    // 检测是否在移动设备上
    const isMobile = window.innerWidth < 768;

    // 获取筛选后的记忆
    const scenes = getFilteredScenes();
    const gridView = domElements.gridView;

    // 清空当前内容
    gridView.innerHTML = '';

    // 检查是否有数据
    if (scenes.length === 0) {
        gridView.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-brain"></i>
                <h5>暂无记忆</h5>
                <p>开始添加你的记忆吧</p>
            </div>
        `;
        return;
    }

    // 使用文档片段减少DOM重绘次数
    const fragment = document.createDocumentFragment();

    // 限制移动设备上一次渲染的项目数量以提高性能
    const maxItemsToRender = (isMobile && isLightweight) ? 12 : scenes.length;

    // 创建卡片项
    for (let i = 0; i < Math.min(maxItemsToRender, scenes.length); i++) {
        const scene = scenes[i];
        const sceneCard = document.createElement('div');
        sceneCard.className = 'scene-card';
        sceneCard.setAttribute('data-scene-id', scene.id);

        // 格式化时间显示
        const timeDisplay = scene.time ? formatTimeDisplay(scene.time) : '未设置时间';
        const timeTypeClass = `time-${scene.timeType}`;

        // 简化移动设备上的HTML结构
        if (isMobile) {
            sceneCard.innerHTML = `
                <div class="scene-card-header">
                    <div class="title-row">
                        <h5 class="scene-card-title">${scene.title}</h5>
                        <div class="title-actions">
                            <button class="action-btn edit-btn" data-scene-id="${scene.id}" data-action="edit" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn delete-btn" data-scene-id="${scene.id}" data-action="delete" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="scene-time">${timeDisplay}</div>
                </div>
                <div class="scene-card-footer">
                    <div class="time-type-badge ${timeTypeClass}">${getTimeTypeText(scene.timeType)}</div>
                </div>
            `;
        } else {
            sceneCard.innerHTML = `
                <div class="scene-card-header">
                    <div class="title-row">
                        <h5 class="scene-card-title">${scene.title}</h5>
                        <div class="title-actions">
                            <button class="action-btn edit-btn" data-scene-id="${scene.id}" data-action="edit" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn delete-btn" data-scene-id="${scene.id}" data-action="delete" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="scene-time">${timeDisplay}</div>
                </div>
                <div class="scene-card-content">
                    <div class="scene-card-description">${scene.description.length > 100 ? scene.description.substring(0, 100) + '...' : scene.description}</div>
                </div>
                <div class="scene-card-footer">
                    <div class="time-type-badge ${timeTypeClass}">${getTimeTypeText(scene.timeType)}</div>
                </div>
            `;
        }

        // 添加到片段中
        fragment.appendChild(sceneCard);
    }

    // 一次性添加所有元素到DOM
    gridView.appendChild(fragment);

    // 如果是轻量级渲染且有更多数据，添加加载更多按钮
    if (isLightweight && scenes.length > maxItemsToRender) {
        const loadMoreBtn = document.createElement('button');
        loadMoreBtn.className = 'btn btn-outline-primary w-100 mt-3 mb-3';
        loadMoreBtn.textContent = '加载更多...';
        loadMoreBtn.addEventListener('click', function() {
            // 移除加载更多按钮
            this.remove();
            // 完全渲染所有场景
            renderGridView(false);
        });
        gridView.appendChild(loadMoreBtn);
    }
}

// 打开场景详情模态框
function openSceneDetailModal(sceneOrId) {
    let scene;
    
    if (typeof sceneOrId === 'string') {
        // 如果传入的是ID，查找对应的场景
        scene = visionData.scenes.find(s => s.id === sceneOrId);
    } else {
        // 否则直接使用传入的场景对象
        scene = sceneOrId;
    }
    
    if (!scene) return;
    
    // 设置模态框内容
    domElements.modalSceneTitle.textContent = scene.title;
    domElements.modalSceneContent.innerHTML = `
        <div class="modal-scene-content">
            <div class="modal-scene-meta">
                <div class="modal-scene-meta-item">
                    <span class="modal-scene-meta-label">时间维度:</span>
                    ${scene.timeType}
                </div>
                <div class="modal-scene-meta-item">
                    <span class="modal-scene-meta-label">创建时间:</span>
                    ${formatDate(scene.createdAt)}
                </div>
            </div>
            
            <div class="modal-scene-description">${scene.description}</div>
        </div>
    `;
    
    // 存储当前场景ID，用于编辑功能
    domElements.editSceneBtn.setAttribute('data-scene-id', scene.id);
    
    // 显示模态框
    const modal = new bootstrap.Modal(domElements.sceneDetailModal);
    modal.show();
}

// 打开编辑场景模态框
function openEditSceneModal() {
    const sceneId = domElements.editSceneBtn.getAttribute('data-scene-id');
    const scene = visionData.scenes.find(s => s.id === sceneId);

    if (!scene) return;

    // 填充表单
    domElements.editSceneId.value = scene.id;
    domElements.editSceneTitle.value = scene.title;
    domElements.editSceneTimeType.value = scene.timeType;
    domElements.editSceneDescription.value = scene.description;

    // 关闭详情模态框，打开编辑模态框
    bootstrap.Modal.getInstance(domElements.sceneDetailModal).hide();
    const editModal = new bootstrap.Modal(domElements.editSceneModal);
    editModal.show();
}

// 直接打开编辑场景模态框（从操作按钮调用）
function openEditSceneModalDirect(sceneId) {
    const scene = visionData.scenes.find(s => s.id === sceneId);

    if (!scene) return;

    // 填充表单
    domElements.editSceneId.value = scene.id;
    domElements.editSceneTitle.value = scene.title;
    domElements.editSceneTime.value = scene.time || '';
    domElements.editSceneDescription.value = scene.description;

    // 确保没有其他模态框打开
    cleanupModalState();

    // 等待其他模态框完全关闭后再打开新的
    setTimeout(() => {
        const editModal = new bootstrap.Modal(domElements.editSceneModal, {
            backdrop: true,
            keyboard: true,
            focus: true
        });
        editModal.show();
    }, 100);
}

// 保存编辑过的场景
function saveEditedScene() {
    try {
        const sceneId = domElements.editSceneId.value;
        const sceneIndex = visionData.scenes.findIndex(s => s.id === sceneId);

        if (sceneIndex === -1) {
            throw new Error('记忆不存在，无法保存');
        }

        // 获取编辑后的数据
        const editedScene = {
            ...visionData.scenes[sceneIndex], // 保留原有数据
            title: domElements.editSceneTitle.value.trim(),
            time: domElements.editSceneTime.value,
            description: domElements.editSceneDescription.value.trim(),
            updatedAt: Date.now()
        };

        // 验证数据
        if (!editedScene.title) {
            throw new Error('请填写记忆标题');
        }
        if (!editedScene.time) {
            throw new Error('请选择时间');
        }
        if (!editedScene.description) {
            throw new Error('请填写记忆描述');
        }

        // 更新数据
        visionData.scenes[sceneIndex] = editedScene;

        // 重新排序
        sortScenesByTime();

        // 保存数据
        saveVisionData();

        // 关闭编辑模态框
        const editModalInstance = bootstrap.Modal.getInstance(domElements.editSceneModal);
        if (editModalInstance) {
            editModalInstance.hide();
        }

        // 确保模态框完全关闭后再更新视图
        setTimeout(() => {
            // 重新渲染视图
            renderTreeView();
        }, 300);

        showToast('记忆更新成功', 'success');

    } catch (error) {
        console.error('保存编辑失败', error);
        showToast(error.message || '保存编辑失败，请重试', 'danger');
    }
}

// 删除场景
function deleteScene() {
    try {
        const sceneId = domElements.editSceneId.value;
        const sceneIndex = visionData.scenes.findIndex(s => s.id === sceneId);

        if (sceneIndex === -1) {
            return; // 静默返回，不显示错误
        }

        // 确认删除
        if (!confirm('确定要删除这个画面吗？此操作不可撤销。')) {
            return;
        }

        // 删除数据
        visionData.scenes.splice(sceneIndex, 1);

        // 保存数据
        saveVisionData();

        // 关闭模态框
        bootstrap.Modal.getInstance(domElements.editSceneModal).hide();

        // 更新视图
        renderCurrentView();

    } catch (error) {
        console.error('删除失败', error);
        showToast(error.message || '删除失败，请重试', 'danger');
    }
}

// 直接删除场景（从操作按钮调用）
function deleteSceneDirect(sceneId) {
    try {
        const sceneIndex = visionData.scenes.findIndex(s => s.id === sceneId);

        if (sceneIndex === -1) {
            return; // 静默返回，不显示错误
        }

        const scene = visionData.scenes[sceneIndex];

        // 确保没有模态框打开
        cleanupModalState();

        // 确认删除
        if (!confirm(`确定要删除记忆"${scene.title}"吗？此操作不可撤销。`)) {
            return;
        }

        // 删除数据
        visionData.scenes.splice(sceneIndex, 1);

        // 保存数据
        saveVisionData();

        // 重新渲染视图
        renderTreeView();

    } catch (error) {
        console.error('删除失败', error);
        showToast(error.message || '删除失败，请重试', 'danger');
    }
}

// 导出场景数据
function exportSceneData() {
    try {
        // 准备导出数据
        const exportData = {
            scenes: visionData.scenes,
            exportDate: Date.now(),
            version: '1.0'
        };
        
        // 转换为JSON字符串
        const jsonString = JSON.stringify(exportData, null, 2);
        
        // 创建Blob对象
        const blob = new Blob([jsonString], { type: 'application/json' });
        
        // 创建下载链接
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `脑中画面_${formatDateForFilename(new Date())}.json`;
        
        // 触发下载
        document.body.appendChild(a);
        a.click();
        
        // 清理
        setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }, 100);
        
        showToast('数据导出成功');
    } catch (error) {
        console.error('导出数据失败', error);
        showToast('导出数据失败，请重试', 'danger');
    }
}

// =============== 工具函数 ===============

// 清理模态框状态
function cleanupModalState() {
    // 移除所有模态框背景
    const backdrops = document.querySelectorAll('.modal-backdrop');
    backdrops.forEach(backdrop => backdrop.remove());

    // 恢复body的样式
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';

    // 关闭所有打开的模态框
    const existingModals = document.querySelectorAll('.modal.show');
    existingModals.forEach(modal => {
        const modalInstance = bootstrap.Modal.getInstance(modal);
        if (modalInstance) {
            modalInstance.hide();
        }
    });
}



// 格式化日期
function formatDate(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    }).replace(/\//g, '-');
}

// 格式化日期用于文件名
function formatDateForFilename(date) {
    return `${date.getFullYear()}${(date.getMonth() + 1).toString().padStart(2, '0')}${date.getDate().toString().padStart(2, '0')}`;
}

// 显示提示消息
function showToast(message, type = 'success') {
    // 查找或创建toast容器
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'position-fixed bottom-0 end-0 p-3';
        toastContainer.style.zIndex = '1050';
        document.body.appendChild(toastContainer);
    }
    
    // 创建toast元素
    const toastId = 'toast-' + Date.now();
    const toastElement = document.createElement('div');
    toastElement.className = `toast align-items-center text-white bg-${type}`;
    toastElement.id = toastId;
    toastElement.setAttribute('role', 'alert');
    toastElement.setAttribute('aria-live', 'assertive');
    toastElement.setAttribute('aria-atomic', 'true');
    
    toastElement.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;
    
    // 添加到容器
    toastContainer.appendChild(toastElement);
    
    // 创建Bootstrap Toast实例
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: 3000
    });
    
    // 显示toast
    toast.show();
    
    // 监听隐藏事件，移除DOM元素
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

// 添加返回顶部按钮
function addScrollToTopButton() {
    // 创建按钮
    const scrollBtn = document.createElement('div');
    scrollBtn.className = 'scroll-to-top';
    scrollBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
    
    // 添加点击事件
    scrollBtn.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });
    
    // 添加到文档
    document.body.appendChild(scrollBtn);
    
    // 监听滚动，控制按钮显示/隐藏
    window.addEventListener('scroll', () => {
        if (window.scrollY > 300) {
            scrollBtn.classList.add('visible');
        } else {
            scrollBtn.classList.remove('visible');
        }
    });
}

// 初始化函数调用
document.addEventListener('DOMContentLoaded', initVisionScenes);

// 格式化时间显示 (年月)
function formatTimeDisplay(timeString) {
    if (!timeString) return '未设置时间';
    const [year, month] = timeString.split('-');
    return `${year}年${month}月`;
}

// 获取时间类型文本
function getTimeTypeText(timeType) {
    const types = {
        past: '过去',
        present: '现在',
        future: '未来'
    };
    return types[timeType] || '未知';
}

// 清理模态框状态
function cleanupModalState() {
    // 关闭所有可能打开的模态框
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        const modalInstance = bootstrap.Modal.getInstance(modal);
        if (modalInstance) {
            modalInstance.hide();
        }
    });

    // 移除模态框背景
    const backdrops = document.querySelectorAll('.modal-backdrop');
    backdrops.forEach(backdrop => backdrop.remove());

    // 恢复body的样式
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';
}



// =============== 树状视图相关函数 ===============

// 渲染树状视图
function renderTreeView() {
    const treeView = domElements.treeView;

    // 清空当前内容
    treeView.innerHTML = '';

    // 获取筛选后的记忆
    const scenes = getFilteredScenes();

    // 检查是否有数据
    if (scenes.length === 0) {
        treeView.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-tree"></i>
                <h5>暂无记忆</h5>
                <p>开始添加你的记忆树吧</p>
            </div>
        `;
        return;
    }

    // 构建树状结构
    const treeData = buildTreeStructure(scenes);

    // 渲染根节点
    const fragment = document.createDocumentFragment();
    treeData.roots.forEach(rootScene => {
        const treeNode = createTreeNode(rootScene, treeData.childrenMap);
        fragment.appendChild(treeNode);
    });

    treeView.appendChild(fragment);
}

// 构建树状结构数据
function buildTreeStructure(scenes) {
    const childrenMap = new Map();
    const roots = [];

    // 初始化子节点映射
    scenes.forEach(scene => {
        childrenMap.set(scene.id, { left: null, right: null });
    });

    // 分类根节点和子节点
    scenes.forEach(scene => {
        if (!scene.parentId) {
            // 根节点
            roots.push(scene);
        } else {
            // 子节点
            const parentChildren = childrenMap.get(scene.parentId);
            if (parentChildren) {
                if (scene.position === 'right') {
                    parentChildren.right = scene;
                } else {
                    parentChildren.left = scene;
                }
            }
        }
    });

    return { roots, childrenMap };
}

// 创建树节点
function createTreeNode(scene, childrenMap) {
    const nodeElement = document.createElement('div');
    nodeElement.className = 'tree-node';
    nodeElement.setAttribute('data-scene-id', scene.id);

    // 获取子节点
    const children = childrenMap.get(scene.id);
    const hasChildren = children && (children.left || children.right);

    // 格式化时间显示
    const timeDisplay = scene.time ? formatTimeDisplay(scene.time) : '未设置时间';
    const timeTypeClass = `time-${scene.timeType}`;

    nodeElement.innerHTML = `
        <div class="tree-node-content">
            <div class="tree-node-header">
                <div class="tree-node-info">
                    <h6 class="tree-node-title">${scene.title}</h6>
                    <div class="tree-node-meta">
                        <span class="tree-node-time">${timeDisplay}</span>
                        <span class="time-type-badge ${timeTypeClass}">${getTimeTypeText(scene.timeType)}</span>
                    </div>
                </div>
                <div class="tree-node-actions">
                    <button class="action-btn edit-btn" data-scene-id="${scene.id}" data-action="edit" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" data-scene-id="${scene.id}" data-action="delete" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                    ${hasChildren ? '<button class="tree-toggle-btn" title="展开/折叠"><i class="fas fa-chevron-down"></i></button>' : ''}
                </div>
            </div>
            <div class="tree-node-description">${scene.description}</div>
        </div>
    `;

    // 如果有子节点，创建子节点容器
    if (hasChildren) {
        const childrenContainer = document.createElement('div');
        childrenContainer.className = 'tree-children';

        // 创建左右分支容器
        const branchesContainer = document.createElement('div');
        branchesContainer.className = 'tree-branches';

        // 左分支
        if (children.left) {
            const leftBranch = document.createElement('div');
            leftBranch.className = 'tree-branch tree-branch-left';
            leftBranch.appendChild(createTreeNode(children.left, childrenMap));
            branchesContainer.appendChild(leftBranch);
        }

        // 右分支
        if (children.right) {
            const rightBranch = document.createElement('div');
            rightBranch.className = 'tree-branch tree-branch-right';
            rightBranch.appendChild(createTreeNode(children.right, childrenMap));
            branchesContainer.appendChild(rightBranch);
        }

        childrenContainer.appendChild(branchesContainer);
        nodeElement.appendChild(childrenContainer);

        // 添加展开/折叠功能
        const toggleBtn = nodeElement.querySelector('.tree-toggle-btn');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                toggleTreeNode(nodeElement);
            });
        }
    }

    return nodeElement;
}

// 展开/折叠树节点
function toggleTreeNode(nodeElement) {
    const childrenContainer = nodeElement.querySelector('.tree-children');
    const toggleBtn = nodeElement.querySelector('.tree-toggle-btn i');

    if (childrenContainer) {
        const isExpanded = !childrenContainer.classList.contains('collapsed');

        if (isExpanded) {
            childrenContainer.classList.add('collapsed');
            toggleBtn.className = 'fas fa-chevron-right';
        } else {
            childrenContainer.classList.remove('collapsed');
            toggleBtn.className = 'fas fa-chevron-down';
        }
    }
}

// 处理父记忆选择变化
function handleParentSceneChange() {
    const parentId = domElements.parentScene.value;
    const positionGroup = domElements.positionGroup;

    if (parentId) {
        positionGroup.style.display = 'block';
        // 默认选择左分支
        document.getElementById('leftPosition').checked = true;
    } else {
        positionGroup.style.display = 'none';
        // 清除位置选择
        document.querySelectorAll('input[name="position"]').forEach(radio => {
            radio.checked = false;
        });
    }
}

// 更新父记忆选择列表
function updateParentSceneOptions() {
    const parentSelect = domElements.parentScene;
    const currentOptions = Array.from(parentSelect.options).slice(1); // 保留第一个"作为根记忆"选项

    // 清除现有选项（除了第一个）
    currentOptions.forEach(option => option.remove());

    // 添加所有记忆作为选项
    visionData.scenes.forEach(scene => {
        const option = document.createElement('option');
        option.value = scene.id;
        option.textContent = scene.title;
        parentSelect.appendChild(option);
    });
}

// 暴露给全局 - 用于在其他地方调用
window.openSceneDetailModal = openSceneDetailModal;

// 初始化函数调用
document.addEventListener('DOMContentLoaded', initVisionScenes);