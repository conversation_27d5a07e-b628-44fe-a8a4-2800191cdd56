// 复盘系统功能实现

// 检查运行环境
function checkEnvironment() {
    const issues = [];
    
    // 检查基本浏览器API
    if (!window.localStorage) {
        issues.push('您的浏览器不支持localStorage，无法保存数据');
    }
    
    // 检查必要的第三方库
    if (typeof bootstrap === 'undefined') {
        issues.push('Bootstrap库未加载，界面功能将受限');
    }
    
    // 不再检查Sortable库，移除这段代码
    // if (typeof Sortable === 'undefined') {
    //     issues.push('Sortable库未加载，拖拽排序功能不可用');
    // }
    
    return {
        hasIssues: issues.length > 0,
        issues: issues
    };
}

// 配置
const CONFIG = {
    STORAGE_KEY: 'savingsData', // 使用主系统的存储键
    CURRENT_DATE: new Date(),
    DRAFT_KEY: 'reviewDraft' // 草稿存储键
};

// 数据结构
let reviewData = {
    templates: [], // 复盘模板
    reviews: [], // 复盘记录
    statistics: {
        totalCount: 0,
        dailyCount: 0,
        weeklyCount: 0,
        monthlyCount: 0,
        streakCount: 0
    }
};

// 草稿数据
let draftData = null;

// DOM元素缓存
let domElements = {};

// ================ 初始化 ================

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('初始化复盘系统');
    
    // 设置全局错误处理
    window.onerror = function(message, source, lineno, colno, error) {
        console.error('全局错误:', message, source, lineno, colno, error);
        
        // 特别处理移动浏览器的Script error
        if (message === 'Script error.' || message === 'script error') {
            console.warn('检测到跨域脚本错误，尝试降级处理');
            // 尝试降级初始化
            try {
                loadData();
                renderSimpleUI();
                return true;
            } catch(e) {
                console.error('降级处理失败', e);
            }
        }
        
        // 记录错误日志到localStorage以便调试
        try {
            let errorLog = JSON.parse(localStorage.getItem('errorLog') || '[]');
            errorLog.push({
                message: message,
                source: source,
                lineno: lineno,
                colno: colno,
                userAgent: navigator.userAgent,
                time: new Date().toISOString()
            });
            // 只保留最近10条错误
            if (errorLog.length > 10) errorLog = errorLog.slice(-10);
            localStorage.setItem('errorLog', JSON.stringify(errorLog));
        } catch (e) {
            console.error('无法记录错误到localStorage', e);
        }
        
        return true; // 防止默认错误处理
    };
    
    // 尝试使用try-catch包装所有初始化代码
    try {
        // 先检查环境
        const envCheck = checkEnvironment();
        if (envCheck.hasIssues) {
            console.warn('环境检查发现问题:', envCheck.issues);
            if (envCheck.issues.some(issue => issue.includes('未加载'))) {
                // 如果关键库未加载，尝试重新加载页面一次
                if (!sessionStorage.getItem('reloadAttempt')) {
                    sessionStorage.setItem('reloadAttempt', '1');
                    console.log('尝试重新加载页面以修复库加载问题');
                    window.location.reload();
                    return;
                }
            }
        }
        
        // 安全地初始化系统
        initReviewSystem();
    } catch (e) {
        console.error('初始化复盘系统失败，尝试降级方式加载', e);
        
        // 不弹出警告，而是显示在界面上
        const errorMsg = '初始化失败: ' + e.message + '。系统将以简单模式运行，部分功能可能不可用。';
        showErrorBanner(errorMsg);
        
        // 降级初始化
        try {
            renderSimpleUI();
        } catch (e2) {
            console.error('降级初始化也失败', e2);
            document.body.innerHTML = `
                <div class="container mt-5">
                    <div class="alert alert-danger">
                        <h4>系统初始化失败</h4>
                        <p>非常抱歉，复盘系统无法正确加载。请尝试以下解决方案：</p>
                        <ol>
                            <li>刷新页面重试</li>
                            <li>清除浏览器缓存后重试</li>
                            <li>使用不同的浏览器访问</li>
                        </ol>
                        <p>错误详情: ${e.message} → ${e2.message}</p>
                        <button class="btn btn-primary" onclick="window.location.reload()">刷新页面</button>
                        <a href="index.html" class="btn btn-secondary">返回主页</a>
                    </div>
                </div>
            `;
        }
    }
});

// 简单UI渲染函数，用于降级处理
function renderSimpleUI() {
    // 尝试加载数据
    loadData();
    
    // 简单方式初始化视图
    document.querySelectorAll('input[name="reviewViewMode"]').forEach(radio => {
        radio.addEventListener('change', function(e) {
            const viewMode = e.target.id.replace('Mode', '');
            document.querySelectorAll('.view-container').forEach(container => {
                container.style.display = 'none';
            });
            
            const targetView = document.getElementById(viewMode + 'View');
            if (targetView) {
                targetView.style.display = 'block';
                // 保存当前视图模式
                localStorage.setItem('reviewViewMode', viewMode);
            }
        });
    });
    
    // 尝试恢复上次的视图模式
    try {
        const lastViewMode = localStorage.getItem('reviewViewMode') || 'template';
        const radioId = lastViewMode + 'Mode';
        const radioBtn = document.getElementById(radioId);
        
        if (radioBtn) {
            radioBtn.checked = true;
            document.querySelectorAll('.view-container').forEach(container => {
                container.style.display = 'none';
            });
            
            const targetView = document.getElementById(lastViewMode + 'View');
            if (targetView) {
                targetView.style.display = 'block';
            } else {
                document.getElementById('templateView').style.display = 'block';
            }
        } else {
            // 默认显示模板视图
            document.getElementById('templateView').style.display = 'block';
            const defaultBtn = document.getElementById('templateMode');
            if (defaultBtn) defaultBtn.checked = true;
        }
    } catch (e) {
        console.error('恢复视图模式失败', e);
        // 默认显示模板视图
        const templateView = document.getElementById('templateView');
        if (templateView) templateView.style.display = 'block';
    }
    
    // 简单方式显示模板
    const container = document.getElementById('templatesContainer');
    if (container && reviewData.templates && reviewData.templates.length > 0) {
        try {
            container.innerHTML = reviewData.templates.map(template => `
                <div class="card template-card mb-3">
                    <div class="card-body">
                        <h5 class="card-title">${template.name || '未命名模板'}</h5>
                        <p class="card-text">${getTemplateTypeText(template.type) || '未分类'}</p>
                        <ul>
                            ${Array.isArray(template.items) ? template.items.map(item => `<li>${item}</li>`).join('') : '<li>无复盘项</li>'}
                        </ul>
                    </div>
                </div>
            `).join('');
        } catch (e) {
            console.error('渲染模板失败', e);
            container.innerHTML = '<div class="alert alert-warning">模板数据渲染失败</div>';
        }
    }
}

// 初始化复盘系统
function initReviewSystem() {
    try {
        // 0. 检查环境
        const envCheck = checkEnvironment();
        if (envCheck.hasIssues) {
            console.warn('环境检查发现问题:', envCheck.issues);
            // 显示问题但继续初始化
            setTimeout(() => {
                envCheck.issues.forEach(issue => {
                    showErrorBanner(issue);
                });
            }, 1000);
        }
        
        // 1. 缓存DOM元素
        cacheDOMElements();
        
        // 2. 加载数据
        loadData();
        
        // 3. 加载保存的草稿数据
        loadDraftData();
        
        // 4. 初始化事件监听
        initEventListeners();
        
        // 5. 更新UI
        updateUI();
        
        // 6. 显示当前日期
        updateCurrentDateDisplay();
        
        // 7. 恢复上次选择的视图模式
        restoreLastViewMode();
        
        // 8. 自动保存一次，确保数据格式统一
        setTimeout(() => {
            saveData();
        }, 2000);
    } catch (e) {
        handleSystemError(e, '初始化复盘系统失败');
        throw e; // 重新抛出以便外层捕获
    }
}

// 恢复上次选择的视图模式
function restoreLastViewMode() {
    try {
        // 从localStorage获取上次选择的视图模式
        const lastViewMode = localStorage.getItem('reviewViewMode');
        
        // 确定要使用的视图模式：如果localStorage中有值就用它，否则默认为'template'
        const viewMode = lastViewMode || 'template';
        
        // 模拟点击对应的radio按钮
        const radioId = viewMode + 'Mode';
        const radioBtn = document.getElementById(radioId);
        
        if (radioBtn) {
            // 设置按钮选中状态
            radioBtn.checked = true;
            // 切换到对应视图
            switchView(viewMode);
        } else {
            // 如果找不到对应按钮，默认显示模板视图
            console.warn('找不到视图按钮:', radioId);
            document.getElementById('templateMode').checked = true;
            switchView('template');
        }
    } catch (e) {
        console.error('恢复上次视图模式失败', e);
        // 失败时不阻止程序运行，设置默认视图
        document.getElementById('templateMode').checked = true;
        switchView('template');
    }
}

// 检查数据一致性
function ensureDataConsistency() {
    // 确保所有复盘记录引用的模板都存在，否则移除这些记录
    const validTemplateIds = new Set(reviewData.templates.map(t => t.id));
    const initialReviewCount = reviewData.reviews.length;
    
    reviewData.reviews = reviewData.reviews.filter(review => {
        return validTemplateIds.has(review.templateId);
    });
    
    const removedCount = initialReviewCount - reviewData.reviews.length;
    if (removedCount > 0) {
        console.warn(`删除了 ${removedCount} 条无效的复盘记录（引用了不存在的模板）`);
        showErrorBanner(`系统修复：删除了 ${removedCount} 条无效的复盘记录`);
    }
    
    // 确保所有日期格式正确
    reviewData.reviews.forEach(review => {
        try {
            new Date(review.date);
        } catch (e) {
            review.date = formatDateForInput(new Date());
        }
    });
    
    // 重新计算统计数据
    calculateStatistics();
}

// 缓存DOM元素
function cacheDOMElements() {
    try {
        domElements = {
            // 视图切换
            templateMode: document.getElementById('templateMode'),
            dailyMode: document.getElementById('dailyMode'),
            historyMode: document.getElementById('historyMode'),
            templateView: document.getElementById('templateView'),
            dailyReviewView: document.getElementById('dailyReviewView'),
            historyReviewView: document.getElementById('historyReviewView'),
            
            // 模板管理
            templateForm: document.getElementById('templateForm'),
            templateName: document.getElementById('templateName'),
            templateType: document.getElementById('templateType'),
            templateItems: document.getElementById('templateItems'),
            templatesContainer: document.getElementById('templatesContainer'),
            addItemBtn: document.getElementById('addItemBtn'),
            
            // 编辑模板相关
            editTemplateForm: document.getElementById('editTemplateForm'),
            editTemplateId: document.getElementById('editTemplateId'),
            editTemplateName: document.getElementById('editTemplateName'),
            editTemplateType: document.getElementById('editTemplateType'),
            editTemplateItems: document.getElementById('editTemplateItems'),
            editAddItemBtn: document.getElementById('editAddItemBtn'),
            saveEditTemplateBtn: document.getElementById('saveEditTemplateBtn'),
            
            // 复盘管理
            reviewDate: {
                value: formatDateForInput(new Date()) // 使用当前本地时间作为默认日期
            },
            reviewForm: document.getElementById('reviewForm'),
            saveReviewBtn: document.getElementById('saveReviewBtn'),
            templateSelect: document.getElementById('templateSelect'),
            reviewTemplateSelector: document.getElementById('reviewTemplateSelector'),
            reviewItemsContainer: document.getElementById('reviewItemsContainer'),
            
            // 历史复盘
            reviewHistoryAccordion: document.getElementById('reviewHistoryAccordion'),
            archiveDataBtn: document.getElementById('archiveDataBtn'),
            
            // 模态框
            confirmModal: document.getElementById('confirmModal'),
            confirmModalBody: document.getElementById('confirmModalBody'),
            confirmModalBtn: document.getElementById('confirmModalBtn'),
            
            // 统计
            totalReviewCount: document.getElementById('totalReviewCount'),
            dailyReviewCount: document.getElementById('dailyReviewCount'),
            weeklyReviewCount: document.getElementById('weeklyReviewCount'),
            monthlyReviewCount: document.getElementById('monthlyReviewCount'),
            streakCount: document.getElementById('streakCount'),
            
            // 搜索相关
            reviewSearchInput: document.getElementById('reviewSearchInput'),
            reviewSearchBtn: document.getElementById('reviewSearchBtn'),
            
            // 草稿相关按钮
            saveDraftBtn: document.getElementById('saveDraftBtn'),
            editDraftBtn: document.getElementById('editDraftBtn')
        };
    } catch (e) {
        console.error('缓存DOM元素失败', e);
        throw e;
    }
}

// 加载数据
function loadData() {
    try {
        // 从主系统存储中加载数据
        const savedData = localStorage.getItem(CONFIG.STORAGE_KEY);
        if (savedData) {
            // 计算数据大小
            const dataSize = calculateDataSize(savedData);
            
            const parsedData = JSON.parse(savedData);
            
            // 从主系统数据中提取复盘数据
            if (parsedData.reviewSystem) {
                // 如果存在压缩数据，则解压
                if (parsedData.reviewSystem.compressed) {
                    try {
                        reviewData = decompressReviewData(parsedData.reviewSystem.data);
                        console.log(`成功解压复盘数据，大小：${dataSize}`);
                        // 不在这里显示下载通知，只在显式同步时显示
                    } catch (e) {
                        console.error('解压复盘数据失败，将使用默认空数据', e);
                        reviewData = {
                            templates: [],
                            reviews: [],
                            statistics: {
                                totalCount: 0,
                                dailyCount: 0,
                                weeklyCount: 0,
                                monthlyCount: 0,
                                streakCount: 0
                            }
                        };
                    }
                } else {
                    reviewData = parsedData.reviewSystem.data;
                    // 不在这里显示下载通知
                }
                
                // 确保数据结构完整
                reviewData = {
                    templates: Array.isArray(reviewData.templates) ? reviewData.templates : [],
                    reviews: Array.isArray(reviewData.reviews) ? reviewData.reviews : [],
                    statistics: reviewData.statistics || {
                        totalCount: 0,
                        dailyCount: 0,
                        weeklyCount: 0,
                        monthlyCount: 0,
                        streakCount: 0
                    }
                };
                
                // 检查数据一致性
                ensureDataConsistency();
                
                console.log('成功加载复盘数据:', reviewData.templates.length, '个模板,', 
                           reviewData.reviews.length, '条记录');
            } else {
                // 主系统数据中不存在复盘数据，初始化空数据
                console.log('主系统中未找到复盘数据，初始化空数据');
                reviewData = {
                    templates: [],
                    reviews: [],
                    statistics: {
                        totalCount: 0,
                        dailyCount: 0,
                        weeklyCount: 0,
                        monthlyCount: 0,
                        streakCount: 0
                    }
                };
            }
        } else {
            // 主系统数据不存在，初始化空数据
            console.log('未找到主系统数据，初始化空数据');
            reviewData = {
                templates: [],
                reviews: [],
                statistics: {
                    totalCount: 0,
                    dailyCount: 0,
                    weeklyCount: 0,
                    monthlyCount: 0,
                    streakCount: 0
                }
            };
        }
        
        // 计算一次统计数据以确保数据一致性
        calculateStatistics();
    } catch (e) {
        console.error('加载数据失败', e);
        handleSystemError(e, '加载数据失败');
        
        // 出错时重置为空数据
        reviewData = {
            templates: [],
            reviews: [],
            statistics: {
                totalCount: 0,
                dailyCount: 0,
                weeklyCount: 0,
                monthlyCount: 0,
                streakCount: 0
            }
        };
    }
}

// 保存数据
function saveData(showNotification = false) {
    try {
        // 获取主系统数据
        const savedData = localStorage.getItem(CONFIG.STORAGE_KEY);
        let appData = savedData ? JSON.parse(savedData) : {};
        
        // 压缩复盘数据
        const compressedData = compressReviewData(reviewData);
        
        // 将复盘数据集成到主系统数据
        appData.reviewSystem = {
            compressed: true,
            data: compressedData,
            lastUpdate: new Date().toISOString()
        };
        
        // 计算数据大小
        const dataString = JSON.stringify(appData);
        const dataSize = calculateDataSize(dataString);
        
        // 保存回主系统存储
        localStorage.setItem(CONFIG.STORAGE_KEY, dataString);
        console.log(`复盘数据已保存并集成到主系统，大小：${dataSize}`);
        
        // 只在显式要求时显示通知
        if (showNotification) {
            showSuccess(`数据已保存到本地，大小：${dataSize}`);
        }
    } catch (e) {
        console.error('保存数据失败', e);
        handleSystemError(e, '保存数据失败');
    }
}

// 压缩复盘数据
function compressReviewData(data) {
    // 创建压缩后的数据结构
    const compressed = {
        t: [], // templates
        r: [], // reviews
        s: data.statistics // statistics (不压缩统计数据，因为它很小)
    };
    
    // 压缩模板数据
    if (data.templates && data.templates.length) {
        data.templates.forEach(template => {
            compressed.t.push({
                i: template.id,
                n: template.name,
                t: template.type,
                it: template.items
            });
        });
    }
    
    // 压缩复盘记录数据
    if (data.reviews && data.reviews.length) {
        data.reviews.forEach(review => {
            compressed.r.push({
                i: review.id,
                t: review.templateId,
                d: review.date,
                it: review.items
            });
        });
    }
    
    return compressed;
}

// 解压复盘数据
function decompressReviewData(compressed) {
    // 创建解压后的数据结构
    const decompressed = {
        templates: [],
        reviews: [],
        statistics: compressed.s || {
            totalCount: 0,
            dailyCount: 0,
            weeklyCount: 0,
            monthlyCount: 0,
            streakCount: 0
        }
    };
    
    // 解压模板数据
    if (compressed.t && compressed.t.length) {
        compressed.t.forEach(template => {
            decompressed.templates.push({
                id: template.i,
                name: template.n,
                type: template.t,
                items: template.it
            });
        });
    }
    
    // 解压复盘记录数据
    if (compressed.r && compressed.r.length) {
        compressed.r.forEach(review => {
            decompressed.reviews.push({
                id: review.i,
                templateId: review.t,
                date: review.d,
                items: review.it
            });
        });
    }
    
    return decompressed;
}

// 初始化事件监听
function initEventListeners() {
    try {
        // 视图模式切换
        document.querySelectorAll('input[name="reviewViewMode"]').forEach(radio => {
            radio.addEventListener('change', handleViewModeChange);
        });
        
        // 模板表单提交
        domElements.templateForm.addEventListener('submit', handleTemplateSubmit);
        
        // 添加复盘项按钮
        domElements.addItemBtn.addEventListener('click', addTemplateItem);
        
        // 选择模板事件
        domElements.templateSelect.addEventListener('change', handleTemplateSelect);
        
        // 移除复盘项按钮 (动态添加)
        document.querySelectorAll('.remove-item').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const item = e.target.closest('.input-group');
                item.remove();
            });
        });
        
        // 保存复盘按钮
        domElements.saveReviewBtn.addEventListener('click', saveReview);
        
        // 编辑模板中的添加项按钮
        document.getElementById('editAddItemBtn').addEventListener('click', addEditTemplateItem);
        
        // 保存编辑模板按钮
        document.getElementById('saveEditTemplateBtn').addEventListener('click', saveEditTemplate);
        
        // 确保编辑模板模态框的取消按钮和关闭按钮正常工作
        const closeButtons = document.querySelectorAll('#editTemplateModal .btn-close, #editTemplateModal .btn-secondary');
        closeButtons.forEach(button => {
            button.addEventListener('click', function() {
                try {
                    const modalEl = document.getElementById('editTemplateModal');
                    const modal = bootstrap.Modal.getInstance(modalEl);
                    if (modal) {
                        modal.hide();
                    }
                } catch (e) {
                    console.warn('使用原生方式关闭模态框失败', e);
                    // 尝试直接修改DOM
                    const modalEl = document.getElementById('editTemplateModal');
                    modalEl.classList.remove('show');
                    modalEl.style.display = 'none';
                    document.body.classList.remove('modal-open');
                    const backdrops = document.querySelectorAll('.modal-backdrop');
                    backdrops.forEach(el => el.remove());
                }
            });
        });
        
        // 数据归档按钮
        const archiveDataBtn = document.getElementById('archiveDataBtn');
        if (archiveDataBtn) {
            archiveDataBtn.addEventListener('click', showArchiveDialog);
        }
        
        // 初始化搜索功能
        initSearchFeature();
        
        // 草稿保存按钮
        if (domElements.saveDraftBtn) {
            domElements.saveDraftBtn.addEventListener('click', saveDraft);
        }
        
        // 草稿编辑按钮
        if (domElements.editDraftBtn) {
            domElements.editDraftBtn.addEventListener('click', loadDraftForEditing);
        }
        
    } catch (e) {
        console.error('初始化事件监听失败', e);
        throw e;
    }
}

// 初始化拖拽排序 - 完全禁用此功能
function initSortable() {
    // 已删除拖拽排序功能，不执行任何操作
    // console.log('拖拽排序功能已禁用');
    return;
}

// 显示错误横幅
function showErrorBanner(message) {
    try {
        const banner = document.createElement('div');
        banner.className = 'alert alert-warning alert-dismissible fade show';
        banner.role = 'alert';
        banner.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>提示：</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        
        // 查找一个合适的位置插入警告
        const containerEl = document.querySelector('.container');
        if (containerEl) {
            containerEl.insertBefore(banner, containerEl.firstChild);
        } else {
            document.body.insertBefore(banner, document.body.firstChild);
        }
        
        // 5秒后自动消失
        setTimeout(() => {
            if (document.body.contains(banner)) {
                const bsAlert = new bootstrap.Alert(banner);
                if (bsAlert && bsAlert.close) {
                    bsAlert.close();
                } else {
                    banner.remove();
                }
            }
        }, 5000);
    } catch (e) {
        console.error('显示错误横幅失败', e);
        // 降级到console
    }
}

// ================ 事件处理 ================

// 处理视图模式切换
function handleViewModeChange(e) {
    const viewMode = e.target.id.replace('Mode', '');
    switchView(viewMode);
    // 保存当前视图模式
    localStorage.setItem('reviewViewMode', viewMode);
}

// 处理模板提交
function handleTemplateSubmit(e) {
    e.preventDefault();
    
    const template = {
        id: Date.now().toString(),
        name: domElements.templateName.value,
        type: domElements.templateType.value,
        items: Array.from(domElements.templateItems.querySelectorAll('.template-item'))
            .map(input => input.value.trim())
            .filter(value => value)
    };
    
    if (template.items.length === 0) {
        showError('请至少添加一个复盘项');
        return;
    }
    
    reviewData.templates.push(template);
    saveData();
    updateUI();
    e.target.reset();
}

// 处理复盘提交
function handleReviewSubmit(e) {
    e.preventDefault();
    saveReview();
}

// 处理模板选择
function handleTemplateSelect(e) {
    const templateId = e.target.value;
    const template = reviewData.templates.find(t => t.id === templateId);
    if (template) {
        renderReviewItems(template);
    }
}

// ================ UI更新 ================

// 更新UI
function updateUI() {
    updateTemplatesView();
    updateStatistics();
    updateTemplateSelect();
    updateHistoryView();
}

// 更新模板视图
function updateTemplatesView() {
    domElements.templatesContainer.innerHTML = '';
    
    if (reviewData.templates.length === 0) {
        // 当没有模板时显示引导信息
        domElements.templatesContainer.innerHTML = `
            <div class="col-12 empty-state">
                <div class="mb-4">
                    <i class="fas fa-clipboard-list fa-4x"></i>
                </div>
                <h5>还没有复盘模板</h5>
                <p class="text-muted mb-4">使用左侧表单创建您的第一个复盘模板</p>
                <div class="example-items mt-4">
                    <div class="card mb-3 mx-auto" style="max-width: 400px;">
                        <div class="card-body">
                            <h6 class="mb-3">示例：日复盘可以包含</h6>
                            <ul class="text-muted">
                                <li>今日完成了什么</li>
                                <li>遇到了哪些问题</li>
                                <li>明日计划做什么</li>
                                <li>有什么需要改进的地方</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `;
        return;
    }
    
    reviewData.templates.forEach(template => {
        const card = createTemplateCard(template);
        domElements.templatesContainer.appendChild(card);
    });
}

// 更新统计信息
function updateStatistics() {
    const stats = reviewData.statistics;
    domElements.totalReviewCount.textContent = stats.totalCount;
    domElements.dailyReviewCount.textContent = stats.dailyCount;
    domElements.weeklyReviewCount.textContent = stats.weeklyCount;
    domElements.monthlyReviewCount.textContent = stats.monthlyCount;
    domElements.streakCount.textContent = stats.streakCount;
}

// 更新模板选择器
function updateTemplateSelect() {
    domElements.templateSelect.innerHTML = '<option value="">选择模板...</option>';
    reviewData.templates.forEach(template => {
        const option = document.createElement('option');
        option.value = template.id;
        option.textContent = template.name;
        domElements.templateSelect.appendChild(option);
    });
}

// 切换视图
function switchView(viewMode) {
    domElements.templateView.style.display = viewMode === 'template' ? 'block' : 'none';
    domElements.dailyReviewView.style.display = viewMode === 'daily' ? 'block' : 'none';
    domElements.historyReviewView.style.display = viewMode === 'history' ? 'block' : 'none';
    
    if (viewMode === 'daily') {
        const today = new Date();
        // 不再设置currentReviewDate文本，因为已经从UI中移除
        domElements.reviewDate.value = formatDateForInput(today);
    } else if (viewMode === 'history') {
        updateHistoryView();
    }
}

// ================ 辅助函数 ================

// 创建模板卡片
function createTemplateCard(template) {
    const card = document.createElement('div');
    card.className = 'card template-card fade-in';
    
    const badgeClass = {
        daily: 'badge-daily',
        weekly: 'badge-weekly',
        monthly: 'badge-monthly'
    }[template.type];
    
    // 为不同类型设置不同的图标
    const typeIcons = {
        daily: 'calendar-day',
        weekly: 'calendar-week',
        monthly: 'calendar-alt'
    };
    const typeIcon = typeIcons[template.type] || 'clipboard-list';
    
    card.innerHTML = `
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3 template-header" style="position: relative;">
                <h5 class="card-title mb-0">
                    <i class="fas fa-${typeIcon} me-2 text-${template.type === 'daily' ? 'info' : template.type === 'weekly' ? 'success' : 'warning'}"></i>
                    ${template.name}
                </h5>
                <div class="d-flex align-items-center">
                    <div class="template-actions me-2" style="display: none; position: absolute; right: 85px; top: 0;">
                        <button class="btn btn-sm btn-outline-primary edit-template" data-id="${template.id}" title="编辑模板">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger delete-template" data-id="${template.id}" title="删除模板">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <span class="badge ${badgeClass}">${getTemplateTypeText(template.type)}</span>
                </div>
            </div>
            
            <div class="d-flex justify-content-end mt-3">
                <button class="btn btn-sm btn-primary use-template" data-id="${template.id}">
                    <i class="fas fa-play me-1"></i> 立即使用
                </button>
            </div>
        </div>
    `;
    
    // 添加事件监听
    card.querySelector('.edit-template').addEventListener('click', () => editTemplate(template));
    card.querySelector('.delete-template').addEventListener('click', () => deleteTemplate(template.id));
    card.querySelector('.use-template').addEventListener('click', () => {
        // 选择此模板并切换到复盘视图
        document.getElementById('dailyMode').checked = true;
        handleViewModeChange({ target: { id: 'dailyMode' } });
        domElements.templateSelect.value = template.id;
        handleTemplateSelect({ target: { value: template.id } });
    });
    
    // 添加鼠标悬停效果显示按钮
    const header = card.querySelector('.template-header');
    const actions = card.querySelector('.template-actions');
    
    header.addEventListener('mouseenter', () => {
        actions.style.display = 'flex';
    });
    
    header.addEventListener('mouseleave', () => {
        actions.style.display = 'none';
    });
    
    return card;
}

// 添加模板项
function addTemplateItem() {
    const item = document.createElement('div');
    item.className = 'input-group mb-2 fade-in';
    item.innerHTML = `
        <input type="text" class="form-control template-item" placeholder="复盘项内容" required />
        <button type="button" class="btn btn-outline-danger remove-item" title="删除此项">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    const removeBtn = item.querySelector('.remove-item');
    removeBtn.addEventListener('click', () => {
        // 添加淡出动画
        item.classList.add('fade-out');
        setTimeout(() => item.remove(), 300);
    });
    
    domElements.templateItems.appendChild(item);
    
    // 自动聚焦到新添加的输入框
    setTimeout(() => {
        const input = item.querySelector('input');
        if (input) input.focus();
    }, 100);
}

// 编辑模板
function editTemplate(template) {
    try {
        document.getElementById('editTemplateId').value = template.id;
        document.getElementById('editTemplateName').value = template.name;
        document.getElementById('editTemplateType').value = template.type;
        
        const itemsContainer = document.getElementById('editTemplateItems');
        if (!itemsContainer) {
            showError('编辑面板初始化失败');
            return;
        }
        
        itemsContainer.innerHTML = '';
        
        template.items.forEach(item => {
            const itemElement = document.createElement('div');
            itemElement.className = 'input-group mb-2';
            itemElement.innerHTML = `
                <input type="text" class="form-control template-item" value="${item}" required />
                <button type="button" class="btn btn-outline-danger remove-item"><i class="fas fa-times"></i></button>
            `;
            
            itemElement.querySelector('.remove-item').addEventListener('click', () => itemElement.remove());
            itemsContainer.appendChild(itemElement);
        });
        
        if (domElements.editTemplateModal) {
            domElements.editTemplateModal.show();
        } else {
            const modal = new bootstrap.Modal(document.getElementById('editTemplateModal'));
            modal.show();
        }
    } catch (e) {
        console.error('编辑模板失败', e);
        showError('编辑模板失败: ' + e.message);
    }
}

// 删除模板
function deleteTemplate(templateId) {
    // 确保确认模态框可用
    const confirmModalEl = document.getElementById('confirmModal');
    if (!confirmModalEl) {
        if (confirm('确认要删除此模板吗？此操作不可恢复。')) {
            executeTemplateDelete(templateId);
        }
        return;
    }

    // 使用模态框确认
    document.getElementById('confirmModalBody').textContent = '确认要删除此模板吗？此操作不可恢复。';
    
    // 动态创建模态框实例
    let modalInstance = bootstrap.Modal.getInstance(confirmModalEl);
    if (!modalInstance) {
        try {
            modalInstance = new bootstrap.Modal(confirmModalEl);
        } catch (e) {
            console.error('创建模态框实例失败', e);
            // 降级到简单确认
            if (confirm('确认要删除此模板吗？此操作不可恢复。')) {
                executeTemplateDelete(templateId);
            }
            return;
        }
    }
    
    // 设置确认按钮事件
    const confirmBtn = document.getElementById('confirmModalBtn');
    if (confirmBtn) {
        // 移除旧的事件监听器
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
        
        // 添加新的事件监听器
        newConfirmBtn.addEventListener('click', function() {
            executeTemplateDelete(templateId);
            modalInstance.hide();
        });
    }
    
    // 显示模态框
    modalInstance.show();
}

// 执行模板删除操作
function executeTemplateDelete(templateId) {
    reviewData.templates = reviewData.templates.filter(t => t.id !== templateId);
    saveData();
    updateUI();
}

// 渲染复盘项
function renderReviewItems(template) {
    domElements.reviewItemsContainer.innerHTML = '';
    
    // 添加各复盘项
    template.items.forEach((item, index) => {
        const itemId = `question-${index}-${Date.now()}`;
        const itemElement = document.createElement('div');
        itemElement.className = 'mb-4 review-form-item review-item-container fade-in';
        itemElement.dataset.questionId = itemId;
        itemElement.style.animationDelay = `${index * 0.1}s`;
        
        itemElement.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <label class="form-label mb-0 review-question">
                    <i class="fas fa-edit me-2 text-primary"></i>${item}
                </label>
                <span class="badge bg-light text-muted">${index + 1}/${template.items.length}</span>
            </div>
            <textarea class="form-control review-answer" rows="3" placeholder="写下您的想法..." required></textarea>
            <div class="form-text text-end small">
                <span class="character-count">0</span> 个字符
            </div>
        `;
        
        // 添加字符计数功能
        const textarea = itemElement.querySelector('textarea');
        const charCount = itemElement.querySelector('.character-count');
        
        textarea.addEventListener('input', () => {
            const count = textarea.value.length;
            charCount.textContent = count;
            
            // 根据内容长度动态调整文本框高度
            textarea.style.height = 'auto';
            textarea.style.height = Math.max(100, textarea.scrollHeight) + 'px';
        });
        
        domElements.reviewItemsContainer.appendChild(itemElement);
    });
}

// 保存复盘
function saveReview() {
    try {
        // 获取选中的模板
        const templateId = domElements.templateSelect.value;
        if (!templateId) {
            showError('请先选择一个复盘模板');
            return;
        }
        
        // 获取选中的模板对象
        const template = reviewData.templates.find(t => t.id === templateId);
        if (!template) {
            showError('找不到所选模板');
            return;
        }
        
        // 收集复盘项回答
        const items = collectReviewFormData();
        
        // 创建复盘记录
        const review = {
            id: generateUniqueId(),
            templateId,
            templateName: template.name,
            templateType: template.type,
            date: formatDateForInput(new Date()),
            timestamp: new Date().getTime(),
            items
        };
        
        // 添加到复盘记录
        reviewData.reviews.push(review);
        
        // 保存数据 - 不显示同步上传成功提示
        saveData(false);
        
        // 更新统计信息
        calculateStatistics();
        
        // 更新历史视图
        updateHistoryView();
        
        // 清空表单
        domElements.reviewForm.reset();
        
        // 清除草稿数据
        localStorage.removeItem(CONFIG.DRAFT_KEY);
        draftData = null;
        
        // 更新草稿按钮状态
        loadDraftData();
        
        // 重置模板选择和清空复盘项容器
        domElements.templateSelect.value = '';
        domElements.reviewItemsContainer.innerHTML = '';
        
        // 显示成功消息
        // showSuccess('复盘已保存');
    } catch (error) {
        handleSystemError(error, '保存复盘失败');
    }
}

// 编辑模板中添加项
function addEditTemplateItem() {
    const item = document.createElement('div');
    item.className = 'input-group mb-2 fade-in';
    item.innerHTML = `
        <input type="text" class="form-control template-item" placeholder="复盘项内容" required />
        <button type="button" class="btn btn-outline-danger remove-item" title="删除此项">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    const removeBtn = item.querySelector('.remove-item');
    removeBtn.addEventListener('click', () => {
        // 添加淡出动画
        item.classList.add('fade-out');
        setTimeout(() => item.remove(), 300);
    });
    
    document.getElementById('editTemplateItems').appendChild(item);
    
    // 自动聚焦到新添加的输入框
    setTimeout(() => {
        const input = item.querySelector('input');
        if (input) input.focus();
    }, 100);
}

// 保存编辑后的模板
function saveEditTemplate() {
    const templateId = document.getElementById('editTemplateId').value;
    const templateName = document.getElementById('editTemplateName').value.trim();
    const templateType = document.getElementById('editTemplateType').value;
    
    if (!templateName) {
        showNotification('请输入模板名称', 'error');
        return;
    }
    
    const templateItems = Array.from(document.querySelectorAll('#editTemplateItems .template-item'))
        .map(input => input.value.trim())
        .filter(item => item !== '');
    
    if (templateItems.length === 0) {
        showNotification('请至少添加一个复盘项', 'error');
        return;
    }
    
    // 更新模板数据
    const template = reviewData.templates.find(t => t.id === templateId);
    if (template) {
        template.name = templateName;
        template.type = templateType;
        template.items = templateItems;
        template.updatedAt = new Date().toISOString();
        
        // 保存数据
        saveData();
        
        // 更新UI
        updateUI();
        
        // 关闭模态框 - 多种方式尝试确保成功关闭
        try {
            // 方式1: 使用Bootstrap API
            const modalEl = document.getElementById('editTemplateModal');
            const modal = bootstrap.Modal.getInstance(modalEl);
            if (modal) {
                modal.hide();
            } else {
                // 如果找不到实例，可能是因为DOM刷新了，尝试创建新实例关闭
                new bootstrap.Modal(modalEl).hide();
            }
        } catch (e) {
            console.error('关闭模态框失败 (方式1):', e);
            try {
                // 方式2: 直接操作DOM
                const modalEl = document.getElementById('editTemplateModal');
                modalEl.classList.remove('show');
                modalEl.style.display = 'none';
                document.body.classList.remove('modal-open');
                document.querySelectorAll('.modal-backdrop').forEach(el => el.remove());
            } catch (e2) {
                console.error('关闭模态框失败 (方式2):', e2);
                // 方式3: 尝试使用jQuery (如果存在)
                if (typeof $ !== 'undefined') {
                    try {
                        $('#editTemplateModal').modal('hide');
                    } catch (e3) {
                        console.error('关闭模态框失败 (所有方式):', e3);
                    }
                }
            }
        }
        
        // showNotification('模板已更新', 'success');
    }
}

// 更新历史复盘视图
function updateHistoryView() {
    domElements.reviewHistoryAccordion.innerHTML = '';
    
    // 按日期倒序排列复盘记录
    const sortedReviews = [...reviewData.reviews].sort((a, b) => new Date(b.date) - new Date(a.date));
    
    if (sortedReviews.length === 0) {
        domElements.reviewHistoryAccordion.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-history fa-3x mb-3"></i>
                <h5>暂无复盘记录</h5>
                <p class="text-muted mb-4">创建您的第一条复盘记录，记录下您的成长轨迹</p>
                <button id="startFirstReviewBtn" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i> 创建第一条复盘
                </button>
            </div>
        `;
        
        // 添加创建复盘按钮事件
        const startBtn = document.getElementById('startFirstReviewBtn');
        if (startBtn) {
            startBtn.addEventListener('click', () => {
                document.getElementById('dailyMode').checked = true;
                handleViewModeChange({ target: { id: 'dailyMode' } });
            });
        }
        
        // 隐藏批量删除相关控件
        if (document.getElementById('selectAllReviews')) {
            document.getElementById('selectAllReviews').style.display = 'none';
        }
        if (document.getElementById('batchDeleteReviewsBtn')) {
            document.getElementById('batchDeleteReviewsBtn').style.display = 'none';
        }
        
        return;
    }
    
    // 创建卡片容器
    const cardsContainer = document.createElement('div');
    cardsContainer.className = 'row g-3';
    
    // 按月份对复盘记录进行分组
    const reviewsByMonth = {};
    
    sortedReviews.forEach(review => {
        const date = new Date(review.date);
        const monthKey = `${date.getFullYear()}-${date.getMonth() + 1}`;
        
        if (!reviewsByMonth[monthKey]) {
            reviewsByMonth[monthKey] = [];
        }
        
        reviewsByMonth[monthKey].push(review);
    });
    
    // 创建月份分组
    Object.keys(reviewsByMonth).sort().reverse().forEach(monthKey => {
        const [year, month] = monthKey.split('-');
        const monthName = new Date(parseInt(year), parseInt(month) - 1, 1).toLocaleDateString('zh-CN', { year: 'numeric', month: 'long' });
        
        // 为每个月创建一个容器
        const monthContainer = document.createElement('div');
        monthContainer.className = 'month-container mb-4';
        monthContainer.dataset.monthKey = monthKey;
        
        // 添加月份标题，使其可点击
        const monthHeader = document.createElement('div');
        monthHeader.className = 'col-12 mb-2';
        monthHeader.innerHTML = `
            <h5 class="border-bottom pb-2 d-flex justify-content-between align-items-center month-title" style="cursor: pointer;">
                <span>
                    <i class="fas fa-calendar-alt me-2 text-primary"></i>${monthName}
                    <span class="badge bg-primary rounded-pill ms-2">${reviewsByMonth[monthKey].length}</span>
                </span>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </h5>
        `;
        monthContainer.appendChild(monthHeader);
        
        // 创建该月记录的容器
        const monthReviewsContainer = document.createElement('div');
        monthReviewsContainer.className = 'row g-3 month-reviews';
        monthContainer.appendChild(monthReviewsContainer);
        
        // 添加点击事件以展开/收起
        monthHeader.querySelector('.month-title').addEventListener('click', () => {
            const isVisible = monthReviewsContainer.style.display !== 'none';
            monthReviewsContainer.style.display = isVisible ? 'none' : '';
            
            // 切换图标
            const toggleIcon = monthHeader.querySelector('.toggle-icon');
            toggleIcon.className = isVisible ? 'fas fa-chevron-right toggle-icon' : 'fas fa-chevron-down toggle-icon';
        });
        
        // 添加该月的复盘记录卡片
        reviewsByMonth[monthKey].forEach(review => {
        const template = reviewData.templates.find(t => t.id === review.templateId);
        if (!template) return;
        
            // 正确处理日期：确保解析字符串日期时不受时区影响
            const dateParts = review.date.split('-');
            const year = parseInt(dateParts[0]);
            const month = parseInt(dateParts[1]) - 1; // 月份从0开始
            const day = parseInt(dateParts[2]);
            const date = new Date(year, month, day);
            const formattedDate = formatDate(date);
            
            // 获取星期几
            const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
            const weekday = weekdays[date.getDay()];
            
            const reviewCard = document.createElement('div');
            reviewCard.className = 'col-md-4 col-sm-6';
            reviewCard.dataset.reviewId = review.id;
            
        const badgeClass = {
            daily: 'bg-info',
            weekly: 'bg-success',
            monthly: 'bg-warning'
        }[template.type] || 'bg-secondary';
        
            const typeIcon = {
                daily: 'calendar-day',
                weekly: 'calendar-week',
                monthly: 'calendar-alt'
            }[template.type] || 'clipboard-list';
            
            reviewCard.innerHTML = `
                <div class="card h-100 review-card fade-in shadow-sm" style="cursor: pointer;">
                    <div class="card-body">
                        <div class="d-flex justify-content-center align-items-center">
                            <h5 class="card-title mb-0">${date.getDate()}日</h5>
                        </div>
                    </div>
                </div>
            `;
        
            // 添加卡片点击事件以查看详情
            const card = reviewCard.querySelector('.card');
            card.addEventListener('click', () => {
                showReviewDetail(review);
            });
            
            monthReviewsContainer.appendChild(reviewCard);
        });
        
        cardsContainer.appendChild(monthContainer);
    });
    
    // 将卡片容器添加到历史视图
    domElements.reviewHistoryAccordion.appendChild(cardsContainer);
    
    // 初始化搜索功能
    initSearchFeature();
}

// 显示复盘详情
function showReviewDetail(review) {
    try {
        // 获取模板信息
        const template = reviewData.templates.find(t => t.id === review.templateId);
        if (!template) {
            showError('找不到对应的模板信息，可能已被删除');
            return;
        }
        
        // 正确处理日期：确保解析字符串日期时不受时区影响
        const dateParts = review.date.split('-');
        const year = parseInt(dateParts[0]);
        const month = parseInt(dateParts[1]) - 1; // 月份从0开始
        const day = parseInt(dateParts[2]);
        const date = new Date(year, month, day);
        const formattedDate = formatDate(date);
        
        // 获取星期几
        const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
        const weekday = weekdays[date.getDay()];
        
        // 设置当前查看的复盘ID，用于删除操作
        document.getElementById('deleteReviewDetailBtn').dataset.reviewId = review.id;
        
        // 获取模板类型图标和颜色
        const typeIcons = {
            daily: ['calendar-day', 'info'],
            weekly: ['calendar-week', 'success'],
            monthly: ['calendar-alt', 'warning']
        };
        const [icon, color] = typeIcons[template.type] || ['clipboard-list', 'primary'];
        
        // 设置模态框标题
        document.getElementById('reviewDetailModalLabel').innerHTML = `
            <div style="display: flex; align-items: center; font-size: 0.95rem;">
                <i class="fas fa-${icon} me-1 text-${color}"></i>${formattedDate} <span class="ms-1 text-muted">(星期${weekday})</span>
                <span class="badge bg-${color} ms-2 small" style="padding: 0.25rem 0.5rem;">${getTemplateTypeText(template.type)}</span>
            </div>
        `;
        
        // 设置模态框内容
        const modalBody = document.getElementById('reviewDetailModalBody');
        modalBody.innerHTML = '';
        modalBody.style.padding = '0.75rem'; // 减少模态框内边距
        
        // 添加模板名称 - 减小底部间距
        const templateHeader = document.createElement('div');
        templateHeader.className = 'template-header mb-2 pb-1 border-bottom d-flex justify-content-between align-items-center';
        templateHeader.innerHTML = `
            <h6 class="fw-bold mb-0">
                <i class="fas fa-layer-group text-${color} me-2"></i>
                ${template.name}
            </h6>
            <span class="text-muted small">${template.items.length}项复盘</span>
        `;
        modalBody.appendChild(templateHeader);
        
        // 添加复盘内容
        const content = document.createElement('div');
        content.className = 'review-detail-content';
        
        // 确保review.items存在
        const reviewItems = Array.isArray(review.items) ? review.items : [];
        
        template.items.forEach((item, index) => {
            const itemEl = document.createElement('div');
            itemEl.className = 'review-detail-item mb-2 animate__animated animate__fadeIn'; // 减小底部间距
            itemEl.style.animationDelay = `${index * 0.1}s`;
            
            // 获取回答内容，进行格式化显示
            // 安全地获取answer，确保即使格式不对也不会出错
            let answer = '';
            if (reviewItems[index] && typeof reviewItems[index] === 'object') {
                answer = reviewItems[index].answer || '';
            } else if (typeof reviewItems[index] === 'string') {
                answer = reviewItems[index];
            }
            
            const formattedAnswer = answer 
                ? answer.trim() 
                : '<span class="text-muted fst-italic">无内容</span>';
            
            itemEl.innerHTML = `
                <div class="card shadow-sm border-0 review-item-card">
                    <div class="card-header bg-${color} bg-opacity-10 py-1 px-2">
                        <h6 class="fw-bold mb-0 small text-${color}">
                            <i class="fas fa-quote-left text-${color} me-1 opacity-50 small"></i>${item}
                        </h6>
                    </div>
                    <div class="card-body py-1 px-2">
                        <div class="review-answer">${formattedAnswer}</div>
                    </div>
                </div>
            `;
            
            content.appendChild(itemEl);
        });
        
        modalBody.appendChild(content);
        
        // 更新模态框类名
        const modalDialog = document.querySelector('#reviewDetailModal .modal-dialog');
        modalDialog.className = 'modal-dialog modal-lg';
        
        // 更新模态框样式
        const modalContent = document.querySelector('#reviewDetailModal .modal-content');
        modalContent.className = 'modal-content border-0 shadow-lg rounded-3';
        
        // 绑定删除按钮事件
        const deleteBtn = document.getElementById('deleteReviewDetailBtn');
        const newDeleteBtn = deleteBtn.cloneNode(true);
        deleteBtn.parentNode.replaceChild(newDeleteBtn, deleteBtn);
        
        newDeleteBtn.addEventListener('click', () => {
            const reviewId = newDeleteBtn.dataset.reviewId;
            if (reviewId) {
                // 先关闭详情模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('reviewDetailModal'));
                modal.hide();
                
                // 然后删除复盘
                deleteReview(reviewId);
            }
        });
        
        // 显示模态框
        const reviewDetailModal = new bootstrap.Modal(document.getElementById('reviewDetailModal'));
        reviewDetailModal.show();
    } catch (error) {
        console.error('显示复盘详情失败:', error);
        showError('显示复盘详情失败: ' + error.message);
    }
}

// 添加样式适配
document.addEventListener('DOMContentLoaded', function() {
    // 添加卡片相关的样式
    const style = document.createElement('style');
    style.textContent = `
        .review-card {
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
            border-radius: 12px;
            border: none;
        }
        .review-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1) !important;
        }
        .review-date-badge {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .review-detail-item h6 {
            color: #495057;
        }
        .review-detail-item .bg-light {
            background-color: #f8f9fa !important;
        }
    `;
    document.head.appendChild(style);
});

// ================ 工具函数 ================

// 格式化日期
function formatDate(date) {
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// 格式化日期为输入框格式
function formatDateForInput(date) {
    // 确保使用本地时间而不是UTC
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// 获取模板类型文本
function getTemplateTypeText(type) {
    const types = {
        daily: '日复盘',
        weekly: '周复盘',
        monthly: '月复盘'
    };
    return types[type] || type;
}

// 显示错误信息
function showError(message) {
    showNotification(message, 'error');
}

// 显示成功信息
function showSuccess(message) {
    showNotification(message, 'success');
}

// 显示通知（简化版本，确保即使在初始化失败时也能显示）
function showNotification(message, type = 'info') {
    // 如果早期初始化失败，使用alert作为后备
    if (typeof document === 'undefined' || !document.body) {
        alert(message);
        return;
    }
    
    try {
        // 移除之前的通知
        const existingNotifications = document.querySelectorAll('.custom-notification');
        existingNotifications.forEach(notification => {
            notification.remove();
        });
        
        const notification = document.createElement('div');
        notification.className = `custom-notification notification-${type} fade-in`;
        // 调整样式，确保在右上角显示
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '9999';
        
        const icons = {
            success: '<i class="fas fa-check-circle"></i>',
            error: '<i class="fas fa-exclamation-circle"></i>',
            info: '<i class="fas fa-info-circle"></i>'
        };
        
        notification.innerHTML = `
            <div class="notification-icon">${icons[type] || icons.info}</div>
            <div class="notification-message">${message}</div>
        `;
        
        document.body.appendChild(notification);
        
        // 添加关闭按钮
        const closeBtn = document.createElement('div');
        closeBtn.className = 'notification-close';
        closeBtn.innerHTML = '<i class="fas fa-times"></i>';
        closeBtn.addEventListener('click', () => {
            notification.classList.add('fade-out');
            setTimeout(() => notification.remove(), 300);
        });
        notification.appendChild(closeBtn);
        
        // 自动消失 - 减少显示时间
        setTimeout(() => {
            if (document.body.contains(notification)) {
                notification.classList.add('fade-out');
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        notification.remove();
                    }
                }, 300);
            }
        }, 1000); // 从1500毫秒减少到1000毫秒
    } catch (e) {
        // 如果通知显示失败，使用alert作为后备
        console.error('显示通知失败', e);
        alert(message);
    }
}

// 统一的错误处理函数
function handleSystemError(error, context = '') {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const fullMessage = context ? `${context}: ${errorMessage}` : errorMessage;
    
    // 记录错误
    console.error(fullMessage, error);
    
    // 显示错误通知
    showError(fullMessage);
    
    // 对于严重错误，可以添加额外处理
    if (error instanceof TypeError || error instanceof ReferenceError) {
        showErrorBanner('系统遇到严重错误，部分功能可能无法正常工作');
    }
}

// 修改原有的handleError函数调用为新的handleSystemError
// 例如：
try {
    // 代码...
} catch (e) {
    handleSystemError(e, '操作失败');
}

// 更新批量删除按钮状态
function updateBatchDeleteButton() {
    const batchDeleteBtn = document.getElementById('batchDeleteReviewsBtn');
    const checkedBoxes = document.querySelectorAll('.review-select-checkbox:checked');
    
    if (checkedBoxes.length > 0) {
        batchDeleteBtn.style.display = '';
        batchDeleteBtn.textContent = `删除选中的 ${checkedBoxes.length} 项`;
    } else {
        batchDeleteBtn.style.display = 'none';
    }
}

// 批量删除复盘记录
function batchDeleteReviews() {
    const checkedBoxes = document.querySelectorAll('.review-select-checkbox:checked');
    if (checkedBoxes.length === 0) return;
    
    if (confirm(`确定要删除选中的 ${checkedBoxes.length} 条复盘记录吗？此操作不可恢复。`)) {
        const idsToDelete = Array.from(checkedBoxes).map(checkbox => checkbox.value);
        
        // 从数据中删除记录
        reviewData.reviews = reviewData.reviews.filter(review => !idsToDelete.includes(review.id));
        
        // 更新统计和保存
        calculateStatistics();
        saveData();
        updateUI();
        
        showSuccess(`已删除 ${idsToDelete.length} 条复盘记录`);
    }
}

// 初始化搜索功能
function initSearchFeature() {
    const searchInput = document.getElementById('reviewSearchInput');
    const searchBtn = document.getElementById('reviewSearchBtn');
    
    if (!searchInput || !searchBtn) return;
    
    // 绑定搜索按钮点击事件
    searchBtn.addEventListener('click', () => {
        searchReviews(searchInput.value.trim());
    });
    
    // 绑定输入框回车事件
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            searchReviews(searchInput.value.trim());
        }
    });
}

// 搜索复盘记录
function searchReviews(keyword) {
    if (!keyword) {
        // 如果关键词为空，显示所有记录
        document.querySelectorAll('.month-container').forEach(container => {
            container.style.display = '';
        });
        document.querySelectorAll('.col-md-4.col-sm-6').forEach(item => {
            item.style.display = '';
        });
        
        // 确保月份内容可见
        document.querySelectorAll('.month-reviews').forEach(container => {
            container.style.display = '';
        });
        
        // 更新月份图标
        document.querySelectorAll('.toggle-icon').forEach(icon => {
            icon.className = 'fas fa-chevron-down toggle-icon';
        });
        
        return;
    }
    
    // 显示所有月份容器
    document.querySelectorAll('.month-container').forEach(container => {
        container.style.display = '';
    });
    
    // 确保所有月份的内容是可见的，以便搜索
    document.querySelectorAll('.month-reviews').forEach(container => {
        container.style.display = '';
    });
    
    // 转为小写进行不区分大小写的搜索
    const lowerKeyword = keyword.toLowerCase();
    
    // 遍历所有复盘记录卡片
    document.querySelectorAll('.col-md-4.col-sm-6').forEach(item => {
        const reviewId = item.dataset.reviewId;
        const review = reviewData.reviews.find(r => r.id === reviewId);
        
        if (!review) {
            item.style.display = 'none';
            return;
        }
        
        // 检查日期、模板名称和复盘内容
        const template = reviewData.templates.find(t => t.id === review.templateId);
        if (!template) {
            item.style.display = 'none';
            return;
        }
        
        // 构建可搜索文本
        const searchableText = [
            review.date,
            template.name,
            ...template.items,
            ...review.items
        ].join(' ').toLowerCase();
        
        // 如果任何字段包含关键词，则显示此复盘记录
        if (searchableText.includes(lowerKeyword)) {
            item.style.display = '';
            item.classList.add('search-highlight');
            setTimeout(() => item.classList.remove('search-highlight'), 2000);
        } else {
            item.style.display = 'none';
        }
    });
    
    // 隐藏没有匹配记录的月份容器
    document.querySelectorAll('.month-container').forEach(container => {
        const hasVisibleCards = Array.from(container.querySelectorAll('.col-md-4.col-sm-6')).some(
            card => card.style.display !== 'none'
        );
        container.style.display = hasVisibleCards ? '' : 'none';
    });
    
    // 更新月份箭头图标
    document.querySelectorAll('.toggle-icon').forEach(icon => {
        icon.className = 'fas fa-chevron-down toggle-icon';
    });
}

// 删除复盘记录
function deleteReview(reviewId) {
    // 类似模板，确保确认模态框可用
    const confirmModalEl = document.getElementById('confirmModal');
    if (!confirmModalEl) {
        if (confirm('确认要删除此复盘记录吗？此操作不可恢复。')) {
            executeReviewDelete(reviewId);
        }
        return;
    }
    
    document.getElementById('confirmModalBody').textContent = '确认要删除此复盘记录吗？此操作不可恢复。';
    
    // 动态创建模态框实例
    let modalInstance = bootstrap.Modal.getInstance(confirmModalEl);
    if (!modalInstance) {
        try {
            modalInstance = new bootstrap.Modal(confirmModalEl);
        } catch (e) {
            console.error('创建模态框实例失败', e);
            // 降级到简单确认
            if (confirm('确认要删除此复盘记录吗？此操作不可恢复。')) {
                executeReviewDelete(reviewId);
            }
            return;
        }
    }
    
    // 设置确认按钮事件
    const confirmBtn = document.getElementById('confirmModalBtn');
    if (confirmBtn) {
        // 移除旧的事件监听器
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
        
        // 添加新的事件监听器
        newConfirmBtn.addEventListener('click', function() {
            executeReviewDelete(reviewId);
            modalInstance.hide();
        });
    }
    
    // 显示模态框
    modalInstance.show();
}

// 执行复盘记录删除操作
function executeReviewDelete(reviewId) {
    // 从数据模型中删除记录
    reviewData.reviews = reviewData.reviews.filter(r => r.id !== reviewId);
    calculateStatistics();
    saveData();
    
    // 直接从DOM中移除对应的卡片元素，而不是更新整个历史视图
    const cardElement = document.querySelector(`.col-md-4.col-sm-6[data-review-id="${reviewId}"]`);
    if (cardElement) {
        // 获取卡片所在的月份容器
        const monthContainer = cardElement.closest('.month-container');
        
        // 添加淡出动画效果后移除元素
        cardElement.classList.add('fade-out');
        setTimeout(() => {
            cardElement.remove();
            
            // 检查月份容器中是否还有卡片
            if (monthContainer) {
                const remainingCards = monthContainer.querySelectorAll('.col-md-4.col-sm-6');
                if (remainingCards.length === 0) {
                    // 如果该月没有卡片了，隐藏整个月份容器
                    monthContainer.classList.add('fade-out');
                    setTimeout(() => {
                        monthContainer.remove();
                        
                        // 检查是否所有月份都被删除了
                        const allMonths = document.querySelectorAll('.month-container');
                        if (allMonths.length === 0) {
                            // 如果没有任何月份了，显示空状态提示
                            domElements.reviewHistoryAccordion.innerHTML = `
                                <div class="empty-state">
                                    <i class="fas fa-history fa-3x mb-3"></i>
                                    <h5>暂无复盘记录</h5>
                                    <p class="text-muted mb-4">创建您的第一条复盘记录，记录下您的成长轨迹</p>
                                    <button id="startFirstReviewBtn" class="btn btn-primary">
                                        <i class="fas fa-edit me-2"></i> 创建第一条复盘
                                    </button>
                                </div>
                            `;
                            
                            // 添加创建复盘按钮事件
                            const startBtn = document.getElementById('startFirstReviewBtn');
                            if (startBtn) {
                                startBtn.addEventListener('click', () => {
                                    document.getElementById('dailyMode').checked = true;
                                    handleViewModeChange({ target: { id: 'dailyMode' } });
                                });
                            }
                            
                            // 隐藏批量删除相关控件
                            if (document.getElementById('selectAllReviews')) {
                                document.getElementById('selectAllReviews').style.display = 'none';
                            }
                            if (document.getElementById('batchDeleteReviewsBtn')) {
                                document.getElementById('batchDeleteReviewsBtn').style.display = 'none';
                            }
                        }
                    }, 300);
                }
            }
        }, 300);
    }
    
    // 只更新统计数据，不重新渲染历史视图
    updateStatistics();
    
    // 关闭详情模态框
    const reviewDetailModal = bootstrap.Modal.getInstance(document.getElementById('reviewDetailModal'));
    if (reviewDetailModal) {
        reviewDetailModal.hide();
    }
}

// 计算统计数据
function calculateStatistics() {
    const stats = reviewData.statistics;
    
    // 总复盘数
    stats.totalCount = reviewData.reviews.length;
    
    // 各类型复盘数
    const typeCounts = { daily: 0, weekly: 0, monthly: 0 };
    reviewData.reviews.forEach(review => {
        const template = reviewData.templates.find(t => t.id === review.templateId);
        if (template && typeCounts.hasOwnProperty(template.type)) {
            typeCounts[template.type]++;
        }
    });
    
    stats.dailyCount = typeCounts.daily;
    stats.weeklyCount = typeCounts.weekly;
    stats.monthlyCount = typeCounts.monthly;
    
    // 计算连续天数
    const dailyDates = reviewData.reviews
        .filter(review => {
            const template = reviewData.templates.find(t => t.id === review.templateId);
            return template && template.type === 'daily';
        })
        .map(review => review.date)
        .sort();
    
    if (dailyDates.length === 0) {
        stats.streakCount = 0;
        return;
    }
    
    // 检查最后一次复盘是否是今天或昨天
    const today = formatDateForInput(new Date());
    const yesterday = formatDateForInput(new Date(Date.now() - 86400000));
    const lastDate = dailyDates[dailyDates.length - 1];
    
    if (lastDate !== today && lastDate !== yesterday) {
        stats.streakCount = 0;
        return;
    }
    
    // 计算最长连续天数
    let streak = 1;
    let maxStreak = 1;
    for (let i = 1; i < dailyDates.length; i++) {
        const curr = new Date(dailyDates[i]);
        const prev = new Date(dailyDates[i-1]);
        
        const diffDays = Math.floor((curr - prev) / 86400000);
        
        if (diffDays === 1) {
            streak++;
            maxStreak = Math.max(maxStreak, streak);
        } else if (diffDays > 1) {
            streak = 1;
        }
    }
    
    stats.streakCount = maxStreak;
}

// 计算数据大小并格式化显示
function calculateDataSize(dataString) {
    try {
        const bytes = new Blob([dataString]).size;
        
        if (bytes < 1024) {
            return `${bytes} 字节`;
        } else if (bytes < 1024 * 1024) {
            return `${(bytes / 1024).toFixed(2)} KB`;
        } else {
            return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
        }
    } catch (e) {
        console.error('计算数据大小失败', e);
        return '未知大小';
    }
}

// 数据归档功能 - 只保留最近几个月的数据
function archiveReviewData(monthsToKeep) {
    if (!confirm(`确定要归档数据吗？只会保留最近${monthsToKeep}个月的复盘记录，其余记录将被删除。此操作不可恢复！`)) {
        return;
    }
    
    // 记录原始数据量
    const originalCount = reviewData.reviews.length;
    
    // 获取当前日期
    const now = new Date();
    
    // 计算截止日期
    const cutoffDate = new Date(now.getFullYear(), now.getMonth() - monthsToKeep, now.getDate());
    
    // 过滤复盘记录，只保留截止日期之后的记录
    reviewData.reviews = reviewData.reviews.filter(review => {
        const reviewDate = new Date(review.date);
        return reviewDate >= cutoffDate;
    });
    
    // 计算删除的记录数
    const deletedCount = originalCount - reviewData.reviews.length;
    
    // 重新计算统计数据
    calculateStatistics();
    
    // 保存更新后的数据
    saveData(true);
    
    // 更新UI
    updateUI();
    
    // 显示归档结果
    showSuccess(`归档成功！已删除${deletedCount}条过期记录，保留最近${monthsToKeep}个月的${reviewData.reviews.length}条记录。`);
}

// 显示归档对话框
function showArchiveDialog() {
    // 创建模态对话框HTML
    const modalHTML = `
    <div class="modal fade" id="archiveModal" tabindex="-1" aria-labelledby="archiveModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="archiveModalLabel">数据归档设置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>归档功能将只保留最近几个月的复盘记录，以减少数据量。</p>
                    <p>当前复盘记录数：<strong>${reviewData.reviews.length}</strong>条</p>
                    <div class="mb-3">
                        <label for="archiveMonthsToKeep" class="form-label">保留最近几个月的数据：</label>
                        <select class="form-select" id="archiveMonthsToKeep">
                            <option value="1">1个月</option>
                            <option value="2">2个月</option>
                            <option value="3">3个月</option>
                            <option value="4">4个月</option>
                            <option value="5">5个月</option>
                            <option value="6" selected>6个月</option>
                        </select>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        警告：此操作将永久删除旧数据，无法恢复！
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="executeArchiveBtn">执行归档</button>
                </div>
            </div>
        </div>
    </div>
    `;
    
    // 添加模态框到body
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // 获取模态框元素
    const modalElement = document.getElementById('archiveModal');
    
    // 初始化模态框
    const modal = new bootstrap.Modal(modalElement);
    
    // 添加归档执行按钮事件
    document.getElementById('executeArchiveBtn').addEventListener('click', function() {
        const monthsToKeep = parseInt(document.getElementById('archiveMonthsToKeep').value);
        modal.hide();
        // 延迟执行归档操作，等待模态框完全关闭
        setTimeout(() => {
            archiveReviewData(monthsToKeep);
        }, 500);
    });
    
    // 显示模态框
    modal.show();
    
    // 在模态框隐藏后删除它
    modalElement.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modalElement);
    });
}

// 更新当前日期显示
function updateCurrentDateDisplay() {
    const dateElement = document.getElementById('formattedCurrentDate');
    if (dateElement) {
        const today = new Date();
        const options = { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric',
            weekday: 'long'
        };
        dateElement.textContent = today.toLocaleDateString('zh-CN', options);
    }
}

// 保存草稿
function saveDraft() {
    try {
        // 获取当前选中的模板
        const templateId = domElements.templateSelect.value;
        if (!templateId) {
            showError('请先选择一个复盘模板');
            return;
        }
        
        // 收集表单数据
        const formData = collectReviewFormData();
        
        // 创建草稿数据对象
        const draft = {
            templateId,
            items: formData,
            timestamp: new Date().getTime(),
            date: formatDateForInput(new Date())
        };
        
        // 保存到本地存储
        localStorage.setItem(CONFIG.DRAFT_KEY, JSON.stringify(draft));
        
        // 更新草稿按钮状态
        loadDraftData();
        
        // 重置模板选择和清空复盘项容器
        domElements.templateSelect.value = '';
        domElements.reviewItemsContainer.innerHTML = '';
        
        // 显示成功消息
        // showSuccess('草稿已保存');
    } catch (error) {
        handleSystemError(error, '保存草稿失败');
    }
}

// 收集复盘表单数据
function collectReviewFormData() {
    const items = [];
    const itemContainers = document.querySelectorAll('.review-item-container');
    
    itemContainers.forEach(container => {
        const questionId = container.dataset.questionId;
        const questionText = container.querySelector('.review-question').textContent;
        const answerElement = container.querySelector('.review-answer');
        
        items.push({
            id: questionId,
            question: questionText,
            answer: answerElement ? answerElement.value : ''
        });
    });
    
    return items;
}

// 加载草稿数据
function loadDraftData() {
    try {
        const draftJson = localStorage.getItem(CONFIG.DRAFT_KEY);
        if (draftJson) {
            draftData = JSON.parse(draftJson);
            
            // 检查草稿数据是否存在
            const hasDraft = draftData && draftData.templateId && draftData.items;
            
            // 更新"编辑草稿"按钮状态
            if (domElements.editDraftBtn) {
                if (hasDraft) {
                    domElements.editDraftBtn.classList.remove('disabled');
                    domElements.editDraftBtn.disabled = false;
                    
                    // 计算草稿保存时间
                    const draftTime = new Date(draftData.timestamp);
                    const today = new Date();
                    const isToday = draftTime.getDate() === today.getDate() && 
                                   draftTime.getMonth() === today.getMonth() &&
                                   draftTime.getFullYear() === today.getFullYear();
                    
                    const timeText = isToday ? 
                        `今天 ${draftTime.getHours().toString().padStart(2, '0')}:${draftTime.getMinutes().toString().padStart(2, '0')}` : 
                        draftTime.toLocaleDateString('zh-CN');
                    
                    // 添加工具提示显示最后保存时间
                    domElements.editDraftBtn.setAttribute('title', `上次保存：${timeText}`);
                    new bootstrap.Tooltip(domElements.editDraftBtn);
                } else {
                    domElements.editDraftBtn.classList.add('disabled');
                    domElements.editDraftBtn.disabled = true;
                    domElements.editDraftBtn.removeAttribute('title');
                }
            }
        }
    } catch (error) {
        console.error('加载草稿数据失败:', error);
    }
}

// 从草稿加载编辑
function loadDraftForEditing() {
    try {
        // 重新读取草稿数据，确保使用最新的草稿
        const draftJson = localStorage.getItem(CONFIG.DRAFT_KEY);
        if (draftJson) {
            draftData = JSON.parse(draftJson);
        }
        
        if (!draftData || !draftData.templateId) {
            showError('没有可用的草稿数据');
            return;
        }
        
        // 检查模板是否存在
        const template = reviewData.templates.find(t => t.id === draftData.templateId);
        if (!template) {
            showError('草稿对应的模板已不存在');
            localStorage.removeItem(CONFIG.DRAFT_KEY); // 清除无效草稿
            return;
        }
        
        // 切换到今日复盘视图
        switchView('daily');
        
        // 选择对应的模板
        domElements.templateSelect.value = draftData.templateId;
        
        // 先手动触发模板渲染
        renderReviewItems(template);
        
        console.log('渲染模板项目完成，准备填充草稿内容');
        
        // 等待模板内容渲染完成
        setTimeout(() => {
            console.log('开始填充草稿内容，草稿数据:', draftData);
            console.log('当前模板ID:', template.id);
            
            // 如果草稿数据是旧格式（没有id字段），直接按顺序填充
            if (draftData.items && draftData.items.length > 0 && !draftData.items[0].id) {
                console.log('检测到旧格式草稿数据，按顺序填充');
                const textareas = document.querySelectorAll('.review-item-container .review-answer');
                draftData.items.forEach((item, index) => {
                    if (textareas[index]) {
                        const answer = typeof item === 'string' ? item : (item.answer || '');
                        textareas[index].value = answer;
                        textareas[index].dispatchEvent(new Event('input'));
                    }
                });
            } else {
                // 新格式，按id填充
                const containers = document.querySelectorAll('.review-item-container');
                console.log('找到', containers.length, '个复盘项容器');
                
                // 打印所有容器的ID以进行调试
                containers.forEach((container, idx) => {
                    console.log(`容器${idx}: data-question-id="${container.dataset.questionId}"`);
                });
                
                // 如果无法按ID匹配，则按顺序填充
                if (containers.length > 0) {
                    template.items.forEach((templateItem, index) => {
                        const container = containers[index];
                        if (container && draftData.items && draftData.items[index]) {
                            const answerElement = container.querySelector('.review-answer');
                            if (answerElement) {
                                const answer = draftData.items[index].answer || '';
                                answerElement.value = answer;
                                answerElement.dispatchEvent(new Event('input'));
                                console.log(`填充第${index+1}项: "${templateItem.substring(0, 20)}..." => "${answer.substring(0, 20)}..."`);
                            }
                        }
                    });
                }
            }
            
            // 显示成功消息
            // showSuccess('草稿已加载');
        }, 300); // 增加等待时间，确保DOM已完全渲染
    } catch (error) {
        console.error('加载草稿数据失败:', error);
        handleSystemError(error, '加载草稿数据失败');
    }
}

// 生成唯一ID
function generateUniqueId() {
    return Date.now().toString() + '-' + Math.random().toString(36).substr(2, 9);
}