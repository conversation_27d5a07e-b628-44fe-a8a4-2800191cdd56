/* Responsive design styles */

/* Small devices (phones) */
@media (max-width: 576px) {
    .event-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .event-name {
        margin-bottom: 10px;
    }
    
    .time-input {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .event-card .btn {
        margin-left: 0;
        margin-right: 8px;
    }

    /* 移动设备待办事项布局 - 每个象限单独一行 */
    .quadrant-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
        gap: 0.8rem;
    }
    
    .quadrant {
        margin: 0.25rem 0;
        min-height: 180px;
    }
    
    .add-todo {
        flex-direction: column;
        gap: 0.3rem;
    }
    
    .add-todo input, 
    .add-todo button {
        width: 100%;
    }
    
    .todo-item {
        padding: 0.6rem 0.5rem;
        flex-wrap: wrap;
    }
    
    /* 优化待办项内部布局 */
    .todo-text {
        width: calc(100% - 30px);
        margin: 0.2rem 0;
        word-break: break-word;
        display: block;
    }
    
    .todo-actions {
        width: 100%;
        justify-content: flex-end;
        margin-top: 0.3rem;
    }
}

/* Medium devices (tablets) */
@media (max-width: 768px) {
    .container {
        padding: 0.75rem;
    }
    
    .card-header {
        flex-direction: column;
        align-items: stretch !important;
        gap: 0.5rem;
        padding: 0.75rem;
    }
    
    .card-header > div {
        justify-content: center;
        width: 100%;
    }
    
    .btn-group {
        margin-top: 0.5rem;
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
    }
    
    .btn-group .btn {
        flex: 1 1 auto;
        min-width: calc(33.333% - 0.5rem);
        text-align: center;
        font-size: 0.8rem !important;
        padding: 0.3rem 0.5rem;
    }
    
    .card-header .btn,
    .card-header .btn-group {
        width: 100%;
        margin: 0.25rem 0;
    }
    
    .goal-container {
        grid-template-columns: 1fr;
        gap: 0.5rem;
        padding: 0.5rem;
    }
    
    .nav-buttons {
        width: 100%;
        justify-content: flex-start;
    }
    
    .archive-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .categories-container {
        grid-template-columns: 1fr;
    }
    
    .data-item .btn-group {
        opacity: 1;
    }
    
    .inspiration-filters {
        padding: 0.5rem;
        margin: 0.5rem 0;
    }
    
    .tag-btn {
        padding: 0.25rem 0.75rem;
        font-size: 0.8rem;
    }
    
    /* 平板设备下的待办事项布局 */
    .quadrant-grid {
        grid-template-columns: 1fr;
        gap: 0.8rem;
    }
}

/* Large devices (desktops) */
@media (max-width: 1200px) and (min-width: 769px) {
    .quadrant-grid {
        grid-template-columns: minmax(300px, 1fr) minmax(300px, 1fr);
        gap: 1rem;
    }
    
    .quadrant {
        min-height: 250px;
    }
}

/* Extra large devices */
@media (max-width: 1400px) {
    .quadrant-grid {
        max-width: 1200px;
    }
} 