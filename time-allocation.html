<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />
    <title>24小时时间分配 - ST计划</title>
    <!-- 添加网站图标 -->
    <link rel="icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <link rel="shortcut icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <!-- 内联PNG图标，用于不支持SVG的浏览器 -->
    <link rel="icon" href="data:image/png;base64,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" type="image/png">
    <!-- 样式文件 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    <link rel="stylesheet" type="text/css" href="css/main.css">
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-light: #818cf8;
            --primary-dark: #4f46e5;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --border-radius: 0.375rem;
            --border-radius-lg: 0.5rem;
        }

        body {
            overflow-x: hidden;
            -webkit-overflow-scrolling: touch;
            touch-action: pan-y;
            position: relative;
            font-family: 'Nunito', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f9fafb;
            color: var(--gray-700);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            padding: 0 15px;
        }

        .card {
            border: none;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid var(--gray-200);
            padding: 1.25rem 1.5rem;
        }

        .card-header h5 {
            font-weight: 700;
            color: var(--gray-800);
            font-size: 1.2rem;
            margin: 0;
        }

        .card-body {
            padding: 1.5rem;
            background-color: white;
        }

        /* 24小时时间分配样式 */
        .allocation-task-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
            transition: all 0.2s ease;
        }

        .allocation-task-item:hover {
            background: #e9ecef;
            border-color: var(--primary-color);
            transform: translateY(-1px);
        }

        .allocation-task-item.priority-high {
            border-left: 4px solid var(--danger-color);
        }

        .allocation-task-item.priority-medium {
            border-left: 4px solid var(--warning-color);
        }

        .allocation-task-item.priority-low {
            border-left: 4px solid var(--info-color);
        }

        .hour-slot {
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 4px;
            min-height: 60px;
            background: #fff;
            transition: all 0.2s ease;
            cursor: pointer;
            position: relative;
        }

        .hour-slot:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .hour-slot.occupied {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            border-color: var(--primary-color);
            border-width: 2px;
        }

        .hour-slot.occupied.priority-high {
            background: linear-gradient(135deg, #ffebee, #fce4ec);
            border-color: var(--danger-color);
        }

        .hour-slot.occupied.priority-medium {
            background: linear-gradient(135deg, #fff8e1, #fef7e0);
            border-color: var(--warning-color);
        }

        .hour-slot.occupied.priority-low {
            background: linear-gradient(135deg, #e8f5e8, #f1f8e9);
            border-color: var(--info-color);
        }

        .hour-slot .task-in-slot {
            background: var(--primary-color);
            color: white;
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 13px;
            display: inline-block;
            margin: 2px;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .hour-slot .task-in-slot.priority-high {
            background: var(--danger-color);
        }

        .hour-slot .task-in-slot.priority-medium {
            background: var(--warning-color);
        }

        .hour-slot .task-in-slot.priority-low {
            background: var(--info-color);
        }

        .hour-slot .hour-label {
            font-weight: 700;
            color: var(--gray-700);
            font-size: 14px;
            margin-bottom: 5px;
        }

        .hour-slot .time-info {
            font-size: 11px;
            color: var(--gray-500);
            margin-top: 5px;
        }

        .hour-slot.empty {
            background: #f8f9fa;
            border-style: dashed;
            opacity: 0.7;
        }

        .hour-slot.empty:hover {
            background: #e9ecef;
            opacity: 1;
        }

        .hour-slot.empty::after {
            content: '点击分配任务';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: var(--gray-400);
            font-size: 12px;
            pointer-events: none;
        }

        .time-allocation-stats {
            font-size: 16px;
        }

        .time-allocation-stats .stat-value {
            font-size: 20px;
            font-weight: 700;
        }

        .task-form-container {
            background: var(--gray-100);
            border-radius: var(--border-radius);
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .schedule-container {
            max-height: 600px;
            overflow-y: auto;
        }

        .task-list-container {
            max-height: 400px;
            overflow-y: auto;
        }

        /* 日期选择器样式 */
        .date-selector {
            background: white;
            border-radius: var(--border-radius);
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: var(--shadow);
        }

        .current-date-display {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        /* 时间分类标题样式 */
        .time-category-title {
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 5px;
            margin-bottom: 10px;
        }

        .time-category-title h5 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .container {
                padding: 0 10px;
            }
            
            .card-body {
                padding: 1rem;
            }
            
            .schedule-container {
                max-height: 400px;
            }
            
            .task-list-container {
                max-height: 300px;
            }
        }
    </style>
</head>

<body>
    <div class="container mt-3" id="appContainer">
        <!-- 顶部导航按钮 -->
        <div class="row mb-3">
            <div class="col-12 d-flex justify-content-between align-items-center">
                <h2 class="mb-0 text-primary">
                    <i class="fas fa-clock me-2"></i>24小时时间分配
                </h2>
                <button class="btn btn-outline-primary" onclick="window.location.href='index.html'">
                    <i class="fas fa-home me-2"></i>返回主页
                </button>
            </div>
        </div>

        <!-- 日期选择器 -->
        <div class="date-selector">
            <div class="row align-items-center">
                <div class="col-12 text-center">
                    <div class="d-flex justify-content-center align-items-center">
                        <button id="prevDateBtn" class="btn btn-outline-primary btn-sm me-3">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <div id="currentDate" class="current-date-display">2024年4月15日</div>
                        <button id="nextDateBtn" class="btn btn-outline-primary btn-sm ms-3">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="row">
            <!-- 左侧：24小时时间表 -->
            <div class="col-lg-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-day me-2"></i>24小时时间表
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="schedule-container">
                            <div id="hourlySchedule">
                                <!-- 24小时时间表将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：任务管理 -->
            <div class="col-lg-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tasks me-2"></i>任务管理
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- 添加任务表单 -->
                        <div class="task-form-container">
                            <form id="addAllocationTaskForm">
                                <div class="row">
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" id="allocationTaskName" placeholder="任务名称" required>
                                    </div>
                                    <div class="col-md-4">
                                        <input type="number" class="form-control" id="allocationTaskDuration" placeholder="时长(小时)" min="0.5" max="24" step="0.5" required>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <select class="form-select" id="allocationTaskPriority">
                                            <option value="high">高优先级</option>
                                            <option value="medium" selected>中优先级</option>
                                            <option value="low">低优先级</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <button type="submit" class="btn btn-primary w-100">
                                            <i class="fas fa-plus"></i> 添加任务
                                        </button>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <input type="time" class="form-control" id="allocationTaskStartTime" placeholder="开始时间(可选)">
                                        <small class="text-muted">可选：设置具体开始时间</small>
                                    </div>
                                    <div class="col-md-6">
                                        <input type="time" class="form-control" id="allocationTaskEndTime" placeholder="结束时间(可选)">
                                        <small class="text-muted">可选：设置具体结束时间</small>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- 时间统计 -->
                        <div class="alert alert-info time-allocation-stats">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="stat-value" id="totalAllocatedTime">0h</div>
                                    <small>已分配</small>
                                </div>
                                <div class="col-4">
                                    <div class="stat-value" id="remainingTime">24h</div>
                                    <small>剩余</small>
                                </div>
                                <div class="col-4">
                                    <div class="stat-value" id="taskCount">0</div>
                                    <small>任务数</small>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <div id="allocationSuggestion" class="text-center small"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 任务列表 -->
                        <div class="task-list-container">
                            <div id="allocationTaskList">
                                <!-- 任务将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.min.js"></script>

    <script>
        // 全局变量
        let currentDate = new Date();
        let allocationTasks = [];
        let hourlySchedule = Array(24).fill(null).map((_, index) => ({
            hour: index,
            tasks: []
        }));

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
            bindEventListeners();
            loadAllocationData();
            renderAllocationTasks();
            renderHourlySchedule();
            updateAllocationStats();
        });

        // 初始化页面
        function initializePage() {
            updateDateDisplay();
        }

        // 绑定事件监听器
        function bindEventListeners() {
            // 日期导航
            document.getElementById('prevDateBtn').addEventListener('click', () => {
                currentDate.setDate(currentDate.getDate() - 1);
                updateDateDisplay();
                loadAllocationData();
                renderAllocationTasks();
                renderHourlySchedule();
                updateAllocationStats();
            });

            document.getElementById('nextDateBtn').addEventListener('click', () => {
                currentDate.setDate(currentDate.getDate() + 1);
                updateDateDisplay();
                loadAllocationData();
                renderAllocationTasks();
                renderHourlySchedule();
                updateAllocationStats();
            });

            // 添加任务表单
            document.getElementById('addAllocationTaskForm').addEventListener('submit', function(e) {
                e.preventDefault();
                addAllocationTask();
            });
        }

        // 更新日期显示
        function updateDateDisplay() {
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            };
            const dateStr = currentDate.toLocaleDateString('zh-CN', options);
            document.getElementById('currentDate').textContent = dateStr;
        }

        // 格式化日期键
        function formatDateKey(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        // 添加分配任务
        function addAllocationTask() {
            const taskName = document.getElementById('allocationTaskName').value.trim();
            const duration = parseFloat(document.getElementById('allocationTaskDuration').value);
            const priority = document.getElementById('allocationTaskPriority').value;
            const startTime = document.getElementById('allocationTaskStartTime').value;
            const endTime = document.getElementById('allocationTaskEndTime').value;

            if (!taskName || !duration) {
                alert('请填写任务名称和时长');
                return;
            }

            // 检查剩余时间
            const totalAllocated = allocationTasks.reduce((sum, task) => sum + task.duration, 0);
            if (totalAllocated + duration > 24) {
                alert('总时长不能超过24小时');
                return;
            }

            // 验证时间段
            let startHour = null;
            let endHour = null;
            if (startTime && endTime) {
                startHour = parseInt(startTime.split(':')[0]);
                endHour = parseInt(endTime.split(':')[0]);

                // 计算实际时长（支持跨日）
                let actualDuration;
                if (endHour > startHour) {
                    // 同一天内
                    actualDuration = endHour - startHour;
                } else {
                    // 跨日（如23:00-7:00）
                    actualDuration = (24 - startHour) + endHour;
                }

                if (actualDuration !== duration) {
                    alert(`时间段长度与设定时长不匹配。实际时长：${actualDuration}小时，设定时长：${duration}小时`);
                    return;
                }
            }

            const newTask = {
                id: Date.now().toString(),
                name: taskName,
                duration: duration,
                priority: priority,
                startHour: startHour,
                endHour: endHour,
                createdAt: new Date().toISOString()
            };

            allocationTasks.push(newTask);

            // 如果设置了具体时间，自动分配到时间表
            if (startHour !== null && endHour !== null) {
                autoAssignTaskToSchedule(newTask);
            }

            // 重置表单
            document.getElementById('addAllocationTaskForm').reset();

            // 更新界面
            renderAllocationTasks();
            renderHourlySchedule();
            updateAllocationStats();

            // 自动保存到主系统数据
            saveToMainSystem();
        }

        // 自动分配任务到时间表
        function autoAssignTaskToSchedule(task) {
            // 清除该任务在其他时间段的分配
            hourlySchedule.forEach(slot => {
                slot.tasks = slot.tasks.filter(t => t.id !== task.id);
            });

            // 分配到指定时间段（支持跨日）
            if (task.endHour > task.startHour) {
                // 同一天内
                for (let hour = task.startHour; hour < task.endHour; hour++) {
                    if (hour >= 0 && hour < 24) {
                        hourlySchedule[hour].tasks.push({
                            id: task.id,
                            name: task.name,
                            priority: task.priority
                        });
                    }
                }
            } else {
                // 跨日任务
                // 从开始时间到24:00
                for (let hour = task.startHour; hour < 24; hour++) {
                    hourlySchedule[hour].tasks.push({
                        id: task.id,
                        name: task.name,
                        priority: task.priority
                    });
                }
                // 从0:00到结束时间
                for (let hour = 0; hour < task.endHour; hour++) {
                    hourlySchedule[hour].tasks.push({
                        id: task.id,
                        name: task.name,
                        priority: task.priority
                    });
                }
            }
        }

        // 渲染分配任务列表
        function renderAllocationTasks() {
            const container = document.getElementById('allocationTaskList');
            if (!container) return;

            container.innerHTML = '';

            if (allocationTasks.length === 0) {
                container.innerHTML = '<div class="text-muted text-center py-3">暂无任务，请添加任务开始规划</div>';
                return;
            }

            allocationTasks.forEach(task => {
                const taskElement = document.createElement('div');
                taskElement.className = `allocation-task-item priority-${task.priority}`;
                taskElement.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1">
                            <div class="fw-bold">${task.name}</div>
                            <small class="text-muted">
                                ${task.duration}小时 | ${getPriorityText(task.priority)}
                                ${task.startHour !== null ? ` | ${task.startHour}:00-${task.endHour}:00` : ''}
                            </small>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-danger" onclick="removeAllocationTask('${task.id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
                container.appendChild(taskElement);
            });

            updateAllocationStats();
        }

        // 删除分配任务
        function removeAllocationTask(taskId) {
            allocationTasks = allocationTasks.filter(task => task.id !== taskId);

            // 从时间表中移除
            hourlySchedule.forEach(slot => {
                slot.tasks = slot.tasks.filter(t => t.id !== taskId);
            });

            renderAllocationTasks();
            renderHourlySchedule();
            updateAllocationStats();

            // 自动保存到主系统数据
            saveToMainSystem();
        }

        // 渲染24小时时间表
        function renderHourlySchedule() {
            const container = document.getElementById('hourlySchedule');
            if (!container) return;

            container.innerHTML = '';

            // 添加时间段分类标题
            const timeCategories = [
                { title: "早晨 (5:00-9:00)", start: 5, end: 9 },
                { title: "上午 (9:00-12:00)", start: 9, end: 12 },
                { title: "中午 (12:00-14:00)", start: 12, end: 14 },
                { title: "下午 (14:00-18:00)", start: 14, end: 18 },
                { title: "晚上 (18:00-22:00)", start: 18, end: 22 },
                { title: "夜间 (22:00-5:00)", start: 22, end: 29 } // 29表示第二天的5点
            ];

            // 创建时间段分类
            timeCategories.forEach(category => {
                // 创建分类标题
                const categoryTitle = document.createElement('div');
                categoryTitle.className = 'time-category-title mb-2 mt-3';
                categoryTitle.innerHTML = `<h5 class="text-primary"><i class="fas fa-clock me-2"></i>${category.title}</h5>`;
                container.appendChild(categoryTitle);

                // 创建该分类下的时间槽
                for (let hour = category.start; hour < category.end; hour++) {
                    const actualHour = hour % 24; // 处理跨日情况
                    const slot = hourlySchedule[actualHour];

                    // 创建时间槽元素
                    const slotElement = document.createElement('div');

                    // 设置基本类名
                    let className = 'hour-slot';

                    // 判断是否有任务
                    if (slot.tasks.length > 0) {
                        className += ' occupied';

                        // 获取第一个任务的优先级作为时间槽的优先级
                        if (slot.tasks[0].priority) {
                            className += ` priority-${slot.tasks[0].priority}`;
                        }
                    } else {
                        className += ' empty';
                    }

                    slotElement.className = className;

                    // 添加点击事件
                    slotElement.onclick = function() {
                        showAddTaskForHour(actualHour);
                    };

                    // 构建时间槽内容
                    let tasksHtml = '';
                    slot.tasks.forEach(task => {
                        tasksHtml += `<span class="task-in-slot priority-${task.priority}">${task.name}</span>`;
                    });

                    // 获取当前时间段的任务总数
                    const taskCount = slot.tasks.length;

                    // 构建时间信息
                    const timeInfo = taskCount > 0 ?
                        `<div class="time-info">${taskCount}个任务 | 点击添加更多</div>` :
                        '';

                    // 设置HTML内容
                    slotElement.innerHTML = `
                        <div>
                            <div class="hour-label">${actualHour}:00 - ${(actualHour + 1) % 24}:00</div>
                            <div class="tasks-in-hour">
                                ${tasksHtml}
                            </div>
                            ${timeInfo}
                        </div>
                    `;

                    container.appendChild(slotElement);
                }
            });
        }

        // 显示为特定小时添加任务的表单
        function showAddTaskForHour(hour) {
            // 设置开始时间
            document.getElementById('allocationTaskStartTime').value = `${hour.toString().padStart(2, '0')}:00`;

            // 设置结束时间（默认为1小时后）
            const endHour = (hour + 1) % 24;
            document.getElementById('allocationTaskEndTime').value = `${endHour.toString().padStart(2, '0')}:00`;

            // 设置默认时长为1小时
            document.getElementById('allocationTaskDuration').value = "1";

            // 聚焦到任务名称输入框
            document.getElementById('allocationTaskName').focus();

            // 滚动到表单位置
            document.getElementById('addAllocationTaskForm').scrollIntoView({ behavior: 'smooth' });
        }

        // 更新分配统计
        function updateAllocationStats() {
            const totalAllocated = allocationTasks.reduce((sum, task) => sum + task.duration, 0);
            const remaining = 24 - totalAllocated;
            const taskCount = allocationTasks.length;

            document.getElementById('totalAllocatedTime').textContent = totalAllocated.toFixed(1) + 'h';
            document.getElementById('remainingTime').textContent = remaining.toFixed(1) + 'h';
            document.getElementById('taskCount').textContent = taskCount;

            // 生成智能建议
            const suggestionElement = document.getElementById('allocationSuggestion');
            let suggestion = '';

            if (totalAllocated === 0) {
                suggestion = '💡 开始添加任务来规划您的一天吧！';
            } else if (remaining > 8) {
                suggestion = '⏰ 还有较多空闲时间，可以添加更多任务或休息时间';
            } else if (remaining > 2) {
                suggestion = '✅ 时间分配合理，记得留出休息和缓冲时间';
            } else if (remaining > 0) {
                suggestion = '⚠️ 时间安排较紧，建议预留一些缓冲时间';
            } else if (remaining === 0) {
                suggestion = '🎯 完美！24小时全部分配完成';
            } else {
                suggestion = '❌ 时间超出24小时，请调整任务时长';
            }

            // 添加任务类型分析
            const workTasks = allocationTasks.filter(task =>
                task.name.includes('工作') || task.name.includes('学习') || task.name.includes('项目')
            );
            const restTasks = allocationTasks.filter(task =>
                task.name.includes('休息') || task.name.includes('睡觉') || task.name.includes('放松')
            );

            if (workTasks.length > 0 && restTasks.length === 0) {
                suggestion += ' | 💤 建议添加休息时间';
            } else if (restTasks.length > 0 && workTasks.length === 0) {
                suggestion += ' | 💪 可以添加一些工作或学习任务';
            }

            suggestionElement.textContent = suggestion;
        }

        // 获取优先级文本
        function getPriorityText(priority) {
            switch (priority) {
                case 'high': return '高优先级';
                case 'medium': return '中优先级';
                case 'low': return '低优先级';
                default: return '未知';
            }
        }

        // 保存到主系统数据
        function saveToMainSystem() {
            const currentDateStr = formatDateKey(currentDate);

            // 获取主系统的时间线数据
            let timelineData = {};
            try {
                const savedData = localStorage.getItem('timelineData');
                if (savedData) {
                    timelineData = JSON.parse(savedData);
                }
            } catch (error) {
                console.error('读取主系统数据失败:', error);
            }

            // 确保当前日期的数据结构存在
            if (!timelineData[currentDateStr]) {
                timelineData[currentDateStr] = {};
            }

            // 清除之前的24小时分配数据（以allocation_开头的时间段）
            Object.keys(timelineData[currentDateStr]).forEach(key => {
                if (key.startsWith('allocation_')) {
                    delete timelineData[currentDateStr][key];
                }
            });

            // 添加新的24小时分配数据
            allocationTasks.forEach(task => {
                if (task.startHour !== null && task.endHour !== null) {
                    const timeSlotId = `allocation_${task.startHour}_${task.endHour}_${task.id}`;

                    if (task.endHour > task.startHour) {
                        // 同一天内的任务
                        timelineData[currentDateStr][timeSlotId] = {
                            startTime: `${task.startHour}:00`,
                            endTime: `${task.endHour}:00`,
                            tasks: [{
                                id: task.id,
                                content: task.name,
                                priority: task.priority,
                                completed: false,
                                createdAt: task.createdAt,
                                source: 'allocation' // 标记来源为24小时分配
                            }]
                        };
                    } else {
                        // 跨日任务，分成两个时间段
                        // 第一段：从开始时间到24:00
                        const timeSlotId1 = `allocation_${task.startHour}_24_${task.id}_part1`;
                        timelineData[currentDateStr][timeSlotId1] = {
                            startTime: `${task.startHour}:00`,
                            endTime: `24:00`,
                            tasks: [{
                                id: task.id + '_part1',
                                content: task.name + ' (第1部分)',
                                priority: task.priority,
                                completed: false,
                                createdAt: task.createdAt,
                                source: 'allocation'
                            }]
                        };

                        // 第二段：从0:00到结束时间
                        const timeSlotId2 = `allocation_0_${task.endHour}_${task.id}_part2`;
                        timelineData[currentDateStr][timeSlotId2] = {
                            startTime: `0:00`,
                            endTime: `${task.endHour}:00`,
                            tasks: [{
                                id: task.id + '_part2',
                                content: task.name + ' (第2部分)',
                                priority: task.priority,
                                completed: false,
                                createdAt: task.createdAt,
                                source: 'allocation'
                            }]
                        };
                    }
                }
            });

            // 保存到localStorage
            try {
                localStorage.setItem('timelineData', JSON.stringify(timelineData));
                console.log('24小时分配数据已同步到主系统');
            } catch (error) {
                console.error('保存到主系统失败:', error);
            }
        }

        // 加载分配数据
        function loadAllocationData() {
            const currentDateStr = formatDateKey(currentDate);

            // 从主系统数据中加载24小时分配数据
            try {
                const timelineData = JSON.parse(localStorage.getItem('timelineData') || '{}');
                const dayData = timelineData[currentDateStr] || {};

                // 重置数据
                allocationTasks = [];
                hourlySchedule = Array(24).fill(null).map((_, index) => ({
                    hour: index,
                    tasks: []
                }));

                // 从主系统数据中提取24小时分配的任务
                Object.keys(dayData).forEach(timeSlotId => {
                    if (timeSlotId.startsWith('allocation_')) {
                        const timeSlot = dayData[timeSlotId];
                        timeSlot.tasks.forEach(task => {
                            if (task.source === 'allocation' && !task.id.includes('_part')) {
                                // 只处理原始任务，不处理分割的部分
                                const existingTask = allocationTasks.find(t => t.id === task.id);
                                if (!existingTask) {
                                    // 从时间段ID中解析开始和结束时间
                                    const parts = timeSlotId.split('_');
                                    const startHour = parseInt(parts[1]);
                                    const endHour = parseInt(parts[2]);

                                    const allocationTask = {
                                        id: task.id,
                                        name: task.content,
                                        duration: endHour > startHour ? endHour - startHour : (24 - startHour) + endHour,
                                        priority: task.priority,
                                        startHour: startHour,
                                        endHour: endHour,
                                        createdAt: task.createdAt
                                    };

                                    allocationTasks.push(allocationTask);
                                    autoAssignTaskToSchedule(allocationTask);
                                }
                            }
                        });
                    }
                });

            } catch (error) {
                console.error('加载24小时分配数据失败:', error);
                // 重置为空数据
                allocationTasks = [];
                hourlySchedule = Array(24).fill(null).map((_, index) => ({
                    hour: index,
                    tasks: []
                }));
            }
        }

        // 将函数暴露到全局作用域
        window.removeAllocationTask = removeAllocationTask;
    </script>
</body>
</html>
