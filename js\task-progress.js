// 任务进度系统 JS

// 数据结构
let taskBoards = []; // 任务表数组
let tasks = []; // 任务数组
let selectedBoardId = null; // 当前选中的任务表ID
let taskFoldStates = {}; // 任务折叠状态：{ taskId: boolean } - true表示展开，false表示折叠
let boardFoldStates = {}; // 任务板折叠状态：{ boardId: boolean } - true表示展开，false表示折叠
let shouldAutoScroll = false; // 是否应该自动滚动到选中的任务表

// 生成唯一ID的函数
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random() * 16 | 0,
            v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 从本地存储加载数据
    loadData();
    
    // 初始化UI
    initUI();
    
    // 事件监听器
    setupEventListeners();
    
    // 调试信息
    debugTasks();
});

// 添加调试函数用于显示任务结构
function debugTasks() {
    console.group('===== 任务系统调试信息 =====');
    console.log('任务表数量:', taskBoards.length);
    console.log('任务总数:', tasks.length);
    console.log('当前选中的任务表ID:', selectedBoardId);
    
    // 按任务表分组显示任务
    taskBoards.forEach(board => {
        const boardTasks = tasks.filter(task => task.boardId === board.id);
        const topLevelTasks = boardTasks.filter(task => !task.parentId);
        
        console.group(`任务表: ${board.name} (ID: ${board.id}) - ${boardTasks.length}个任务`);
        console.log(`顶级任务: ${topLevelTasks.length}个`);
        
        // 显示任务层级结构
        console.group('任务层级结构:');
        topLevelTasks.forEach(task => {
            logTaskWithChildren(task, 0);
        });
        console.groupEnd();
        
        console.groupEnd();
    });
    
    console.groupEnd();
}

// 递归记录任务及其子任务
function logTaskWithChildren(task, level) {
    const indent = '  '.repeat(level);
    console.log(`${indent}${task.completed ? '✓' : '○'} ${task.name} (ID: ${task.id}, 层级: ${task.level})`);
    
    // 查找并记录子任务
    const children = tasks.filter(t => t.parentId === task.id);
    if (children.length > 0) {
        children.forEach(child => {
            logTaskWithChildren(child, level + 1);
        });
    }
}

// 加载数据
function loadData() {
    console.log('开始加载任务数据');
    
    // 从独立存储加载数据
    const savedTaskBoards = localStorage.getItem('taskBoards');
    const savedTasks = localStorage.getItem('tasks');
    const savedFoldStates = localStorage.getItem('taskFoldStates');
    const savedBoardFoldStates = localStorage.getItem('boardFoldStates');
    let dataLoaded = false;
    
    if (savedTaskBoards && savedTasks) {
        try {
            // 从本地存储加载任务数据
            taskBoards = JSON.parse(savedTaskBoards);
            tasks = JSON.parse(savedTasks);
            dataLoaded = true;
            console.log('从独立存储加载任务数据成功', {
                '任务表数量': taskBoards.length,
                '任务数量': tasks.length
            });
            
            // 检查任务数据完整性
            console.group('任务数据完整性检查:');
            const topLevelTasks = tasks.filter(task => !task.parentId);
            console.log(`顶级任务数量: ${topLevelTasks.length}`);
            
            // 检查并修复无效的父任务引用
            const tasksWithInvalidParent = tasks.filter(task => 
                task.parentId && !tasks.find(t => t.id === task.parentId)
            );
            if (tasksWithInvalidParent.length > 0) {
                console.warn(`发现${tasksWithInvalidParent.length}个任务的父任务ID无效，正在修复...`, tasksWithInvalidParent);
                
                // 将无效父任务ID的任务转为顶级任务
                tasksWithInvalidParent.forEach(task => {
                    const taskIndex = tasks.findIndex(t => t.id === task.id);
                    if (taskIndex !== -1) {
                        tasks[taskIndex].parentId = null;
                        tasks[taskIndex].level = 0;
                        console.log(`已将任务 "${tasks[taskIndex].name}" 转为顶级任务`);
                    }
                });
            }
            
            // 检查并修复任务表关联
            const tasksWithInvalidBoard = tasks.filter(task => 
                !taskBoards.find(board => board.id === task.boardId)
            );
            if (tasksWithInvalidBoard.length > 0) {
                console.warn(`发现${tasksWithInvalidBoard.length}个任务的任务表ID无效，正在修复...`, tasksWithInvalidBoard);
                
                // 如果有任务表，将无效任务表ID的任务转移到第一个任务表
                if (taskBoards.length > 0) {
                    const firstBoardId = taskBoards[0].id;
                    tasksWithInvalidBoard.forEach(task => {
                        const taskIndex = tasks.findIndex(t => t.id === task.id);
                        if (taskIndex !== -1) {
                            tasks[taskIndex].boardId = firstBoardId;
                            console.log(`已将任务 "${tasks[taskIndex].name}" 移动到任务表 "${taskBoards[0].name}"`);
                        }
                    });
                } else {
                    // 如果没有任务表，创建一个默认任务表
                    console.log('没有有效的任务表，创建默认任务表');
                    initDefaultTaskBoard();
                    
                    // 将所有任务移动到新的默认任务表
                    const defaultBoardId = taskBoards[0].id;
                    tasks.forEach(task => {
                        task.boardId = defaultBoardId;
                    });
                    console.log(`已将所有任务移动到默认任务表`);
                }
            }
            
            // 检查任务嵌套级别
            const tasksWithInvalidLevel = tasks.filter(task => {
                if (!task.parentId) return task.level !== 0;
                
                const parentTask = tasks.find(t => t.id === task.parentId);
                return parentTask && task.level !== parentTask.level + 1;
            });
            
            if (tasksWithInvalidLevel.length > 0) {
                console.warn(`发现${tasksWithInvalidLevel.length}个任务的嵌套级别不正确，正在修复...`, tasksWithInvalidLevel);
                
                // 修复嵌套级别
                tasksWithInvalidLevel.forEach(task => {
                    const taskIndex = tasks.findIndex(t => t.id === task.id);
                    if (taskIndex !== -1) {
                        if (!task.parentId) {
                            tasks[taskIndex].level = 0;
                        } else {
                            const parentTask = tasks.find(t => t.id === task.parentId);
                            if (parentTask) {
                                tasks[taskIndex].level = parentTask.level + 1;
                            }
                        }
                        console.log(`已修复任务 "${tasks[taskIndex].name}" 的嵌套级别为 ${tasks[taskIndex].level}`);
                    }
                });
            }
            
            // 如果进行了任何修复，保存更新后的数据
            if (tasksWithInvalidParent.length > 0 || tasksWithInvalidBoard.length > 0 || tasksWithInvalidLevel.length > 0) {
                console.log('数据修复完成，保存更新后的数据');
                saveData();
            }
            
            console.groupEnd();
        } catch (e) {
            console.error('解析独立存储数据失败', e);
            // 初始化默认数据
            taskBoards = [];
            tasks = [];
            initDefaultTaskBoard();
        }
    }
    
    // 加载子任务折叠状态
    if (savedFoldStates) {
        try {
            taskFoldStates = JSON.parse(savedFoldStates);
            console.log('从独立存储加载子任务折叠状态成功');
            
            // 临时解决方案: 设置所有任务为展开状态
            tasks.forEach(task => {
                taskFoldStates[task.id] = true;
            });
            console.log('已重置所有任务的折叠状态为展开');
            
            // 保存更新后的折叠状态
            saveTaskFoldStates();
        } catch (e) {
            console.error('解析子任务折叠状态数据失败', e);
            taskFoldStates = {};
        }
    } else {
        taskFoldStates = {};
        // 确保所有任务都有展开状态
        tasks.forEach(task => {
            taskFoldStates[task.id] = true;
        });
    }
    
    // 加载任务板折叠状态
    if (savedBoardFoldStates) {
        try {
            boardFoldStates = JSON.parse(savedBoardFoldStates);
            console.log('从独立存储加载任务板折叠状态成功');
            
            // 确保所有任务板默认展开
            taskBoards.forEach(board => {
                boardFoldStates[board.id] = true;
            });
            console.log('已设置所有任务板为展开状态');
            
            // 保存更新后的状态
            saveBoardFoldStates();
        } catch (e) {
            console.error('解析任务板折叠状态数据失败', e);
            boardFoldStates = {};
        }
    } else {
        boardFoldStates = {};
        // 确保所有任务板都有展开状态
        taskBoards.forEach(board => {
            boardFoldStates[board.id] = true;
        });
    }
    
    // 如果没有加载到任何数据，初始化默认任务表
    if (!dataLoaded || taskBoards.length === 0) {
        console.log('没有找到现有数据，初始化默认任务表');
        initDefaultTaskBoard();
    }
    
    // 设置默认选中的任务表
    if (taskBoards.length > 0 && !selectedBoardId) {
        selectedBoardId = taskBoards[0].id;
    }
}

// 初始化默认任务表
function initDefaultTaskBoard() {
    const defaultBoard = {
        id: generateUUID(),
        name: '默认任务表',
        createdAt: new Date().toISOString()
    };
    taskBoards.push(defaultBoard);
    
    // 设置默认任务表的初始折叠状态为展开
    boardFoldStates[defaultBoard.id] = true;
    
    saveData();
    saveBoardFoldStates();
}

// 错误处理和日志函数
function logError(message, details) {
    console.error(`[错误] ${message}`, details);
    // 可以在这里添加其他错误处理逻辑，如发送错误报告等
}

// 保存数据
function saveData() {
    console.log('保存任务数据');
    
    // 数据完整性验证 - 确保不会保存空数据
    if (!Array.isArray(tasks) || tasks.length === 0) {
        logError('尝试保存空任务数组，操作已中止', { tasksLength: tasks ? tasks.length : 'undefined' });
        return; // 阻止保存空数据
    }
    
    if (!Array.isArray(taskBoards) || taskBoards.length === 0) {
        logError('尝试保存空任务表数组，操作已中止', { boardsLength: taskBoards ? taskBoards.length : 'undefined' });
        return; // 阻止保存空数据
    }
    
    // 保存到独立存储
    try {
        localStorage.setItem('taskBoards', JSON.stringify(taskBoards));
        localStorage.setItem('tasks', JSON.stringify(tasks));
        console.log('任务数据已保存到独立存储，任务数量:', tasks.length);
    } catch (e) {
        logError('保存到独立存储失败', e);
    }
}

// 保存任务折叠状态
function saveTaskFoldStates() {
    console.log('保存任务折叠状态');
    
    try {
        localStorage.setItem('taskFoldStates', JSON.stringify(taskFoldStates));
        console.log('任务折叠状态已保存到独立存储');
    } catch (e) {
        console.error('保存折叠状态失败', e);
    }
}

// 保存任务板折叠状态
function saveBoardFoldStates() {
    console.log('保存任务板折叠状态');
    
    try {
        localStorage.setItem('boardFoldStates', JSON.stringify(boardFoldStates));
        console.log('任务板折叠状态已保存到独立存储');
    } catch (e) {
        console.error('保存任务板折叠状态失败', e);
    }
}

// 初始化UI
function initUI() {
    // 渲染任务表列表
    renderTaskBoardsList();
    
    // 渲染任务表选择器
    renderTaskBoardSelectors();
    
    // 渲染任务表内容
    renderTaskBoards();
}

// 格式化日期为文件名友好格式
function formatDateForFilename(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    
    return `${year}${month}${day}_${hours}${minutes}`;
}

// 设置事件监听器
function setupEventListeners() {
    // 添加任务表表单提交
    document.getElementById('taskBoardForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const boardNameInput = document.getElementById('boardName');
        const boardName = boardNameInput.value.trim();

        if (boardName) {
            addTaskBoard(boardName);
            boardNameInput.value = '';
        }
    });

    // 批量模式切换事件
    document.getElementById('batchModeToggle').addEventListener('click', function() {
        const normalForm = document.querySelector('.normal-add-form');
        const batchContainer = document.getElementById('batchAddContainer');
        const isCurrentlyBatch = batchContainer.style.display !== 'none';

        if (isCurrentlyBatch) {
            // 切换回普通模式
            normalForm.style.display = 'block';
            batchContainer.style.display = 'none';
            this.innerHTML = '<i class="fas fa-list me-1"></i>批量模式';
            this.className = 'btn btn-outline-info btn-sm';
        } else {
            // 切换到批量模式
            normalForm.style.display = 'none';
            batchContainer.style.display = 'block';
            this.innerHTML = '<i class="fas fa-times me-1"></i>关闭批量';
            this.className = 'btn btn-outline-secondary btn-sm';
        }
    });

    // 批量添加任务按钮事件
    document.getElementById('batchAddBtn').addEventListener('click', function() {
        console.log('捕获到批量添加任务按钮点击事件');

        const batchTaskNamesInput = document.getElementById('batchTaskNames');
        const batchTaskBoardSelect = document.getElementById('batchTaskBoardSelect');

        const taskNamesText = batchTaskNamesInput.value.trim();
        const boardId = batchTaskBoardSelect.value;

        if (taskNamesText && boardId) {
            console.log('准备批量添加任务:', { taskNamesText, boardId });
            batchAddTasks(taskNamesText, boardId);
            batchTaskNamesInput.value = '';
            console.log('批量任务添加完成，表单已重置');
        } else {
            console.error('批量添加任务失败：缺少任务名称或任务表ID');
            if (!taskNamesText) alert('请输入任务名称');
            if (!boardId) alert('请选择任务表');
        }
    });

    // 添加任务表单提交
    document.getElementById('addTaskForm').addEventListener('submit', function(e) {
        e.preventDefault();
        console.log('捕获到添加任务表单提交事件');
        
        const taskNameInput = document.getElementById('taskName');
        const taskDeadlineInput = document.getElementById('taskDeadline');
        const taskBoardSelect = document.getElementById('taskBoardSelect');
        
        console.log('表单输入值:', {
            taskName: taskNameInput.value,
            taskDeadline: taskDeadlineInput.value,
            boardId: taskBoardSelect.value
        });
        
        const taskName = taskNameInput.value.trim();
        let taskDeadline = taskDeadlineInput.value.trim();
        const boardId = taskBoardSelect.value;
        
        // 处理MMDD格式的日期，添加2025年
        if (taskDeadline && /^\d{4}$/.test(taskDeadline)) {
            const month = taskDeadline.substring(0, 2);
            const day = taskDeadline.substring(2, 4);
            taskDeadline = `2025-${month}-${day}`;
            console.log('处理后的截止日期:', taskDeadline);
        }
        
        if (taskName && boardId) {
            console.log('准备添加任务:', { taskName, taskDeadline, boardId });
            // 直接添加顶级任务，没有父任务
            addTask(taskName, taskDeadline, null, boardId);
            taskNameInput.value = '';
            taskDeadlineInput.value = '';
            console.log('任务添加完成，表单已重置');
        } else {
            console.error('添加任务失败：缺少任务名称或任务表ID');
            if (!taskName) alert('请输入任务名称');
            if (!boardId) alert('请选择任务表');
        }
    });
    
    // 编辑任务模态框关闭事件 - 确保关闭模态框时不会触发不必要的操作
    document.getElementById('editTaskModal').addEventListener('hidden.bs.modal', function() {
        console.log('编辑任务模态框已关闭，当前任务数量:', tasks.length);
        // 模态框关闭时不执行任何可能改变任务数据的操作
    });
    
    // 保存任务编辑
    document.getElementById('saveTaskEdit').addEventListener('click', function() {
        const taskId = document.getElementById('editTaskId').value;
        const taskName = document.getElementById('editTaskName').value.trim();
        let taskDeadline = document.getElementById('editTaskDeadline').value.trim();
        const boardId = document.getElementById('editTaskBoard').value;
        const parentTaskId = document.getElementById('editParentTask').value;
        
        console.log('编辑任务 - 点击保存:', { taskId, taskName, taskDeadline, boardId, parentTaskId });
        
        // 验证任务ID是否存在
        const taskExists = tasks.some(task => task.id === taskId);
        if (!taskExists) {
            console.error('编辑任务失败: 任务ID不存在', taskId);
            alert('编辑任务失败: 无法找到该任务');
            return;
        }
        
        // 处理MMDD格式的日期，添加2025年
        if (taskDeadline && /^\d{4}$/.test(taskDeadline)) {
            const month = taskDeadline.substring(0, 2);
            const day = taskDeadline.substring(2, 4);
            taskDeadline = `2025-${month}-${day}`;
        }
        
        if (taskName && boardId) {
            // 获取当前任务的完成状态
            const taskIndex = tasks.findIndex(task => task.id === taskId);
            if (taskIndex === -1) {
                console.error('编辑任务失败: 无法找到索引', taskId);
                return;
            }
            
            const currentTask = tasks[taskIndex];
            const completed = currentTask.completed;
            
            console.log('编辑任务 - 更新前的任务数据:', JSON.parse(JSON.stringify(currentTask)));
            
            // 检查父任务循环引用
            if (parentTaskId && isDescendant(parentTaskId, taskId)) {
                alert('不能选择子任务作为父任务！');
                return;
            }
            
            // 计算新的嵌套级别
            let newLevel = 0;
            if (parentTaskId) {
                const parentTask = tasks.find(task => task.id === parentTaskId);
                if (parentTask) {
                    newLevel = parentTask.level + 1;
                    // 限制最多四层嵌套
                    if (newLevel > 3) {
                        alert('最多支持四层子任务嵌套！');
                        return;
                    }
                }
            }
            
            // 直接更新任务对象，而不是调用 updateTask
            currentTask.name = taskName;
            currentTask.deadline = taskDeadline || null;
            currentTask.parentId = parentTaskId || null;
            currentTask.boardId = boardId;
            currentTask.level = newLevel;
            
            // 如果父任务发生变化，需要更新所有子任务的级别
            const oldParentId = currentTask.parentId;
            const oldLevel = currentTask.level;
            if (oldParentId !== parentTaskId || oldLevel !== newLevel) {
                updateChildrenLevels(taskId, newLevel);
            }
            
            console.log('编辑任务 - 更新后的任务数据:', JSON.parse(JSON.stringify(currentTask)));
            
            saveData();
            
            // 更新UI
            renderTaskBoards();
            renderParentTaskSelectors();
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('editTaskModal'));
            if (modal) {
                modal.hide();
            }
            
            console.log('编辑任务完成 - 当前任务列表长度:', tasks.length);
        } else {
            console.error('编辑任务失败: 任务名称或任务表ID无效', { taskName, boardId });
            alert('请输入任务名称并选择任务表');
        }
    });
    
    // 清理已完成任务
    document.getElementById('clearCompletedTasks').addEventListener('click', clearCompletedTasks);
    
    // 显示/隐藏已完成任务
    document.getElementById('showCompletedTasks').addEventListener('change', function() {
        renderTaskBoards();
    });
    
    // 按截止日期排序
    document.getElementById('sortByDeadline').addEventListener('change', function() {
        renderTaskBoards();
    });
    
    // 添加重置任务系统数据按钮
    const resetButton = document.getElementById('resetTaskSystem');
    if (resetButton) {
        resetButton.addEventListener('click', function() {
            if (confirm('确定要重置任务系统数据吗？这将清除所有任务和任务表，并重置系统状态。')) {
                resetTaskSystem();
            }
        });
    }
}

// 重置任务系统数据
function resetTaskSystem() {
    console.log('重置任务系统数据...');
    
    // 清空任务和任务表数组
    tasks = [];
    taskBoards = [];
    
    // 清空折叠状态
    taskFoldStates = {};
    boardFoldStates = {};
    
    // 清除本地存储
    localStorage.removeItem('tasks');
    localStorage.removeItem('taskBoards');
    localStorage.removeItem('taskFoldStates');
    localStorage.removeItem('boardFoldStates');
    
    // 创建默认任务表
    initDefaultTaskBoard();
    
    // 设置默认选中的任务表
    if (taskBoards.length > 0) {
        selectedBoardId = taskBoards[0].id;
    }
    
    // 重新渲染界面
    renderTaskBoardsList();
    renderTaskBoardSelectors();
    renderTaskBoards();
    
    console.log('任务系统数据已重置');
    
    // 显示成功提示
    alert('任务系统数据已重置，创建了默认任务表。');
}

// 添加任务表
function addTaskBoard(name) {
    const newBoard = {
        id: generateUUID(),
        name: name,
        createdAt: new Date().toISOString()
    };
    
    taskBoards.push(newBoard);
    // 注释掉以下行，不再自动切换到新创建的任务表
    // selectedBoardId = newBoard.id;
    
    // 设置自动滚动标志为 false，防止添加任务表后自动滚动
    shouldAutoScroll = false;
    
    // 设置新任务板的初始折叠状态为展开
    boardFoldStates[newBoard.id] = true;
    
    saveData();
    saveBoardFoldStates();
    
    // 更新UI
    renderTaskBoardsList();
    renderTaskBoardSelectors();
    renderTaskBoards();
}

// 删除任务表
function deleteTaskBoard(boardId) {
    if (confirm('确定要删除这个任务表吗？这将删除该表中的所有任务。')) {
        // 获取要删除的任务列表，用于清理任务折叠状态
        const boardTasks = tasks.filter(task => task.boardId === boardId);
        const taskIds = boardTasks.map(task => task.id);
        
        // 删除该任务表中的所有任务
        tasks = tasks.filter(task => task.boardId !== boardId);
        
        // 删除任务表
        taskBoards = taskBoards.filter(board => board.id !== boardId);
        
        // 如果删除的是当前选中的任务表，则选择第一个任务表
        if (selectedBoardId === boardId && taskBoards.length > 0) {
            selectedBoardId = taskBoards[0].id;
        } else if (taskBoards.length === 0) {
            selectedBoardId = null;
        }
        
        // 清理折叠状态
        delete boardFoldStates[boardId];
        taskIds.forEach(taskId => {
            delete taskFoldStates[taskId];
        });
        
        saveData();
        saveTaskFoldStates();
        saveBoardFoldStates();
        
        // 更新UI
        renderTaskBoardsList();
        renderTaskBoardSelectors();
        renderTaskBoards();
    }
}

// 移动任务表（上移或下移）
function moveTaskBoard(boardId, direction) {
    console.log(`移动任务表 ${boardId} ${direction === 'up' ? '上移' : '下移'}`);
    
    // 找到任务表的索引
    const boardIndex = taskBoards.findIndex(board => board.id === boardId);
    if (boardIndex === -1) {
        console.error(`找不到ID为 ${boardId} 的任务表`);
        return;
    }
    
    // 根据方向确定目标索引
    let targetIndex;
    if (direction === 'up') {
        // 如果已经是第一个，则不能上移
        if (boardIndex === 0) {
            console.log('已经是第一个任务表，无法上移');
            return;
        }
        targetIndex = boardIndex - 1;
    } else if (direction === 'down') {
        // 如果已经是最后一个，则不能下移
        if (boardIndex === taskBoards.length - 1) {
            console.log('已经是最后一个任务表，无法下移');
            return;
        }
        targetIndex = boardIndex + 1;
    } else {
        console.error(`不支持的移动方向: ${direction}`);
        return;
    }
    
    // 交换任务表位置
    const temp = taskBoards[boardIndex];
    taskBoards[boardIndex] = taskBoards[targetIndex];
    taskBoards[targetIndex] = temp;
    
    // 保存更新后的任务表顺序
    saveData();
    
    // 更新UI
    renderTaskBoardsList();
    renderTaskBoards();
}

// 批量添加任务
function batchAddTasks(taskNamesText, boardId) {
    const lines = taskNamesText.split('\n');
    let addedCount = 0;

    lines.forEach(line => {
        const taskName = line.trim();
        if (taskName) { // 忽略空行
            addTask(taskName, null, null, boardId);
            addedCount++;
        }
    });

    console.log(`批量添加了 ${addedCount} 个任务`);
    if (addedCount > 0) {
        alert(`成功添加了 ${addedCount} 个任务`);
    }
}

// 添加任务
function addTask(name, deadline, parentId, boardId) {
    const newTask = {
        id: generateUUID(),
        name: name,
        completed: false,
        createdAt: new Date().toISOString(),
        deadline: deadline || null,
        parentId: parentId || null,
        boardId: boardId,
        level: 0
    };
    
    // 计算嵌套级别
    if (parentId) {
        const parentTask = tasks.find(task => task.id === parentId);
        if (parentTask) {
            newTask.level = parentTask.level + 1;
            // 限制最多四层嵌套
            if (newTask.level > 3) {
                alert('最多支持四层子任务嵌套！');
                return;
            }
            
            // 当添加子任务时，将父任务的折叠状态设置为展开
            taskFoldStates[parentId] = true;
            saveTaskFoldStates();
        }
    }
    
    tasks.push(newTask);
    
    // 设置自动滚动标志为 false，防止添加任务后自动滚动
    shouldAutoScroll = false;
    
    saveData();
    
    // 更新UI
    renderTaskBoards();
}

// 更新任务
function updateTask(taskId, name, deadline, parentId, boardId, completed) {
    const taskIndex = tasks.findIndex(task => task.id === taskId);
    if (taskIndex !== -1) {
        const oldParentId = tasks[taskIndex].parentId;
        const oldLevel = tasks[taskIndex].level;
        
        // 检查父任务是否会造成循环嵌套
        if (parentId && isDescendant(parentId, taskId)) {
            alert('不能选择子任务作为父任务！');
            return;
        }
        
        // 计算新的嵌套级别
        let newLevel = 0;
        if (parentId) {
            const parentTask = tasks.find(task => task.id === parentId);
            if (parentTask) {
                newLevel = parentTask.level + 1;
                // 限制最多四层嵌套
                if (newLevel > 3) {
                    alert('最多支持四层子任务嵌套！');
                    return;
                }
            }
        }
        
        // 更新任务
        tasks[taskIndex].name = name;
        tasks[taskIndex].deadline = deadline || null;
        tasks[taskIndex].parentId = parentId || null;
        tasks[taskIndex].boardId = boardId;
        tasks[taskIndex].level = newLevel;
        
        // 如果提供了完成状态，则更新；否则保留现有状态
        if (completed !== undefined) {
            tasks[taskIndex].completed = completed;
        }
        
        // 如果父任务发生变化，需要更新所有子任务的级别
        if (oldParentId !== parentId || oldLevel !== newLevel) {
            updateChildrenLevels(taskId, newLevel);
        }
        
        saveData();
        
        // 更新UI
        renderTaskBoards();
        renderParentTaskSelectors();
    }
}

// 更新子任务的级别
function updateChildrenLevels(parentId, parentLevel) {
    const children = tasks.filter(task => task.parentId === parentId);
    
    children.forEach(child => {
        const childIndex = tasks.findIndex(task => task.id === child.id);
        const newLevel = parentLevel + 1;
        
        // 如果级别超过上限，将该任务移动到顶层
        if (newLevel > 3) {
            tasks[childIndex].parentId = null;
            tasks[childIndex].level = 0;
        } else {
            tasks[childIndex].level = newLevel;
            // 递归更新子任务的子任务
            updateChildrenLevels(child.id, newLevel);
        }
    });
}

// 检查是否是某个任务的后代（避免循环嵌套）
function isDescendant(taskId, potentialAncestorId) {
    let currentId = taskId;
    
    while (currentId) {
        if (currentId === potentialAncestorId) {
            return true;
        }
        
        const task = tasks.find(task => task.id === currentId);
        currentId = task ? task.parentId : null;
    }
    
    return false;
}

// 将子任务标记为完成/未完成，并返回涉及的任务表ID集合
function markChildrenAsCompleted(parentId, completed, affectedBoardIds = new Set()) {
    const children = tasks.filter(task => task.parentId === parentId);
    
    children.forEach(child => {
        const childIndex = tasks.findIndex(task => task.id === child.id);
        tasks[childIndex].completed = completed;
        
        // 记录涉及的任务表ID
        affectedBoardIds.add(child.boardId);
        
        // 递归处理子任务的子任务
        markChildrenAsCompleted(child.id, completed, affectedBoardIds);
    });
    
    return affectedBoardIds;
}

// 切换任务完成状态
function toggleTaskCompletion(taskId) {
    const taskIndex = tasks.findIndex(task => task.id === taskId);
    if (taskIndex !== -1) {
        const task = tasks[taskIndex];
        const boardId = task.boardId;
        task.completed = !task.completed;
        
        // 更新任务UI
        updateTaskCompletionUI(taskId, task.completed);
        
        // 如果标记为完成，则子任务也标记为完成
        if (task.completed) {
            // 收集所有涉及的任务表ID
            const affectedBoardIds = markChildrenAsCompleted(taskId, true, new Set([boardId]));
            updateChildTasksCompletionUI(taskId, true);
            
            // 更新所有涉及的任务表进度
            affectedBoardIds.forEach(id => updateBoardProgress(id));
        } else {
            // 如果是取消完成状态，只需更新当前任务表的进度
            updateBoardProgress(boardId);
        }
        
        // 检查父任务状态
        updateParentTaskStatus(task.parentId);
        
        saveData();
        // 移除对renderTaskBoards()的调用，避免重绘整个界面
    }
}

// 更新任务完成状态的UI
function updateTaskCompletionUI(taskId, completed) {
    const taskItem = document.querySelector(`.task-item[data-id="${taskId}"]`);
    if (taskItem) {
        if (completed) {
            taskItem.classList.add('task-completed');
        } else {
            taskItem.classList.remove('task-completed');
        }
        
        // 更新复选框状态
        const checkbox = taskItem.querySelector('.task-checkbox');
        if (checkbox) {
            checkbox.checked = completed;
        }
    }
}

// 递归更新子任务的完成状态UI
function updateChildTasksCompletionUI(parentId, completed) {
    const children = tasks.filter(task => task.parentId === parentId);
    
    children.forEach(child => {
        updateTaskCompletionUI(child.id, completed);
        // 递归处理子任务的子任务
        updateChildTasksCompletionUI(child.id, completed);
    });
}

// 更新父任务状态（如果所有子任务完成，父任务也完成）
function updateParentTaskStatus(parentId) {
    if (!parentId) return;
    
    const parentIndex = tasks.findIndex(task => task.id === parentId);
    if (parentIndex !== -1) {
        const parentTask = tasks[parentIndex];
        const children = tasks.filter(task => task.parentId === parentId);
        const allCompleted = children.length > 0 && children.every(child => child.completed);
        
        const previousState = parentTask.completed;
        parentTask.completed = allCompleted;
        
        // 如果父任务状态发生变化，更新UI
        if (previousState !== allCompleted) {
            updateTaskCompletionUI(parentId, allCompleted);
            
            // 更新任务表进度
            updateBoardProgress(parentTask.boardId);
        }
        
        // 递归更新上层父任务
        updateParentTaskStatus(parentTask.parentId);
    }
}

// 删除任务
function deleteTask(taskId) {
    if (confirm('确定要删除这个任务吗？如果有子任务，子任务也会被删除。')) {
        // 获取任务所在的任务表ID，用于后续更新进度
        const task = tasks.find(task => task.id === taskId);
        if (!task) return;
        
        const boardId = task.boardId;
        const affectedBoardIds = new Set([boardId]);
        
        // 递归删除所有子任务，并收集涉及的任务表ID
        deleteChildTasks(taskId, affectedBoardIds);
        
        // 找到要删除的任务
        const taskIndex = tasks.findIndex(task => task.id === taskId);
        if (taskIndex !== -1) {
            const parentId = tasks[taskIndex].parentId;
            
            // 删除任务
            tasks.splice(taskIndex, 1);
            
            // 清理折叠状态
            delete taskFoldStates[taskId];
            
            // 更新父任务状态
            updateParentTaskStatus(parentId);
            
            saveData();
            saveTaskFoldStates();
            
            console.log('任务已从数据中删除');
            
            // 立即从DOM中移除元素
            const taskItem = document.querySelector(`.task-item[data-id="${taskId}"]`);
            
            if (taskItem) {
                taskItem.remove();
                console.log('任务元素已从DOM中移除');
            }
            
            // 更新任务表的进度统计
            affectedBoardIds.forEach(id => {
                // 刷新任务表的进度条和统计信息
                updateBoardProgressUI(id);
            });
        }
    }
}

// 递归删除子任务，并收集涉及的任务表ID
function deleteChildTasks(parentId, affectedBoardIds = new Set()) {
    const children = tasks.filter(task => task.parentId === parentId);
    console.log(`删除任务[${parentId}]的${children.length}个子任务...`);
    
    children.forEach(child => {
        // 记录子任务所在的任务表ID
        affectedBoardIds.add(child.boardId);
        console.log(`准备删除子任务: ${child.name} (ID: ${child.id})`);
        
        // 先递归删除子任务的子任务
        deleteChildTasks(child.id, affectedBoardIds);
        
        // 然后删除子任务
        const childIndex = tasks.findIndex(task => task.id === child.id);
        if (childIndex !== -1) {
            tasks.splice(childIndex, 1);
            console.log(`已删除子任务: ${child.name} (ID: ${child.id})`);
            
            // 清理折叠状态
            delete taskFoldStates[child.id];
        }
    });
    
    return affectedBoardIds;
}

// 清理所有已完成的任务
function clearCompletedTasks() {
    if (confirm('确定要清理所有已完成的任务吗？')) {
        // 获取已完成任务的ID列表和涉及的任务表ID
        const completedTasks = tasks.filter(task => task.completed);
        const completedTaskIds = completedTasks.map(task => task.id);
        const affectedBoardIds = new Set(completedTasks.map(task => task.boardId));
        
        // 过滤出所有未完成的任务
        tasks = tasks.filter(task => !task.completed);
        
        // 清理已完成任务的折叠状态
        completedTaskIds.forEach(taskId => {
            delete taskFoldStates[taskId];
        });
        
        saveData();
        saveTaskFoldStates();
        
        // 更新所有涉及的任务表进度
        affectedBoardIds.forEach(boardId => updateBoardProgress(boardId));
    }
}

// 渲染任务表列表
function renderTaskBoardsList() {
    const container = document.getElementById('taskBoardsList');
    container.innerHTML = '';
    
    taskBoards.forEach((board, index) => {
        const boardItem = document.createElement('div');
        boardItem.className = `list-group-item list-group-item-action d-flex justify-content-between align-items-center ${selectedBoardId === board.id ? 'active' : ''}`;
        boardItem.innerHTML = `
            <div class="d-flex align-items-center">
                <span class="board-name">${board.name}</span>
            </div>
            <div class="btn-group btn-group-sm">
                <div class="move-buttons">
                    <button class="btn btn-xs btn-outline-secondary move-up-btn ${index === 0 ? 'disabled' : ''}" data-id="${board.id}" title="上移" ${index === 0 ? 'disabled' : ''}>
                        <i class="fas fa-arrow-up"></i>
                    </button>
                    <button class="btn btn-xs btn-outline-secondary move-down-btn ${index === taskBoards.length - 1 ? 'disabled' : ''}" data-id="${board.id}" title="下移" ${index === taskBoards.length - 1 ? 'disabled' : ''}>
                        <i class="fas fa-arrow-down"></i>
                    </button>
                </div>
                <button class="btn btn-xs btn-outline-danger delete-board-btn" data-id="${board.id}">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        
        // 点击切换当前任务表并滚动到对应位置
        boardItem.addEventListener('click', function(e) {
            if (!e.target.closest('.btn')) {
                selectedBoardId = board.id;
                shouldAutoScroll = true; // 用户手动切换任务表时，允许自动滚动
                renderTaskBoardsList();
                renderTaskBoards();
                
                // 已在 renderTaskBoards 中通过 shouldAutoScroll 控制滚动行为，不再需要这里的滚动代码
            }
        });
        
        container.appendChild(boardItem);
    });
    
    // 为删除按钮添加事件监听
    document.querySelectorAll('.delete-board-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const boardId = this.dataset.id;
            deleteTaskBoard(boardId);
        });
    });
    
    // 为上移按钮添加事件监听
    document.querySelectorAll('.move-up-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const boardId = this.dataset.id;
            moveTaskBoard(boardId, 'up');
        });
    });
    
    // 为下移按钮添加事件监听
    document.querySelectorAll('.move-down-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const boardId = this.dataset.id;
            moveTaskBoard(boardId, 'down');
        });
    });
}

// 渲染任务表选择器
function renderTaskBoardSelectors() {
    const selectors = [
        document.getElementById('taskBoardSelect'),
        document.getElementById('batchTaskBoardSelect'),
        document.getElementById('editTaskBoard')
    ];

    selectors.forEach(selector => {
        if (selector) {
            selector.innerHTML = '';

            taskBoards.forEach(board => {
                const option = document.createElement('option');
                option.value = board.id;
                option.textContent = board.name;
                option.selected = board.id === selectedBoardId;
                selector.appendChild(option);
            });
        }
    });
}

// 渲染父任务选择器 - 仅用于编辑任务时
function renderParentTaskSelectors() {
    const selector = document.getElementById('editParentTask');
    
    if (selector) {
        // 保留第一个选项（顶级任务）
        const firstOption = selector.options[0];
        selector.innerHTML = '';
        selector.appendChild(firstOption);
        
        // 为每个任务表创建一个选项组
        taskBoards.forEach(board => {
            const topLevelTasks = tasks.filter(task => 
                task.boardId === board.id && 
                task.level === 0
            );
            
            if (topLevelTasks.length > 0) {
                const optgroup = document.createElement('optgroup');
                optgroup.label = board.name;
                
                // 添加顶层任务
                topLevelTasks.forEach(task => {
                    if (task.level < 2) { // 只有level小于2的任务才能有子任务
                        const option = document.createElement('option');
                        option.value = task.id;
                        option.textContent = task.name;
                        optgroup.appendChild(option);
                        
                        // 添加二级子任务（缩进显示）
                        const level1Children = tasks.filter(t => t.parentId === task.id);
                        level1Children.forEach(childTask => {
                            if (childTask.level < 2) { // 只有level小于2的任务才能有子任务
                                const childOption = document.createElement('option');
                                childOption.value = childTask.id;
                                childOption.textContent = `-- ${childTask.name}`;
                                optgroup.appendChild(childOption);
                            }
                        });
                    }
                });
                
                selector.appendChild(optgroup);
            }
        });
    }
}

// 渲染任务表内容
function renderTaskBoards() {
    const container = document.getElementById('taskBoardsContainer');
    container.innerHTML = '';
    
    // 检查是否有任务表
    if (taskBoards.length === 0) {
        console.log('没有任务表可渲染');
        return;
    }
    
    console.log(`开始渲染所有任务表，共 ${taskBoards.length} 个任务表`);
    
    // 渲染所有任务表，而不仅仅是当前选中的任务表
    taskBoards.forEach(board => {
        console.log(`渲染任务表: ${board.name} (ID: ${board.id})`);
        
        const boardTasks = tasks.filter(task => task.boardId === board.id);
        console.log(`该任务表共有 ${boardTasks.length} 个任务`);
        
        // 创建任务表容器
        const boardContainer = document.createElement('div');
        boardContainer.className = 'task-board';
        boardContainer.id = `board-${board.id}`;
        
        // 如果是当前选中的任务表，添加一个样式
        if (board.id === selectedBoardId) {
            boardContainer.classList.add('active-board');
            boardContainer.style.borderLeft = '4px solid #3498db';
        }
        
        // 创建任务表头部
        const boardHeader = document.createElement('div');
        boardHeader.className = 'task-board-header';
        
        const boardTitle = document.createElement('h5');
        boardTitle.className = 'task-board-title d-flex align-items-center';
        
        // 添加折叠/展开按钮
        const foldIcon = document.createElement('i');
        const boardFolded = boardFoldStates[board.id] === false;
        foldIcon.className = `fas fa-chevron-${boardFolded ? 'right' : 'down'} me-2`;
        foldIcon.style.cursor = 'pointer';
        boardTitle.appendChild(foldIcon);
        
        // 添加任务表名称
        const boardNameSpan = document.createElement('span');
        boardNameSpan.className = 'board-name';
        boardNameSpan.textContent = board.name;
        boardNameSpan.style.cursor = 'pointer';
        boardNameSpan.style.userSelect = 'none';
        boardTitle.appendChild(boardNameSpan);
        
        // 添加任务表进度
        const boardToolbar = document.createElement('div');
        boardToolbar.className = 'd-flex align-items-center';
        
        // 计算进度
        const completedCount = boardTasks.filter(task => task.completed).length;
        const totalCount = boardTasks.length;
        const progressPercentage = totalCount > 0 
            ? Math.round((completedCount / totalCount) * 100) 
            : 0;
        
        // 添加进度信息
        const boardProgress = document.createElement('div');
        boardProgress.className = 'me-3';
        boardProgress.innerHTML = `
            <span class="badge bg-${progressPercentage === 100 ? 'success' : 'primary'}">
                ${completedCount}/${totalCount}
            </span>
        `;
        boardToolbar.appendChild(boardProgress);

        // 添加按钮组
        const buttonGroup = document.createElement('div');
        buttonGroup.className = 'btn-group btn-group-sm';

        // 添加任务按钮
        const addTaskBtn = document.createElement('button');
        addTaskBtn.className = 'btn btn-outline-success btn-sm';
        addTaskBtn.innerHTML = '<i class="fas fa-plus"></i>';
        addTaskBtn.title = '添加任务';
        addTaskBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            // 创建一个新的空任务，进入编辑状态
            const newTask = {
                id: generateUUID(),
                name: '',
                completed: false,
                createdAt: new Date().toISOString(),
                deadline: null,
                parentId: null,
                boardId: board.id,
                isEditing: true
            };

            tasks.push(newTask);
            saveData();
            renderTaskBoards();

            // 聚焦到新创建的输入框
            setTimeout(() => {
                const input = document.querySelector(`input[data-task-id="${newTask.id}"]`);
                if (input) {
                    input.focus();
                }
            }, 100);
        });
        buttonGroup.appendChild(addTaskBtn);

        // 添加编辑任务表名称按钮
        const editBoardTitleBtn = document.createElement('button');
        editBoardTitleBtn.className = 'btn btn-outline-secondary btn-sm';
        editBoardTitleBtn.innerHTML = '<i class="fas fa-edit"></i>';
        editBoardTitleBtn.title = '编辑任务表名称';
        buttonGroup.appendChild(editBoardTitleBtn);

        boardToolbar.appendChild(buttonGroup);
        
        // 点击任务表名称时收放任务列表
        boardNameSpan.addEventListener('click', function(e) {
            e.stopPropagation();

            const taskList = boardContainer.querySelector('.task-list');
            const isFolded = taskList.style.display === 'none';

            // 切换显示状态
            taskList.style.display = isFolded ? 'block' : 'none';
            foldIcon.className = `fas fa-chevron-${isFolded ? 'down' : 'right'} me-2`;

            // 更新并保存状态
            boardFoldStates[board.id] = isFolded;
            saveBoardFoldStates();
        });
        
        boardHeader.appendChild(boardTitle);
        boardHeader.appendChild(boardToolbar);
        boardContainer.appendChild(boardHeader);
        
        // 添加进度条 - 移到任务列表前面
        const progressContainer = document.createElement('div');
        progressContainer.className = 'progress';
        progressContainer.style.marginBottom = '10px'; // 添加下边距
        progressContainer.style.marginTop = '5px'; // 添加上边距
        progressContainer.innerHTML = `
            <div class="progress-bar ${progressPercentage === 100 ? 'bg-success' : ''}" 
                 role="progressbar" 
                 style="width: ${progressPercentage}%;" 
                 aria-valuenow="${progressPercentage}" 
                 aria-valuemin="0" 
                 aria-valuemax="100">
            </div>
        `;
        boardContainer.appendChild(progressContainer);
        
        // 创建任务列表
        const taskList = document.createElement('div');
        taskList.className = 'task-list';
        
        // 根据折叠状态设置初始显示
        taskList.style.display = boardFolded ? 'none' : 'block';
        
        boardContainer.appendChild(taskList);
        
        container.appendChild(boardContainer);
        
        // 过滤并排序顶级任务
        const showCompleted = document.getElementById('showCompletedTasks').checked;
        let topLevelTasks = boardTasks.filter(task => !task.parentId);
        console.log(`找到 ${topLevelTasks.length} 个顶级任务`);
        
        if (!showCompleted) {
            const filteredTasks = topLevelTasks.filter(task => !task.completed);
            console.log(`过滤掉已完成任务后剩余 ${filteredTasks.length} 个顶级任务`);
            topLevelTasks = filteredTasks;
        }
        
        const sortByDeadline = document.getElementById('sortByDeadline').checked;
        if (sortByDeadline) {
            topLevelTasks.sort((a, b) => {
                if (!a.deadline) return 1;
                if (!b.deadline) return -1;
                return new Date(a.deadline) - new Date(b.deadline);
            });
        } else {
            topLevelTasks.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
        }
        
        // 添加编辑任务表名称功能
        if (editBoardTitleBtn) {
            // 处理编辑任务表名称
            const handleEditBoardTitleClick = function(e) {
                e.stopPropagation();
                e.preventDefault();
                
                const currentName = boardNameSpan.textContent;
                
                // 创建输入框
                const inputEl = document.createElement('input');
                inputEl.type = 'text';
                inputEl.className = 'form-control form-control-sm';
                inputEl.value = currentName;
                inputEl.style.width = '200px';
                
                // 替换名称文本为输入框
                boardNameSpan.textContent = '';
                boardNameSpan.appendChild(inputEl);
                boardNameSpan.style.cursor = 'default';
                
                // 聚焦输入框
                inputEl.focus();
                inputEl.select();
                
                // 编辑时临时禁用点击收放功能
                boardNameSpan.style.pointerEvents = 'none';
                
                // 阻止输入框上的点击事件冒泡
                inputEl.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
                
                // 处理输入完成事件
                inputEl.addEventListener('blur', function() {
                    const newName = this.value.trim();
                    if (newName && newName !== currentName) {
                        updateTaskBoardName(board.id, newName);
                    } else {
                        // 如果名称未更改或为空，还原原名称
                        boardNameSpan.textContent = currentName;
                    }
                    // 恢复点击功能
                    boardNameSpan.style.pointerEvents = 'auto';
                    boardNameSpan.style.cursor = 'pointer';
                });
                
                // 处理按下回车
                inputEl.addEventListener('keydown', function(e) {
                    e.stopPropagation(); // 阻止事件冒泡
                    if (e.key === 'Enter') {
                        this.blur();
                    } else if (e.key === 'Escape') {
                        boardNameSpan.textContent = currentName;
                        // 恢复点击功能
                        boardNameSpan.style.pointerEvents = 'auto';
                        boardNameSpan.style.cursor = 'pointer';
                    }
                });
            };
            
            editBoardTitleBtn.addEventListener('click', handleEditBoardTitleClick);
        }
        
        // 渲染顶级任务
        console.log(`开始渲染 ${topLevelTasks.length} 个顶级任务`);
        topLevelTasks.forEach(task => {
            console.log(`渲染顶级任务: ${task.name} (ID: ${task.id})`);
            renderTask(task, taskList);
        });
    });
    
    // 为任务项添加事件监听
    addTaskEventListeners();
    
    // 重新设置右键菜单
    console.log('重新设置右键菜单...');
    setupTaskContextMenu();
    
    // 仅在 shouldAutoScroll 为 true 时才自动滚动到选中的任务表
    if (selectedBoardId && shouldAutoScroll) {
        console.log('准备自动滚动到选中的任务表:', selectedBoardId);
        setTimeout(() => {
            const selectedBoard = document.getElementById(`board-${selectedBoardId}`);
            if (selectedBoard) {
                selectedBoard.scrollIntoView({ behavior: 'smooth', block: 'start' });
                // 滚动完成后重置标志位，避免其他操作触发自动滚动
                shouldAutoScroll = false;
            }
        }, 100);
    }
}

// 渲染单个任务及其子任务
function renderTask(task, container) {
    const taskItem = document.createElement('div');
    taskItem.className = `task-item ${task.completed ? 'task-completed' : ''}`;
    taskItem.setAttribute('data-id', task.id);
    
    // 检查截止日期是否接近或已过期
    let deadlineClass = '';
    let deadlineText = '';
    
    if (task.deadline) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        const deadline = new Date(task.deadline);
        deadline.setHours(0, 0, 0, 0);
        
        const diffTime = deadline - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays < 0) {
            deadlineClass = 'urgent';
            deadlineText = `已过期 ${Math.abs(diffDays)} 天`;
        } else if (diffDays === 0) {
            deadlineClass = 'urgent';
            deadlineText = '今天截止';
        } else if (diffDays <= 3) {
            deadlineClass = 'urgent';
            deadlineText = `剩余 ${diffDays} 天`;
        } else {
            deadlineText = `截止: ${formatDate(task.deadline)}`;
        }
    }
    
    taskItem.innerHTML = `
        <div class="task-header">
            <div class="task-title">
                <input type="checkbox" class="task-checkbox" ${task.completed ? 'checked' : ''}>
                <p class="task-name">${task.name}</p>
                ${task.deadline ? `<span class="task-deadline ${deadlineClass}">${deadlineText}</span>` : ''}
            </div>
            <div class="task-actions">
                ${task.level < 3 ? `<button class="btn btn-sm btn-outline-success add-subtask-btn btn-icon" data-id="${task.id}" title="添加子任务"><i class="fas fa-plus"></i></button>` : ''}
                <button class="btn btn-sm btn-outline-primary edit-task-btn btn-icon" data-id="${task.id}" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger delete-task-btn btn-icon" data-id="${task.id}" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
        <div class="subtask-form-container" style="display: none;">
            <form class="add-subtask-form mt-1 border-start ps-2 py-1">
                <div class="row g-1 align-items-center">
                    <div class="col-7">
                        <input type="text" class="form-control form-control-sm subtask-name" placeholder="输入子任务名称" required>
                    </div>
                    <div class="col-3">
                        <input type="text" class="form-control form-control-sm subtask-deadline" placeholder="MMDD">
                    </div>
                    <div class="col-2">
                        <button type="submit" class="btn btn-primary btn-sm w-100"><i class="fas fa-plus"></i></button>
                    </div>
                </div>
            </form>
        </div>
    `;
    
    container.appendChild(taskItem);
    
    console.log(`为任务[${task.id}]创建了子任务表单，层级: ${task.level}`);
    
    // 为子任务表单添加提交事件
    const subtaskForm = taskItem.querySelector('.add-subtask-form');
    if (subtaskForm) {
        subtaskForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const subtaskName = this.querySelector('.subtask-name').value.trim();
            let subtaskDeadline = this.querySelector('.subtask-deadline').value.trim();
            
            console.log(`尝试为任务[${task.id}]添加子任务: ${subtaskName}`);
            
            // 处理MMDD格式的日期，添加2025年
            if (subtaskDeadline && /^\d{4}$/.test(subtaskDeadline)) {
                const month = subtaskDeadline.substring(0, 2);
                const day = subtaskDeadline.substring(2, 4);
                subtaskDeadline = `2025-${month}-${day}`;
            }
            
            if (subtaskName) {
                addTask(subtaskName, subtaskDeadline, task.id, task.boardId);
                this.querySelector('.subtask-name').value = '';
                this.querySelector('.subtask-deadline').value = '';
                this.parentElement.style.display = 'none'; // 隐藏表单
                console.log(`成功添加子任务到父任务[${task.id}]`);
            }
        });

        // 为子任务表单输入字段添加 ESC 键取消功能
        const inputs = subtaskForm.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    console.log('按下ESC键，取消添加子任务');
                    e.preventDefault(); // 阻止默认行为
                    e.stopPropagation(); // 阻止事件冒泡
                    
                    // 清空输入
                    subtaskForm.querySelector('.subtask-name').value = '';
                    subtaskForm.querySelector('.subtask-deadline').value = '';
                    
                    // 隐藏子任务表单
                    subtaskForm.parentElement.style.display = 'none';
                    
                    // 返回false确保事件不继续传播
                    return false;
                }
            });
        });
    }
    
    // 渲染子任务
    const childTasks = tasks.filter(t => t.parentId === task.id);
    console.log(`任务[${task.id}] ${task.name} 有 ${childTasks.length} 个子任务`);
    
    const showCompleted = document.getElementById('showCompletedTasks').checked;
    
    // 如果有子任务
    if (childTasks.length > 0) {
        const subtasksContainer = document.createElement('div');
        subtasksContainer.className = 'subtasks-container';
        
        // 根据任务的折叠状态决定子任务容器是否显示
        const isExpanded = taskFoldStates[task.id] === true;
        subtasksContainer.style.display = isExpanded ? 'block' : 'none';
        
        taskItem.appendChild(subtasksContainer);
        
        // 过滤或排序子任务
        let filteredChildTasks = childTasks;
        if (!showCompleted) {
            filteredChildTasks = filteredChildTasks.filter(task => !task.completed);
            console.log(`过滤掉已完成子任务后，任务[${task.id}]剩余 ${filteredChildTasks.length} 个子任务`);
        }
        
        const sortByDeadline = document.getElementById('sortByDeadline').checked;
        if (sortByDeadline) {
            filteredChildTasks.sort((a, b) => {
                if (!a.deadline) return 1;
                if (!b.deadline) return -1;
                return new Date(a.deadline) - new Date(b.deadline);
            });
        } else {
            filteredChildTasks.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
        }
        
        // 渲染每个子任务
        filteredChildTasks.forEach(childTask => {
            console.log(`渲染子任务: ${childTask.name} (ID: ${childTask.id}, 父任务: ${task.id})`);
            renderTask(childTask, subtasksContainer);
        });
    }
}

// 为任务项添加事件监听
function addTaskEventListeners() {
    // 复选框点击事件
    document.querySelectorAll('.task-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const taskId = this.closest('.task-item').dataset.id;
            toggleTaskCompletion(taskId);
        });
        
        // 移动设备触摸事件已移除
    });
    
    // 不再设置任务项长按事件
    // setupLongPressEvents();
    
    // 任务名称点击事件（展开/折叠子任务）
    document.querySelectorAll('.task-name').forEach(name => {
        name.addEventListener('click', function() {
            const taskItem = this.closest('.task-item');
            const taskId = taskItem.dataset.id;
            const subtasksContainer = taskItem.querySelector('.subtasks-container');
            
            if (subtasksContainer) {
                const isCurrentlyVisible = subtasksContainer.style.display !== 'none';
                const newDisplayState = isCurrentlyVisible ? 'none' : 'block';
                subtasksContainer.style.display = newDisplayState;
                
                // 更新并保存折叠状态
                taskFoldStates[taskId] = newDisplayState === 'block';
                saveTaskFoldStates();
            }
        });
        
        // 移动设备触摸事件已移除
    });
    
    // 添加子任务按钮点击事件
    document.querySelectorAll('.add-subtask-btn').forEach(btn => {
        // 使用一个处理函数同时处理点击和触摸
        const handleAddSubtaskClick = function(e) {
            e.stopPropagation(); // 阻止事件冒泡
            e.preventDefault(); // 防止触发其他事件
            
            // 确保点击事件不会传播到父元素
            if (e && e.cancelBubble !== undefined) e.cancelBubble = true;
            
            const taskItem = this.closest('.task-item');
            const taskId = taskItem.dataset.id;
            console.log(`普通点击添加子任务按钮，任务ID: ${taskId}`);
            
            // 先关闭所有其他的子任务表单，确保只有当前任务的表单打开
            document.querySelectorAll('.subtask-form-container').forEach(container => {
                if (container !== taskItem.querySelector('.subtask-form-container')) {
                    container.style.display = 'none';
                }
            });
            
            const subtaskFormContainer = taskItem.querySelector('.subtask-form-container');
            
            // 切换表单显示状态
            if (subtaskFormContainer) {
                const isVisible = subtaskFormContainer.style.display === 'block';
                subtaskFormContainer.style.display = isVisible ? 'none' : 'block';
                console.log(`${isVisible ? '隐藏' : '显示'}子任务表单，任务ID: ${taskId}`);
                
                // 如果显示表单，则聚焦输入框
                if (!isVisible) {
                    const inputField = subtaskFormContainer.querySelector('.subtask-name');
                    if (inputField) {
                        setTimeout(() => inputField.focus(), 100);
                    }
                }
            } else {
                console.error(`找不到子任务表单容器，任务ID: ${taskId}`);
            }
            
            // 返回false，确保事件不会继续传播
            return false;
        };
        
        // 确保卸载旧的事件处理函数(如果存在)
        btn.removeEventListener('click', handleAddSubtaskClick);

        // 重新绑定事件处理函数
        btn.addEventListener('click', handleAddSubtaskClick);
    });
    
    // 编辑任务按钮点击事件
    document.querySelectorAll('.edit-task-btn').forEach(btn => {
        // 使用一个处理函数同时处理点击和触摸
        const handleEditTaskClick = function(e) {
            e.stopPropagation(); // 阻止事件冒泡
            e.preventDefault(); // 防止触发其他事件
            const taskId = this.dataset.id;
            openEditTaskModal(taskId);
        };
        
        btn.addEventListener('click', handleEditTaskClick);
    });
    
    // 删除任务按钮点击事件
    document.querySelectorAll('.delete-task-btn').forEach(btn => {
        // 使用一个处理函数同时处理点击和触摸
        const handleDeleteTaskClick = function(e) {
            e.stopPropagation(); // 阻止事件冒泡
            e.preventDefault(); // 防止触发其他事件
            
            // 强制阻止事件冒泡
            if (e && e.cancelBubble !== undefined) e.cancelBubble = true;
            
            // 使用dataset获取任务ID
            let taskId = this.dataset.id;
            console.log(`尝试删除任务，ID: ${taskId}`);
            
            // 如果未能通过dataset获取，则尝试从任务项元素获取
            if (!taskId) {
                const taskItem = this.closest('.task-item');
                if (taskItem) {
                    taskId = taskItem.dataset.id;
                    console.log(`从父元素获取任务ID: ${taskId}`);
                }
            }
            
            // 确保获取到有效的任务ID
            if (taskId) {
                // 查找对应的任务元素，准备立即从DOM中移除
                const taskItem = document.querySelector(`.task-item[data-id="${taskId}"]`);
                
                // 直接调用删除函数，但不等待重绘
                if (confirm('确定要删除这个任务吗？如果有子任务，子任务也会被删除。')) {
                    // 获取任务所在的任务表ID，用于后续更新进度
                    const task = tasks.find(task => task.id === taskId);
                    if (!task) return;
                    
                    const boardId = task.boardId;
                    const affectedBoardIds = new Set([boardId]);
                    
                    // 递归删除所有子任务，并收集涉及的任务表ID
                    deleteChildTasks(taskId, affectedBoardIds);
                    
                    // 找到要删除的任务
                    const taskIndex = tasks.findIndex(task => task.id === taskId);
                    if (taskIndex !== -1) {
                        const parentId = tasks[taskIndex].parentId;
                        
                        // 删除任务
                        tasks.splice(taskIndex, 1);
                        
                        // 清理折叠状态
                        delete taskFoldStates[taskId];
                        
                        // 更新父任务状态
                        updateParentTaskStatus(parentId);
                        
                        saveData();
                        saveTaskFoldStates();
                        
                        console.log('任务已从数据中删除');
                        
                        // 立即从DOM中移除元素
                        if (taskItem) {
                            taskItem.remove();
                            console.log('任务元素已从DOM中移除');
                        }
                        
                        // 更新任务表的进度统计
                        affectedBoardIds.forEach(id => {
                            // 刷新任务表的进度条和统计信息
                            updateBoardProgressUI(id);
                        });
                    }
                }
            } else {
                console.error('删除失败: 无法获取任务ID');
                alert('删除失败: 无法获取任务ID');
            }
            
            // 返回false，确保事件不会继续传播
            return false;
        };
        
        // 确保卸载旧的事件处理函数(如果存在)
        btn.removeEventListener('click', handleDeleteTaskClick);

        // 重新绑定事件处理函数
        btn.addEventListener('click', handleDeleteTaskClick);
    });
    
    // 不再调用setupTaskContextMenu，避免重复设置
    console.log('为任务项添加了基本事件监听器');
}

// 设置任务右键菜单
function setupTaskContextMenu() {
    console.log('设置任务右键菜单...');
    
    // 确保页面中有右键菜单容器，如果没有则创建一个
    let contextMenu = document.getElementById('task-context-menu');
    if (!contextMenu) {
        // 创建右键菜单
        contextMenu = document.createElement('div');
        contextMenu.id = 'task-context-menu';
        contextMenu.className = 'context-menu';
        contextMenu.innerHTML = `
            <div class="context-menu-item" id="context-add-subtask">
                <i class="fas fa-plus text-success"></i> 添加子任务
            </div>
            <div class="context-menu-item" id="context-edit-task">
                <i class="fas fa-edit text-primary"></i> 编辑任务
            </div>
            <div class="context-menu-item" id="context-toggle-complete">
                <i class="fas fa-check text-success"></i> <span id="complete-text">完成任务</span>
            </div>
            <div class="context-menu-item text-danger" id="context-delete-task">
                <i class="fas fa-trash"></i> 删除任务
            </div>
        `;
        document.body.appendChild(contextMenu);
        
        // 点击页面其他地方关闭菜单
        document.addEventListener('click', function(e) {
            if (!contextMenu.contains(e.target)) {
                contextMenu.style.display = 'none';
            }
        });
        
        // 按下ESC键关闭菜单
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                contextMenu.style.display = 'none';
            }
        });
    }
    
    // 移除所有任务项上的右键事件处理函数
    document.removeEventListener('contextmenu', handleTaskContextMenu);
    
    // 右键菜单事件处理函数
    function handleTaskContextMenu(e) {
        // 检查是否点击了任务项
        const taskItem = e.target.closest('.task-item');
        if (!taskItem) return;
        
        // 阻止默认右键菜单
        e.preventDefault();
        
        // 获取任务ID
        const taskId = taskItem.dataset.id;
        if (!taskId) return;
        
        // 获取任务数据
        const task = tasks.find(t => t.id === taskId);
        if (!task) return;
        
        console.log(`显示任务[${taskId}]的右键菜单`);
        
        // 更新菜单项状态
        const addSubtaskItem = document.getElementById('context-add-subtask');
        const toggleCompleteItem = document.getElementById('context-toggle-complete');
        const completeText = document.getElementById('complete-text');
        
        // 设置添加子任务选项是否可用
        if (task.level >= 3) {
            addSubtaskItem.style.opacity = '0.5';
            addSubtaskItem.style.cursor = 'not-allowed';
        } else {
            addSubtaskItem.style.opacity = '1';
            addSubtaskItem.style.cursor = 'pointer';
        }
        
        // 设置完成/取消完成文本
        if (task.completed) {
            completeText.textContent = '取消完成';
        } else {
            completeText.textContent = '完成任务';
        }
        
        // 设置菜单位置
        contextMenu.style.left = `${e.pageX}px`;
        contextMenu.style.top = `${e.pageY}px`;
        contextMenu.style.display = 'block';
        
        // 保存当前任务ID到菜单
        contextMenu.dataset.taskId = taskId;
        
        // 绑定菜单项点击事件
        document.getElementById('context-add-subtask').onclick = function() {
            if (task.level < 3) {
                // 获取任务的子任务表单并显示
                const subtaskFormContainer = taskItem.querySelector('.subtask-form-container');
                if (subtaskFormContainer) {
                    subtaskFormContainer.style.display = 'block';
                    const inputField = subtaskFormContainer.querySelector('.subtask-name');
                    if (inputField) {
                        setTimeout(() => inputField.focus(), 100);
                    }
                }
                contextMenu.style.display = 'none';
            }
        };
        
        document.getElementById('context-edit-task').onclick = function() {
            openEditTaskModal(taskId);
            contextMenu.style.display = 'none';
        };
        
        document.getElementById('context-toggle-complete').onclick = function() {
            toggleTaskCompletion(taskId);
            contextMenu.style.display = 'none';
        };
        
        document.getElementById('context-delete-task').onclick = function() {
            deleteTask(taskId);
            contextMenu.style.display = 'none';
        };
    }
    
    // 为整个文档添加右键菜单事件
    document.addEventListener('contextmenu', handleTaskContextMenu);
}

// 更新任务表进度UI
function updateBoardProgressUI(boardId) {
    // 找到对应的任务表容器
    const boardContainer = document.getElementById(`board-${boardId}`);
    if (!boardContainer) {
        console.warn(`未找到任务表容器，ID: board-${boardId}`);
        return;
    }
    
    // 获取该任务表的所有任务
    const boardTasks = tasks.filter(task => task.boardId === boardId);
    
    // 计算进度
    const completedCount = boardTasks.filter(task => task.completed).length;
    const totalCount = boardTasks.length;
    const progressPercentage = totalCount > 0 
        ? Math.round((completedCount / totalCount) * 100) 
        : 0;
    
    console.log(`更新任务表进度: ${boardId}, 完成: ${completedCount}/${totalCount}, 百分比: ${progressPercentage}%`);
    
    // 更新进度条
    const progressBar = boardContainer.querySelector('.progress-bar');
    if (progressBar) {
        progressBar.style.width = `${progressPercentage}%`;
        progressBar.setAttribute('aria-valuenow', progressPercentage);
        
        // 根据完成情况更新进度条颜色
        if (progressPercentage === 100) {
            progressBar.classList.add('bg-success');
        } else {
            progressBar.classList.remove('bg-success');
        }
    }
    
    // 更新进度信息
    const boardProgress = boardContainer.querySelector('.me-3');
    if (boardProgress) {
        boardProgress.innerHTML = `
            <span class="badge bg-${progressPercentage === 100 ? 'success' : 'primary'}">
                ${completedCount}/${totalCount}
            </span>
        `;
    }
}

// 使用updateBoardProgressUI来实现updateBoardProgress
function updateBoardProgress(boardId) {
    // 更新UI
    updateBoardProgressUI(boardId);
}

// 打开编辑任务模态框
function openEditTaskModal(taskId) {
    console.log(`打开编辑任务模态框，任务ID: ${taskId}`);
    
    // 查找任务
    const task = tasks.find(t => t.id === taskId);
    if (!task) {
        console.error(`找不到ID为 ${taskId} 的任务`);
        alert('找不到要编辑的任务');
        return;
    }
    
    // 填充表单字段
    document.getElementById('editTaskId').value = task.id;
    document.getElementById('editTaskName').value = task.name;
    
    // 处理截止日期
    if (task.deadline) {
        // 将 ISO 日期格式转换为 MMDD 格式
        const deadline = new Date(task.deadline);
        const month = (deadline.getMonth() + 1).toString().padStart(2, '0');
        const day = deadline.getDate().toString().padStart(2, '0');
        document.getElementById('editTaskDeadline').value = `${month}${day}`;
    } else {
        document.getElementById('editTaskDeadline').value = '';
    }
    
    // 设置任务表选择
    const boardSelect = document.getElementById('editTaskBoard');
    if (boardSelect) {
        boardSelect.value = task.boardId;
    }
    
    // 设置父任务选择
    const parentTaskSelect = document.getElementById('editParentTask');
    if (parentTaskSelect) {
        parentTaskSelect.value = task.parentId || '';
    }
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('editTaskModal'));
    modal.show();
}

// 更新任务表名称
function updateTaskBoardName(boardId, newName) {
    // 找到要更新的任务表
    const boardIndex = taskBoards.findIndex(board => board.id === boardId);
    if (boardIndex === -1) {
        console.error(`找不到ID为 ${boardId} 的任务表`);
        return;
    }
    
    // 更新任务表名称
    taskBoards[boardIndex].name = newName;
    
    // 保存更新后的任务表
    saveData();
    
    // 更新UI
    renderTaskBoardsList();
    renderTaskBoardSelectors();
    renderTaskBoards();
    
    console.log(`已更新任务表名称: ${newName}`);
}
