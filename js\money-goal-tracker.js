// 获取北京时间
function getBeiJingTime() {
    const now = new Date();
    const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
    const beijing = new Date(utc + (8 * 3600000));
    return beijing.toISOString();
}

// SafeMoney类用于安全的金额处理
class SafeMoney {
    constructor(amount = 0) {
        this.value = Math.round(amount);
    }

    static fromYuan(yuan) {
        return new SafeMoney(yuan);
    }

    static fromValue(value) {
        const money = new SafeMoney(0);
        money.value = Math.round(value);
        return money;
    }

    add(other) {
        if (other instanceof SafeMoney) {
            return SafeMoney.fromValue(this.value + other.value);
        }
        return SafeMoney.fromValue(this.value + Math.round(other));
    }

    subtract(other) {
        if (other instanceof SafeMoney) {
            return SafeMoney.fromValue(this.value - other.value);
        }
        return SafeMoney.fromValue(this.value - Math.round(other));
    }

    toYuan() {
        return this.value;
    }

    toString() {
        return this.value.toString();
    }
}

// 解析各种金额格式的输入
function parseAmount(input) {
    try {
        let amount = 0;
        const units = {
            '万': 10000,
            'w': 10000,
            '十万': 100000,
            'sw': 100000,
            '百万': 1000000,
            'bw': 1000000,
            '千万': 10000000,
            'qw': 10000000,
            '亿': 100000000,
            'y': 100000000
        };
        
        // 移除所有空格和货币符号
        input = input.toString().replace(/\s+/g, '').replace(/¥|￥|\$/g, '');
        
        // 检查是否包含单位
        let unitFound = false;
        for (const [unit, multiplier] of Object.entries(units)) {
            if (input.includes(unit)) {
                const parts = input.split(unit);
                amount = parseFloat(parts[0]) * multiplier;
                if (parts[1]) {
                    amount += parseFloat(parts[1]) || 0;
                }
                unitFound = true;
                break;
            }
        }
        
        if (!unitFound) {
            amount = parseFloat(input);
        }
        
        if (isNaN(amount)) {
            throw new Error('金额格式不正确');
        }
        
        return amount;
    } catch (error) {
        console.error('解析金额出错:', error);
        return 0;
    }
}

// 数据结构和本地存储
class GoalTracker {
    constructor() {
        // 尝试从本地存储加载数据，如果没有则初始化空数组
        this.goals = JSON.parse(localStorage.getItem('moneyGoals')) || [];
        this.currentGoalId = parseInt(localStorage.getItem('currentGoalId')) || 1;
        this.currentSubgoalId = parseInt(localStorage.getItem('currentSubgoalId')) || 1;
    }

    // 保存数据到本地存储
    saveToLocalStorage() {
        localStorage.setItem('moneyGoals', JSON.stringify(this.goals));
        localStorage.setItem('currentGoalId', this.currentGoalId);
        localStorage.setItem('currentSubgoalId', this.currentSubgoalId);
    }

    // 添加新目标
    addGoal(goalData) {
        const newGoal = {
            id: this.currentGoalId++,
            name: goalData.name,
            targetAmount: parseFloat(goalData.targetAmount),
            currentAmount: 0, // 初始化为0，不再使用输入的金额
            deadline: goalData.deadline || null,
            notes: goalData.notes || '',
            createdAt: getBeiJingTime(),
            subgoals: [],
            isExpanded: true // 新添加的目标默认展开
        };

        this.goals.push(newGoal);
        this.saveToLocalStorage();
        return newGoal;
    }

    // 更新目标
    updateGoal(goalId, goalData) {
        const goalIndex = this.goals.findIndex(goal => goal.id === goalId);
        if (goalIndex === -1) return null;

        const updatedGoal = {
            ...this.goals[goalIndex],
            name: goalData.name,
            targetAmount: parseFloat(goalData.targetAmount),
            currentAmount: parseFloat(goalData.currentAmount),
            deadline: goalData.deadline || null,
            notes: goalData.notes || '',
        };

        this.goals[goalIndex] = updatedGoal;
        this.saveToLocalStorage();
        return updatedGoal;
    }

    // 删除目标
    deleteGoal(goalId) {
        const goalIndex = this.goals.findIndex(goal => goal.id === goalId);
        if (goalIndex === -1) return false;

        this.goals.splice(goalIndex, 1);
        this.saveToLocalStorage();
        return true;
    }

    // 添加子目标
    addSubgoal(goalId, subgoalData) {
        const goalIndex = this.goals.findIndex(goal => goal.id === goalId);
        if (goalIndex === -1) return null;

        const newSubgoal = {
            id: this.currentSubgoalId++,
            name: subgoalData.name,
            targetAmount: parseFloat(subgoalData.targetAmount),
            currentAmount: 0, // 初始化为0
            deadline: subgoalData.deadline || null,
            notes: subgoalData.notes || '',
            createdAt: getBeiJingTime()
        };

        this.goals[goalIndex].subgoals.push(newSubgoal);
        this.saveToLocalStorage();
        
        return newSubgoal;
    }

    // 更新子目标
    updateSubgoal(goalId, subgoalId, subgoalData) {
        const goalIndex = this.goals.findIndex(goal => goal.id === goalId);
        if (goalIndex === -1) return null;

        const subgoalIndex = this.goals[goalIndex].subgoals.findIndex(
            subgoal => subgoal.id === subgoalId
        );
        if (subgoalIndex === -1) return null;

        const updatedSubgoal = {
            ...this.goals[goalIndex].subgoals[subgoalIndex],
            name: subgoalData.name,
            targetAmount: parseFloat(subgoalData.targetAmount),
            currentAmount: parseFloat(subgoalData.currentAmount),
            deadline: subgoalData.deadline || null,
            notes: subgoalData.notes || '',
        };

        this.goals[goalIndex].subgoals[subgoalIndex] = updatedSubgoal;
        this.saveToLocalStorage();

        // 更新父目标的当前金额
        this.updateParentGoalAmount(goalId);
        
        return updatedSubgoal;
    }

    // 删除子目标
    deleteSubgoal(goalId, subgoalId) {
        const goalIndex = this.goals.findIndex(goal => goal.id === goalId);
        if (goalIndex === -1) return false;

        const subgoalIndex = this.goals[goalIndex].subgoals.findIndex(
            subgoal => subgoal.id === subgoalId
        );
        if (subgoalIndex === -1) return false;

        // 直接移除子目标，不再影响父目标当前金额
        this.goals[goalIndex].subgoals.splice(subgoalIndex, 1);
        this.saveToLocalStorage();
        
        return true;
    }

    // 根据子目标的金额更新父目标金额
    updateParentGoalAmount(goalId) {
        const goalIndex = this.goals.findIndex(goal => goal.id === goalId);
        if (goalIndex === -1) return false;

        const goal = this.goals[goalIndex];
        
        // 如果有子目标，计算所有子目标的当前金额总和
        if (goal.subgoals && goal.subgoals.length > 0) {
            const totalSubgoalAmount = goal.subgoals.reduce(
                (sum, subgoal) => sum + (parseFloat(subgoal.currentAmount) || 0), 0
            );
            
            // 更新父目标的当前金额为所有子目标金额的总和
            goal.currentAmount = totalSubgoalAmount;
        }
        // 即使没有子目标也确保当前金额不为空
        else if (goal.currentAmount === undefined || goal.currentAmount === null) {
            goal.currentAmount = 0;
        }
        
        this.saveToLocalStorage();
        return true;
    }

    // 切换目标的展开/折叠状态
    toggleGoalExpanded(goalId) {
        const goalIndex = this.goals.findIndex(goal => goal.id === goalId);
        if (goalIndex === -1) return false;

        this.goals[goalIndex].isExpanded = !this.goals[goalIndex].isExpanded;
        this.saveToLocalStorage();
        return true;
    }

    // 获取所有目标
    getAllGoals() {
        return this.goals;
    }

    // 获取单个目标
    getGoal(goalId) {
        return this.goals.find(goal => goal.id === goalId) || null;
    }

    // 获取统计数据
    getStats() {
        // 统计所有子目标的数量
        let totalSubgoals = 0;
        let nonZeroSubgoals = 0;
        let completedSubgoals = 0;
        
        // 父目标的总目标和当前金额
        let totalParentTargetAmount = 0;
        let totalParentCurrentAmount = 0;
        
        this.goals.forEach(goal => {
            // 统计父目标金额
            totalParentTargetAmount += goal.targetAmount;
            totalParentCurrentAmount += goal.currentAmount;
            
            // 如果有子目标，计算子目标相关统计
            if (goal.subgoals && goal.subgoals.length > 0) {
                totalSubgoals += goal.subgoals.length;
                
                goal.subgoals.forEach(subgoal => {
                    // 统计有进度的子目标
                    if (subgoal.currentAmount > 0) {
                        nonZeroSubgoals++;
                    }
                    
                    // 统计已完成的子目标
                    if (subgoal.currentAmount >= subgoal.targetAmount) {
                        completedSubgoals++;
                    }
                });
            } else {
                // 如果没有子目标，就把父目标计入统计
                totalSubgoals += 1;
                
                if (goal.currentAmount > 0) {
                    nonZeroSubgoals++;
                }
                
                if (goal.currentAmount >= goal.targetAmount) {
                    completedSubgoals++;
                }
            }
        });
        
        // 如果没有任何目标，默认为空数据
        if (this.goals.length === 0) {
            totalSubgoals = 0;
            nonZeroSubgoals = 0;
            completedSubgoals = 0;
        }
        
        // 总体进度基于父目标的总进度
        const overallProgress = totalParentTargetAmount > 0 
            ? Math.min(Math.round((totalParentCurrentAmount / totalParentTargetAmount) * 100), 100)
            : 0;
            
        return {
            totalGoals: totalSubgoals,
            inProgressGoals: nonZeroSubgoals,
            completedGoals: completedSubgoals,
            overallProgress
        };
    }

    // 添加金额到目标
    addMoneyToGoal(goalId, amount) {
        const goalIndex = this.goals.findIndex(goal => goal.id === goalId);
        if (goalIndex === -1) return false;
        
        const parsedAmount = parseFloat(amount);
        
        // 直接增加父目标金额，不再分配给子目标
        this.goals[goalIndex].currentAmount += parsedAmount;
        
        // 同步到主系统的存款功能
        this.saveMoneyToMainSystem(parsedAmount, `赚钱目标: ${this.goals[goalIndex].name}`);
        
        this.saveToLocalStorage();
        return true;
    }
    
    // 从父目标减少金额
    subtractMoneyFromGoal(goalId, amount) {
        const goalIndex = this.goals.findIndex(goal => goal.id === goalId);
        if (goalIndex === -1) return false;
        
        // 确保不会减到负数
        const currentAmount = this.goals[goalIndex].currentAmount;
        const amountToSubtract = Math.min(parseFloat(amount), currentAmount);
        
        // 减少父目标金额
        this.goals[goalIndex].currentAmount -= amountToSubtract;
        
        this.saveToLocalStorage();
        return true;
    }
    
    // 添加金额到子目标
    addMoneyToSubgoal(goalId, subgoalId, amount) {
        const goalIndex = this.goals.findIndex(goal => goal.id === goalId);
        if (goalIndex === -1) return false;
        
        const subgoalIndex = this.goals[goalIndex].subgoals.findIndex(
            subgoal => subgoal.id === subgoalId
        );
        if (subgoalIndex === -1) return false;
        
        const parsedAmount = parseFloat(amount);
        
        // 增加子目标金额
        this.goals[goalIndex].subgoals[subgoalIndex].currentAmount += parsedAmount;
        
        // 同时增加父目标金额
        this.goals[goalIndex].currentAmount += parsedAmount;
        
        // 同步到主系统的存款功能
        const subgoalName = this.goals[goalIndex].subgoals[subgoalIndex].name;
        this.saveMoneyToMainSystem(parsedAmount, `子目标: ${subgoalName} (${this.goals[goalIndex].name})`);
        
        this.saveToLocalStorage();
        return true;
    }

    // 连接到主系统的存款功能
    saveMoneyToMainSystem(amount, note) {
        try {
            // 获取主系统的appData
            let appData = JSON.parse(localStorage.getItem('savingsData')) || {};
            
            // 确保appData有必要的属性
            if (!appData.totalSaved) appData.totalSaved = 0;
            if (!appData.history) appData.history = [];
            
            // 处理金额
            const parsedAmount = parseFloat(amount);
            if (isNaN(parsedAmount) || parsedAmount <= 0) {
                throw new Error('无效的金额');
            }
            
            // 使用SafeMoney类处理金额
            const currentMoney = SafeMoney.fromYuan(appData.totalSaved);
            const operationMoney = SafeMoney.fromYuan(parsedAmount);
            appData.totalSaved = currentMoney.add(operationMoney).toYuan();
            
            // 添加历史记录
            const record = {
                id: Date.now(),  // 使用时间戳作为唯一ID
                type: 'deposit', // 存款类型
                amount: parsedAmount,
                note: note || '从赚钱目标系统添加',
                date: getBeiJingTime()
            };
            
            appData.history.unshift(record);  // 添加到数组开头
            
            // 保存数据到主系统
            localStorage.setItem('savingsData', JSON.stringify(appData));
            
            // 记录到控制台但不显示toast提示
            console.log(`成功同步到主系统: ¥${parsedAmount.toLocaleString()}, 备注: ${note}`);
            
            return true;
        } catch (error) {
            console.error('同步到主系统失败:', error);
            return false;
        }
    }

    // 显示提示信息
    showToast(message) {
        let toast = document.getElementById('sync-toast');
        if (!toast) {
            toast = document.createElement('div');
            toast.id = 'sync-toast';
            toast.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background-color: #4361ee;
                color: white;
                padding: 10px 15px;
                border-radius: 4px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
                z-index: 9999;
                opacity: 0;
                transition: opacity 0.3s;
                font-size: 14px;
            `;
            document.body.appendChild(toast);
        }
        
        toast.textContent = message;
        toast.style.opacity = '1';
        
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    // 导出所有数据 - 简化为内部使用
    exportData() {
        return {
            goals: this.goals,
            currentGoalId: this.currentGoalId,
            currentSubgoalId: this.currentSubgoalId
        };
    }

    // 导入数据 - 简化为内部使用
    importData(data) {
        if (data && data.goals) {
            this.goals = data.goals;
            if (data.currentGoalId) this.currentGoalId = data.currentGoalId;
            if (data.currentSubgoalId) this.currentSubgoalId = data.currentSubgoalId;
            this.saveToLocalStorage();
            return true;
        }
        return false;
    }
    
    // 重新排序目标
    reorderGoals(fromIndex, toIndex) {
        if (fromIndex < 0 || fromIndex >= this.goals.length || 
            toIndex < 0 || toIndex >= this.goals.length) {
            return false;
        }
        
        // 获取要移动的目标
        const goalToMove = this.goals[fromIndex];
        
        // 从数组中移除该目标
        this.goals.splice(fromIndex, 1);
        
        // 在新位置插入该目标
        this.goals.splice(toIndex, 0, goalToMove);
        
        // 保存到本地存储
        this.saveToLocalStorage();
        return true;
    }
}

// UI 控制器
class UIController {
    constructor(goalTracker) {
        this.goalTracker = goalTracker;
        this.initElements();
        this.initEventListeners();
        this.renderGoals();
        this.updateDashboard();
    }

    // 初始化DOM元素引用
    initElements() {
        // 主要容器
        this.goalsContainer = document.getElementById('goals-container');
        this.emptyState = document.getElementById('empty-state');
        
        // 仪表盘元素
        this.totalGoalsEl = document.getElementById('total-goals');
        this.inProgressEl = document.getElementById('in-progress');
        this.completedEl = document.getElementById('completed');
        this.overallProgressEl = document.getElementById('overall-progress');
        
        // 统一目标模态框元素
        this.goalModal = document.getElementById('goal-modal');
        this.goalForm = document.getElementById('goal-form');
        this.modalTitle = document.getElementById('modal-title');
        this.goalIdInput = document.getElementById('goal-id');
        this.parentGoalIdInput = document.getElementById('parent-goal-id');
        this.isSubgoalInput = document.getElementById('is-subgoal');
        this.goalNameInput = document.getElementById('goal-name');
        this.goalAmountInput = document.getElementById('goal-amount');
        this.currentAmountInput = document.getElementById('current-amount');
        this.goalDeadlineInput = document.getElementById('goal-deadline');
        this.goalNotesInput = document.getElementById('goal-notes');
        
        // 确保当前金额输入框始终隐藏
        if (this.currentAmountInput) {
            const formGroup = this.currentAmountInput.closest('.form-group');
            if (formGroup) {
                formGroup.style.display = 'none';
            }
        }
        
        // 确认模态框元素
        this.confirmModal = document.getElementById('confirm-modal');
        this.confirmMessage = document.getElementById('confirm-message');
        this.confirmYesBtn = document.getElementById('confirm-yes');
        this.confirmNoBtn = document.getElementById('confirm-no');
        
        // 按钮
        this.addGoalBtn = document.getElementById('add-goal-btn');
        this.closeModalBtn = document.getElementById('close-modal');
        this.cancelBtn = document.getElementById('cancel-btn');
        
        // 存钱模态框元素
        this.moneyInputModal = document.getElementById('money-input-modal');
        this.moneyModalTitle = document.getElementById('money-modal-title');
        this.moneyGoalIdInput = document.getElementById('money-goal-id');
        this.moneySubgoalIdInput = document.getElementById('money-subgoal-id');
        this.moneyAmountInput = document.getElementById('money-amount');
        this.saveMoneyBtn = document.getElementById('save-money-btn');
        this.cancelMoneyBtn = document.getElementById('cancel-money-btn');
        this.closeMoneyModalBtn = document.getElementById('close-money-modal');
    }

    // 初始化事件监听器
    initEventListeners() {
        // 添加新目标按钮点击事件
        this.addGoalBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.showAddGoalModal();
        });
        
        // 目标表单提交事件
        this.goalForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleGoalFormSubmit();
        });
        
        // 目标模态框关闭按钮点击事件
        this.closeModalBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.hideGoalModal();
        });
        this.cancelBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.hideGoalModal();
        });
        
        // 确认模态框按钮点击事件
        this.confirmNoBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.hideConfirmModal();
        });
        
        // 存钱模态框按钮事件
        this.closeMoneyModalBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.hideMoneyModal();
        });
        this.cancelMoneyBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.hideMoneyModal();
        });
        this.saveMoneyBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.handleMoneySave();
        });
        
        // 使用事件委托处理目标卡片上的按钮点击
        this.initGoalCardEventDelegation();
        
        // 初始化拖拽功能
        this.initDragAndDrop();
        
        // 统一处理点击模态窗口外部关闭
        this.initModalOutsideClickHandlers();
        
        // 移动设备拖拽功能已移除
    }

    // 使用事件委托处理目标卡片上的按钮点击
    initGoalCardEventDelegation() {
        // 使用事件委托处理目标卡片上的按钮点击
        this.goalsContainer.addEventListener('click', (e) => {
            const goalCard = e.target.closest('.goal-card');
            if (!goalCard) return;
            
            const goalId = parseInt(goalCard.getAttribute('data-goal-id'));
            if (!goalId) return;
            
            // 添加资金按钮
            if (e.target.matches('.add-money-btn') || e.target.closest('.add-money-btn')) {
                this.showAddMoneyModal(goalId);
                return;
            }

            // 减少资金按钮 (仅限父目标)
            if (e.target.matches('.subtract-money-btn') || e.target.closest('.subtract-money-btn')) {
                this.showSubtractMoneyModal(goalId);
                return;
            }
            
            // 编辑目标按钮
            if (e.target.matches('.edit-goal-btn') || e.target.closest('.edit-goal-btn')) {
                this.showEditGoalModal(goalId);
                return;
            }
            
            // 删除目标按钮
            if (e.target.matches('.delete-goal-btn') || e.target.closest('.delete-goal-btn')) {
                this.showDeleteGoalConfirmation(goalId);
                return;
            }
            
            // 折叠/展开按钮
            if (e.target.matches('.collapse-btn') || e.target.closest('.collapse-btn')) {
                this.toggleGoalExpanded(goalId);
                return;
            }
            
            // 添加子目标按钮
            if (e.target.matches('.add-subgoal-btn') || e.target.closest('.add-subgoal-btn')) {
                const targetGoalId = parseInt(e.target.closest('.add-subgoal-btn').getAttribute('data-goal-id'));
                if (targetGoalId) {
                    this.showAddSubgoalModal(targetGoalId);
                }
                return;
            }
            
            // 子目标按钮
            const subgoalItem = e.target.closest('.subgoal-item');
            if (subgoalItem) {
                const subgoalId = parseInt(subgoalItem.getAttribute('data-subgoal-id'));
                if (!subgoalId) return;
                
                // 添加资金到子目标
                if (e.target.matches('.add-money-subgoal-btn') || e.target.closest('.add-money-subgoal-btn')) {
                    this.showAddSubgoalMoneyModal(goalId, subgoalId);
                    return;
                }
                
                // 编辑子目标
                if (e.target.matches('.edit-subgoal-btn') || e.target.closest('.edit-subgoal-btn')) {
                    this.showEditSubgoalModal(goalId, subgoalId);
                    return;
                }
                
                // 删除子目标
                if (e.target.matches('.delete-subgoal-btn') || e.target.closest('.delete-subgoal-btn')) {
                    this.showDeleteSubgoalConfirmation(goalId, subgoalId);
                    return;
                }
            }
        });
    }

    // 统一处理模态窗口外部点击
    initModalOutsideClickHandlers() {
        // 定义模态窗口映射
        const modalMap = [
            { modal: this.goalModal, hideMethod: 'hideGoalModal' },
            { modal: this.confirmModal, hideMethod: 'hideConfirmModal' },
            { modal: this.moneyInputModal, hideMethod: 'hideMoneyModal' }
        ];
        
        // 点击事件处理
        const handleOutsideClick = (e) => {
            modalMap.forEach(item => {
                if (item.modal && item.modal.style.display === 'block' && e.target === item.modal) {
                    e.preventDefault();
                    this[item.hideMethod]();
                }
            });
        };
        
        // 添加点击事件监听
        document.addEventListener('click', handleOutsideClick);
    }

    // 渲染所有目标
    renderGoals() {
        const goals = this.goalTracker.getAllGoals();
        
        // 如果没有目标，显示空状态
        if (goals.length === 0) {
            this.emptyState.classList.remove('hidden');
            this.goalsContainer.innerHTML = '';
            this.goalsContainer.appendChild(this.emptyState);
            return;
        }
        
        // 隐藏空状态
        this.emptyState.classList.add('hidden');
        
        // 清空目标容器
        this.goalsContainer.innerHTML = '';
        
        // 渲染每个目标
        goals.forEach(goal => {
            const goalCard = this.createGoalCard(goal);
            this.goalsContainer.appendChild(goalCard);
        });
    }

    // 创建目标卡片
    createGoalCard(goal) {
        // 使用模板创建目标卡片
        const template = document.getElementById('goal-template');
        const goalCard = document.importNode(template.content, true).firstElementChild;
        
        // 设置ID和数据属性
        goalCard.setAttribute('data-goal-id', goal.id);
        goalCard.classList.add('draggable-goal');
        
        // 填充基本信息
        const nameElement = goalCard.querySelector('.goal-name-text');
        nameElement.textContent = goal.name;
        
        // 处理备注信息
        const notesInline = goalCard.querySelector('.goal-notes-inline');
        const notesContent = goalCard.querySelector('.goal-notes-content');
        
        if (goal.notes && goal.notes.trim() !== '') {
            notesContent.textContent = goal.notes;
            notesInline.style.display = 'inline-flex';
            // 确保备注在标题同一行
            const nameContainer = goalCard.querySelector('.goal-name-container');
            nameContainer.style.display = 'flex';
            nameContainer.style.flexWrap = 'nowrap';
            nameContainer.style.overflow = 'hidden';
        } else {
            notesInline.style.display = 'none';
        }
        
        // 设置截止日期
        const deadlineElement = goalCard.querySelector('.goal-deadline');
        if (goal.deadline) {
            const formattedDeadline = new Date(goal.deadline).toLocaleDateString('zh-CN');
            deadlineElement.textContent = formattedDeadline;
        } else {
            deadlineElement.textContent = '未设置';
        }
        
        // 设置目标金额
        const targetAmountElement = goalCard.querySelector('.goal-target-amount');
        targetAmountElement.textContent = `¥${goal.targetAmount.toLocaleString()}`;
        
        // 计算和显示进度
        const currentAmount = parseFloat(goal.currentAmount) || 0;
        const targetAmount = parseFloat(goal.targetAmount) || 1; // 避免除以零
        const progressPercentage = Math.min(Math.round((currentAmount / targetAmount) * 100), 100);
        
        // 设置金额进度文本
        const currentAmountElement = goalCard.querySelector('.current-amount');
        currentAmountElement.textContent = `¥${currentAmount.toLocaleString()}`;
        
        const targetAmountProgressElement = goalCard.querySelector('.target-amount');
        targetAmountProgressElement.textContent = `¥${targetAmount.toLocaleString()}`;
        
        const progressPercentageElement = goalCard.querySelector('.progress-percentage');
        progressPercentageElement.textContent = `${progressPercentage}%`;
        
        // 设置进度条
        const progressBar = goalCard.querySelector('.progress-bar');
        progressBar.style.width = `${progressPercentage}%`;
        
        // 处理时间进度
        if (goal.deadline) {
            const timeProgressContainer = goalCard.querySelector('.time-progress-container');
            const timeProgress = this.calculateTimeProgress(goal.deadline, goal.createdAt);
            
            const timeRemainingElement = goalCard.querySelector('.time-remaining');
            const timeProgressBar = goalCard.querySelector('.time-progress-bar');
            
            if (timeProgress.percentageComplete > 100) {
                timeRemainingElement.textContent = `已超期 ${timeProgress.daysOverdue} 天`;
                timeRemainingElement.style.color = 'var(--danger-color)';
                timeProgressBar.style.width = '100%';
                timeProgressBar.classList.add('time-expired');
            } else {
                const timePercentageText = `(${Math.round(timeProgress.percentageComplete)}%)`;
                timeRemainingElement.textContent = `剩余 ${timeProgress.daysRemaining} 天 ${timePercentageText}`;
                timeProgressBar.style.width = `${timeProgress.percentageComplete}%`;
                
                if (timeProgress.percentageComplete > 75) {
                    timeProgressBar.classList.add('time-warning');
                }
            }
        } else {
            // 如果没有截止日期，隐藏时间进度条
            const timeProgressContainer = goalCard.querySelector('.time-progress-container');
            timeProgressContainer.style.display = 'none';
        }
        
        // 渲染子目标
        this.renderSubgoalsContainer(goal, goalCard);
        
        // 控制折叠状态
        if (goal.isExpanded === false) {
            const subgoalsContainer = goalCard.querySelector('.subgoals-container');
            const addSubgoalContainer = goalCard.querySelector('.add-subgoal-container');
            subgoalsContainer.style.display = 'none';
            addSubgoalContainer.style.display = 'none';
            goalCard.querySelector('.collapse-btn i').classList.replace('fa-chevron-up', 'fa-chevron-down');
        }

        // 确保减少金额按钮在正确的位置（在增加金额按钮后面）
        const goalActions = goalCard.querySelector('.goal-actions');
        const addMoneyBtn = goalActions.querySelector('.add-money-btn');
        
        // 检查是否已存在减少金额按钮
        if (!goalActions.querySelector('.subtract-money-btn')) {
            const subtractMoneyBtn = document.createElement('button');
            subtractMoneyBtn.className = 'action-btn subtract-money-btn';
            subtractMoneyBtn.title = '减少资金';
            subtractMoneyBtn.innerHTML = '<i class="fas fa-minus-circle"></i>';
            
            // 插入在增加金额按钮后面
            if (addMoneyBtn && addMoneyBtn.nextSibling) {
                goalActions.insertBefore(subtractMoneyBtn, addMoneyBtn.nextSibling);
            } else if (addMoneyBtn) {
                goalActions.appendChild(subtractMoneyBtn);
            }
        }
        
        return goalCard;
    }

    // 计算时间进度
    calculateTimeProgress(deadline, createdAt) {
        if (!deadline) {
            return {
                percentageComplete: 0,
                text: '无截止日期',
                daysRemaining: 0,
                daysOverdue: 0,
                class: ''
            };
        }
        
        const now = new Date(getBeiJingTime());
        const deadlineDate = new Date(deadline);
        const createdDate = new Date(createdAt);
        let timeProgressPercent = 0;
        let timeProgressText = '';
        let remainingDays = 0;
        let daysOverdue = 0;
        
        if (deadlineDate > now) {
            // 计算总时间跨度（从创建到截止日期）
            const totalTimeSpan = deadlineDate.getTime() - createdDate.getTime();
            // 计算已经过去的时间
            const elapsedTime = now.getTime() - createdDate.getTime();
            // 计算进度百分比
            timeProgressPercent = Math.min(Math.round((elapsedTime / totalTimeSpan) * 100), 100);
            
            // 计算剩余天数
            remainingDays = Math.ceil((deadlineDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
            timeProgressText = `剩余${remainingDays}天`;
        } else {
            // 如果已过截止日期
            timeProgressPercent = 100;
            timeProgressText = '已过期';
            // 计算超期天数
            daysOverdue = Math.ceil((now.getTime() - deadlineDate.getTime()) / (1000 * 60 * 60 * 24));
        }
        
        // 时间进度条的颜色类
        const timeProgressClass = timeProgressPercent >= 100 ? 'time-expired' : 
                                 timeProgressPercent >= 80 ? 'time-warning' : '';
        
        return {
            percentageComplete: timeProgressPercent,
            text: timeProgressText,
            daysRemaining: remainingDays,
            daysOverdue: daysOverdue,
            class: timeProgressClass
        };
    }

    // 渲染时间进度条
    renderTimeProgressBar(timeProgress) {
        if (timeProgress.text === '无截止日期') {
            return '';
        }
        
        return `
        <div class="progress-text time-progress-text">
            <span>时间进度:</span>
            <span class="progress-time">${timeProgress.text} (${timeProgress.percent}%)</span>
        </div>
        <div class="progress-bar-bg">
            <div class="progress-bar time-progress-bar ${timeProgress.class}" style="width: ${timeProgress.percent}%"></div>
        </div>`;
    }

    // 渲染子目标容器
    renderSubgoalsContainer(goal, goalCard) {
        const subgoalsContainer = goalCard.querySelector('.subgoals-container');
        const addSubgoalContainer = goalCard.querySelector('.add-subgoal-container');
        
        // 清空容器，防止重复添加
        subgoalsContainer.innerHTML = '';
        
        // 如果有子目标，渲染每个子目标
        if (goal.subgoals && goal.subgoals.length > 0) {
            goal.subgoals.forEach(subgoal => {
                // 传递父目标的创建时间，而不是使用子目标自己的创建时间
                const subgoalItem = this.renderSubgoalItem(subgoal, goal.createdAt);
                subgoalItem.setAttribute('data-subgoal-id', subgoal.id);
                subgoalItem.setAttribute('data-parent-goal-id', goal.id);
                subgoalsContainer.appendChild(subgoalItem);
            });
        }
        
        // 设置"添加子目标"按钮的数据属性
        const addSubgoalBtn = addSubgoalContainer.querySelector('.add-subgoal-btn');
        addSubgoalBtn.setAttribute('data-goal-id', goal.id);
    }

    // 渲染单个子目标项
    renderSubgoalItem(subgoal, parentCreatedAt) {
        // 使用模板创建子目标卡片
        const template = document.getElementById('subgoal-template');
        const subgoalItem = document.importNode(template.content, true).firstElementChild;
        
        // 设置子目标名称
        const nameElement = subgoalItem.querySelector('.subgoal-name');
        nameElement.textContent = subgoal.name;
        
        // 处理备注信息
        const notesInline = subgoalItem.querySelector('.subgoal-notes-inline');
        const notesContent = subgoalItem.querySelector('.subgoal-notes-content');
        
        if (subgoal.notes && subgoal.notes.trim() !== '') {
            notesContent.textContent = subgoal.notes;
            notesInline.style.display = 'inline-flex';
            // 确保备注在标题同一行
            const nameContainer = subgoalItem.querySelector('.subgoal-name-container');
            nameContainer.style.display = 'flex';
            nameContainer.style.flexWrap = 'nowrap';
            nameContainer.style.overflow = 'hidden';
        } else {
            notesInline.style.display = 'none';
        }
        
        // 计算进度
        const currentAmount = parseFloat(subgoal.currentAmount) || 0;
        const targetAmount = parseFloat(subgoal.targetAmount) || 1; // 避免除以零
        const progressPercentage = Math.min(Math.round((currentAmount / targetAmount) * 100), 100);
        
        // 设置进度条
        const progressBar = subgoalItem.querySelector('.subgoal-progress-bar');
        progressBar.style.width = `${progressPercentage}%`;
        
        // 手动应用闪光效果
        progressBar.style.animation = 'goldGlowing 1.5s infinite ease-in-out';
        progressBar.style.backgroundImage = 'linear-gradient(45deg, rgba(255,255,255,0.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.15) 50%, rgba(255,255,255,0.15) 75%, transparent 75%, transparent)';
        progressBar.style.backgroundSize = '1rem 1rem';
        progressBar.style.boxShadow = '0 0 10px #FFDF00';
        
        // 设置进度文本
        const progressText = subgoalItem.querySelector('.subgoal-progress-text');
        progressText.textContent = `¥${currentAmount.toLocaleString()} / ¥${targetAmount.toLocaleString()} (${progressPercentage}%)`;
        
        // 设置截止日期
        const deadlineElement = subgoalItem.querySelector('.subgoal-deadline-text');
        if (subgoal.deadline) {
            const formattedDeadline = new Date(subgoal.deadline).toLocaleDateString('zh-CN');
            deadlineElement.textContent = formattedDeadline;
            
            // 处理时间进度 - 使用父目标的创建时间
            const timeProgressContainer = subgoalItem.querySelector('.time-progress-container');
            // 使用父目标的创建时间，而不是子目标的创建时间
            const createdAt = parentCreatedAt || subgoal.createdAt;
            const timeProgress = this.calculateTimeProgress(subgoal.deadline, createdAt);
            
            const timeRemainingElement = subgoalItem.querySelector('.subgoal-time-remaining');
            const timeProgressBar = subgoalItem.querySelector('.time-progress-bar.subgoal-time-progress-bar');
            
            if (timeProgress.percentageComplete > 100) {
                timeRemainingElement.textContent = `已超期 ${timeProgress.daysOverdue} 天`;
                timeRemainingElement.style.color = 'var(--danger-color)';
                timeProgressBar.style.width = '100%';
                timeProgressBar.classList.add('time-expired');
            } else {
                const timePercentageText = `(${Math.round(timeProgress.percentageComplete)}%)`;
                timeRemainingElement.textContent = `剩余 ${timeProgress.daysRemaining} 天 ${timePercentageText}`;
                timeProgressBar.style.width = `${timeProgress.percentageComplete}%`;
                
                if (timeProgress.percentageComplete > 75) {
                    timeProgressBar.classList.add('time-warning');
                }
            }
            
            // 手动应用红色闪光效果
            timeProgressBar.style.animation = 'redGlowing 1.5s infinite ease-in-out';
            timeProgressBar.style.backgroundImage = 'linear-gradient(45deg, rgba(255,255,255,0.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.15) 50%, rgba(255,255,255,0.15) 75%, transparent 75%, transparent)';
            timeProgressBar.style.backgroundSize = '1rem 1rem';
            timeProgressBar.style.boxShadow = '0 0 10px #FF0000';
        } else {
            deadlineElement.textContent = '未设置';
            // 隐藏时间进度条
            const timeProgressContainer = subgoalItem.querySelector('.time-progress-container');
            timeProgressContainer.style.display = 'none';
        }
        
        return subgoalItem;
    }

    // 更新仪表盘
    updateDashboard() {
        const stats = this.goalTracker.getStats();
        
        this.totalGoalsEl.textContent = stats.totalGoals;
        this.inProgressEl.textContent = stats.inProgressGoals;
        this.completedEl.textContent = stats.completedGoals;
        this.overallProgressEl.textContent = `${stats.overallProgress}%`;
    }

    // 显示添加目标模态框
    showAddGoalModal() {
        this.goalIdInput.value = '';
        this.parentGoalIdInput.value = '';
        this.isSubgoalInput.value = 'false';
        this.goalNameInput.value = '';
        this.goalAmountInput.value = '';
        this.currentAmountInput.value = '0';
        this.goalDeadlineInput.value = '';
        this.goalNotesInput.value = '';
        
        // 隐藏当前金额输入框
        this.currentAmountInput.closest('.form-group').style.display = 'none';
        
        this.modalTitle.textContent = '添加新目标';
        this.goalModal.style.display = 'block';
        document.body.style.overflow = 'hidden'; // 防止背景滚动
        
        // iOS相关代码已移除
    }

    // 显示编辑目标模态框
    showEditGoalModal(goalId) {
        const goal = this.goalTracker.getGoal(goalId);
        if (!goal) return;
        
        this.modalTitle.textContent = '编辑目标';
        this.goalIdInput.value = goal.id;
        this.parentGoalIdInput.value = '';
        this.isSubgoalInput.value = 'false';
        this.goalNameInput.value = goal.name;
        this.goalAmountInput.value = goal.targetAmount;
        this.currentAmountInput.value = goal.currentAmount;
        this.goalDeadlineInput.value = goal.deadline || '';
        this.goalNotesInput.value = goal.notes || '';
        
        // 隐藏当前金额输入框
        this.currentAmountInput.closest('.form-group').style.display = 'none';
        
        this.goalModal.style.display = 'block';
        document.body.style.overflow = 'hidden'; // 防止背景滚动
        
        // iOS相关代码已移除
    }

    // 显示添加子目标模态框
    showAddSubgoalModal(goalId) {
        const goal = this.goalTracker.getGoal(goalId);
        if (!goal) return;
        
        this.modalTitle.textContent = `为"${goal.name}"添加子目标`;
        this.goalIdInput.value = '';
        this.parentGoalIdInput.value = goal.id;
        this.isSubgoalInput.value = 'true';
        this.goalNameInput.value = '';
        this.goalAmountInput.value = '';
        this.currentAmountInput.value = '0';
        this.goalDeadlineInput.value = '';
        this.goalNotesInput.value = '';
        
        // 隐藏当前金额输入框
        this.currentAmountInput.closest('.form-group').style.display = 'none';
        
        this.goalModal.style.display = 'block';
        document.body.style.overflow = 'hidden'; // 防止背景滚动
        
        // iOS相关代码已移除
    }

    // 显示编辑子目标模态框
    showEditSubgoalModal(goalId, subgoalId) {
        const goal = this.goalTracker.getGoal(goalId);
        if (!goal) return;
        
        const subgoal = goal.subgoals.find(sub => sub.id === subgoalId);
        if (!subgoal) return;
        
        this.modalTitle.textContent = `编辑"${goal.name}"的子目标`;
        this.goalIdInput.value = subgoalId;
        this.parentGoalIdInput.value = goal.id;
        this.isSubgoalInput.value = 'true';
        this.goalNameInput.value = subgoal.name;
        this.goalAmountInput.value = subgoal.targetAmount;
        this.currentAmountInput.value = subgoal.currentAmount;
        this.goalDeadlineInput.value = subgoal.deadline || '';
        this.goalNotesInput.value = subgoal.notes || '';
        
        // 隐藏当前金额输入框
        this.currentAmountInput.closest('.form-group').style.display = 'none';
        
        this.goalModal.style.display = 'block';
        document.body.style.overflow = 'hidden'; // 防止背景滚动
        
        // iOS相关代码已移除
    }

    // 通用的隐藏模态框函数
    hideModal(modalType) {
        let modal, inputFields;
        
        // 根据类型确定要隐藏的模态框和需要清理的输入字段
        switch(modalType) {
            case 'goal':
                modal = this.goalModal;
                inputFields = () => {
                    this.goalIdInput.value = '';
                    this.parentGoalIdInput.value = '';
                    this.isSubgoalInput.value = 'false';
                    this.goalNameInput.value = '';
                    this.goalAmountInput.value = '';
                    this.currentAmountInput.value = '0';
                    this.goalDeadlineInput.value = '';
                    this.goalNotesInput.value = '';
                };
                break;
            case 'confirm':
                modal = this.confirmModal;
                inputFields = () => {
                    this.tempGoalId = null;
                    this.tempSubgoalId = null;
                    
                    // 移除确认按钮的事件处理器，防止重复触发
                    if (this.confirmYesBtn) {
                        this.confirmYesBtn.onclick = null;
                    }
                };
                break;
            case 'money':
                modal = this.moneyInputModal;
                inputFields = () => {
                    this.moneyGoalIdInput.value = '';
                    this.moneySubgoalIdInput.value = '';
                    this.moneyAmountInput.value = '';
                };
                break;
            default:
                return; // 如果类型无效，直接返回
        }
        
        if (!modal) return;
        
        // 隐藏模态框
        modal.style.display = 'none';
        document.body.style.overflow = '';
        
        // 使用setTimeout清除表单数据，确保模态框完全关闭后执行
        setTimeout(inputFields, 100);
        
        // iOS相关代码已移除
    }

    // 以下是替换原有方法的包装函数
    hideGoalModal() {
        this.hideModal('goal');
    }

    hideConfirmModal() {
        this.hideModal('confirm');
    }

    hideMoneyModal() {
        this.hideModal('money');
    }

    // 处理目标表单提交
    handleGoalFormSubmit() {
        const goalData = {
            name: this.goalNameInput.value,
            targetAmount: this.goalAmountInput.value,
            currentAmount: 0, // 始终为0，不再使用表单输入
            deadline: this.goalDeadlineInput.value || null,
            notes: this.goalNotesInput.value
        };
        
        const isSubgoal = this.isSubgoalInput.value === 'true';
        
        if (isSubgoal) {
            // 子目标处理
            const goalId = parseInt(this.parentGoalIdInput.value);
            const subgoalId = this.goalIdInput.value ? parseInt(this.goalIdInput.value) : null;
            
            if (subgoalId) {
                // 更新现有子目标
                // 但不允许通过表单直接修改当前金额
                const subgoal = this.goalTracker.getGoal(goalId).subgoals.find(s => s.id === subgoalId);
                if (subgoal) {
                    goalData.currentAmount = subgoal.currentAmount; // 保持原有金额不变
                }
                this.goalTracker.updateSubgoal(goalId, subgoalId, goalData);
            } else {
                // 添加新子目标
                this.goalTracker.addSubgoal(goalId, goalData);
            }
        } else {
            // 主目标处理
            const goalId = this.goalIdInput.value ? parseInt(this.goalIdInput.value) : null;
            
            if (goalId) {
                // 更新现有目标
                // 但不允许通过表单直接修改当前金额
                const goal = this.goalTracker.getGoal(goalId);
                if (goal) {
                    goalData.currentAmount = goal.currentAmount; // 保持原有金额不变
                }
                this.goalTracker.updateGoal(goalId, goalData);
            } else {
                // 添加新目标
                this.goalTracker.addGoal(goalData);
            }
        }
        
        this.hideGoalModal();
        this.renderGoals();
        this.updateDashboard();
    }

    // 显示删除目标确认框
    showDeleteGoalConfirmation(goalId) {
        this.tempGoalId = goalId;
        this.tempSubgoalId = null;
        this.confirmMessage.textContent = '确定要删除这个目标吗？所有相关的子目标也将被删除。';
        
        // 设置确认按钮事件
        this.confirmYesBtn.onclick = () => {
            const success = this.goalTracker.deleteGoal(this.tempGoalId);
            if (success) {
                this.renderGoals();
                this.updateDashboard();
                this.hideConfirmModal();
            }
        };
        
        this.confirmModal.style.display = 'block';
        document.body.style.overflow = 'hidden';
        // iOS相关代码已移除
    }

    // 显示删除子目标确认框
    showDeleteSubgoalConfirmation(goalId, subgoalId) {
        this.tempGoalId = goalId;
        this.tempSubgoalId = subgoalId;
        this.confirmMessage.textContent = '确定要删除这个子目标吗？';
        
        // 设置确认按钮事件
        this.confirmYesBtn.onclick = () => {
            const success = this.goalTracker.deleteSubgoal(this.tempGoalId, this.tempSubgoalId);
            if (success) {
                this.renderGoals();
                this.updateDashboard();
                this.hideConfirmModal();
            }
        };
        
        this.confirmModal.style.display = 'block';
        document.body.style.overflow = 'hidden';
        // iOS相关代码已移除
    }

    // 切换目标展开/折叠状态
    toggleGoalExpanded(goalId) {
        this.goalTracker.toggleGoalExpanded(goalId);
        
        // 获取目标卡片
        const goalCard = document.querySelector(`.goal-card[data-goal-id="${goalId}"]`);
        if (!goalCard) return;
        
        // 获取目标信息
        const goal = this.goalTracker.getGoal(goalId);
        if (!goal) return;
        
        // 获取子目标容器和添加子目标按钮容器
        const subgoalsContainer = goalCard.querySelector('.subgoals-container');
        const addSubgoalContainer = goalCard.querySelector('.add-subgoal-container');
        const collapseIcon = goalCard.querySelector('.collapse-btn i');
        
        // 根据目标的展开状态切换显示
        if (goal.isExpanded) {
            subgoalsContainer.style.display = 'block';
            addSubgoalContainer.style.display = 'flex';
            collapseIcon.classList.replace('fa-chevron-down', 'fa-chevron-up');
        } else {
            subgoalsContainer.style.display = 'none';
            addSubgoalContainer.style.display = 'none';
            collapseIcon.classList.replace('fa-chevron-up', 'fa-chevron-down');
        }
    }

    // 显示添加金额模态框
    showAddMoneyModal(goalId) {
        this.moneyGoalIdInput.value = goalId;
        this.moneySubgoalIdInput.value = '';
        this.moneyModalTitle.textContent = '添加资金';
        this.moneyAmountInput.value = '';
        this.moneyInputModal.style.display = 'block';
        document.body.style.overflow = 'hidden';
        // iOS相关代码已移除
        
        // 设置当前模态框的操作类型为"add"
        this.moneyInputModal.dataset.operationType = 'add';
        
        setTimeout(() => this.moneyAmountInput.focus(), 100);
    }
    
    // 显示减少金额模态框
    showSubtractMoneyModal(goalId) {
        this.moneyGoalIdInput.value = goalId;
        this.moneySubgoalIdInput.value = '';
        this.moneyModalTitle.textContent = '减少资金';
        this.moneyAmountInput.value = '';
        this.moneyInputModal.style.display = 'block';
        document.body.style.overflow = 'hidden';
        // iOS相关代码已移除
        
        // 设置当前模态框的操作类型为"subtract"
        this.moneyInputModal.dataset.operationType = 'subtract';
        
        setTimeout(() => this.moneyAmountInput.focus(), 100);
    }
    
    // 显示添加金额到子目标的模态框
    showAddSubgoalMoneyModal(goalId, subgoalId) {
        this.moneyGoalIdInput.value = goalId;
        this.moneySubgoalIdInput.value = subgoalId;
        this.moneyModalTitle.textContent = '添加资金到子目标';
        this.moneyAmountInput.value = '';
        this.moneyInputModal.style.display = 'block';
        document.body.style.overflow = 'hidden';
        // iOS相关代码已移除
        
        // 设置当前模态框的操作类型为"add"
        this.moneyInputModal.dataset.operationType = 'add';
        
        setTimeout(() => this.moneyAmountInput.focus(), 100);
    }
    
    // 处理金额保存
    handleMoneySave() {
        const amount = parseFloat(this.moneyAmountInput.value);
        if (isNaN(amount) || amount <= 0) {
            alert('请输入有效的金额');
            return;
        }
        
        const goalId = parseInt(this.moneyGoalIdInput.value);
        const subgoalId = this.moneySubgoalIdInput.value ? parseInt(this.moneySubgoalIdInput.value) : null;
        const operationType = this.moneyInputModal.dataset.operationType || 'add';
        
        if (operationType === 'subtract') {
            // 减少金额（仅限父目标）
            if (!subgoalId) {
                this.goalTracker.subtractMoneyFromGoal(goalId, amount);
            }
        } else {
            // 添加金额
            if (subgoalId) {
                // 添加金额到子目标
                this.goalTracker.addMoneyToSubgoal(goalId, subgoalId, amount);
            } else {
                // 添加金额到主目标
                this.goalTracker.addMoneyToGoal(goalId, amount);
            }
        }
        
        // 重置操作类型
        this.moneyInputModal.dataset.operationType = 'add';
        
        this.hideMoneyModal();
        this.renderGoals();
        this.updateDashboard();
    }

    // iOS相关方法已移除

    // 初始化拖拽功能
    initDragAndDrop() {
        this.goalsContainer.addEventListener('dragstart', (e) => {
            if (e.target.classList.contains('draggable-goal')) {
                e.target.classList.add('dragging');
                // 在数据传输对象中存储被拖拽的目标ID
                e.dataTransfer.setData('text/plain', e.target.dataset.goalId);
                e.dataTransfer.effectAllowed = 'move';
                
                // 设置拖拽图像（可选）
                if (e.target.querySelector('.goal-name')) {
                    const dragImage = e.target.querySelector('.goal-name').cloneNode(true);
                    dragImage.style.padding = '10px';
                    dragImage.style.background = 'white';
                    dragImage.style.borderRadius = '4px';
                    dragImage.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
                    document.body.appendChild(dragImage);
                    e.dataTransfer.setDragImage(dragImage, 0, 0);
                    setTimeout(() => document.body.removeChild(dragImage), 0);
                }
            }
        });

        this.goalsContainer.addEventListener('dragend', (e) => {
            if (e.target.classList.contains('draggable-goal')) {
                e.target.classList.remove('dragging');
            }
        });

        this.goalsContainer.addEventListener('dragover', (e) => {
            e.preventDefault(); // 允许放置
            const draggable = document.querySelector('.dragging');
            if (!draggable) return;
            
            // 计算最近的卡片和位置
            const afterElement = this.getDragAfterElement(e.clientY);
            
            if (afterElement == null) {
                this.goalsContainer.appendChild(draggable);
            } else {
                this.goalsContainer.insertBefore(draggable, afterElement);
            }
        });

        this.goalsContainer.addEventListener('drop', (e) => {
            e.preventDefault();
            
            const goalId = e.dataTransfer.getData('text/plain');
            const allGoalCards = [...this.goalsContainer.querySelectorAll('.draggable-goal')];
            
            // 找到拖动的元素索引
            const fromIndex = this.goalTracker.goals.findIndex(g => g.id === parseInt(goalId));
            
            // 找到目标位置索引
            const draggedElement = document.querySelector(`[data-goal-id="${goalId}"]`);
            const toIndex = allGoalCards.indexOf(draggedElement);
            
            // 如果位置发生变化，则更新目标顺序
            if (fromIndex !== toIndex) {
                this.goalTracker.reorderGoals(fromIndex, toIndex);
                // 不需要重新渲染全部，因为DOM已经更新了
            }
        });
    }
    
    // 辅助方法：根据鼠标位置找到应该放置的元素
    getDragAfterElement(y) {
        const draggableElements = [...this.goalsContainer.querySelectorAll('.draggable-goal:not(.dragging)')];
        
        return draggableElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;
            
            // 找到最近的元素
            if (offset < 0 && offset > closest.offset) {
                return { offset: offset, element: child };
            } else {
                return closest;
            }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    }
}

// 在DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    console.log('应用初始化中...');
    
    // 移动设备兼容性代码已移除
    
    try {
        // 初始化应用
        const goalTracker = new GoalTracker();
        const uiController = new UIController(goalTracker);
        
        // 加载并渲染目标
        uiController.renderGoals();
        uiController.updateDashboard();
        
        console.log('应用初始化成功');
    } catch (error) {
        console.error('应用初始化失败:', error);
        // 显示错误消息给用户
        alert(`初始化失败: ${error.message}\n请尝试刷新页面或清除浏览器缓存。`);
    }
});
