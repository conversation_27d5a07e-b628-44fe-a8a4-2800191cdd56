<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>人脉管理 - ST计划</title>
    <!-- 网站图标 -->
    <link rel="icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <link rel="shortcut icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <!-- 样式文件 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    <link rel="stylesheet" type="text/css" href="css/contacts.css">
</head>

<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <h4 class="navbar-text text-white mb-0">人脉管理</h4>
            <a class="navbar-brand ms-auto" href="index.html">
                <i class="fas fa-home me-2"></i>返回主页
            </a>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container-fluid mt-3">
        <!-- 工具栏 -->
        <div class="toolbar mb-3">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="searchInput" placeholder="搜索人脉姓名、公司、标签...">
                        <button class="btn btn-outline-secondary" onclick="clearSearch()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="btn-group me-2" role="group">
                        <input type="radio" class="btn-check" name="viewMode" id="listView" checked>
                        <label class="btn btn-outline-primary btn-sm" for="listView">
                            <i class="fas fa-list"></i> 列表
                        </label>
                        <input type="radio" class="btn-check" name="viewMode" id="networkView">
                        <label class="btn btn-outline-primary btn-sm" for="networkView">
                            <i class="fas fa-project-diagram"></i> 关系网
                        </label>
                        <input type="radio" class="btn-check" name="viewMode" id="treeView">
                        <label class="btn btn-outline-primary btn-sm" for="treeView">
                            <i class="fas fa-sitemap"></i> 关系树
                        </label>
                    </div>
                    <button class="btn btn-success btn-sm" onclick="showAddContactModal()">
                        <i class="fas fa-plus"></i> 添加人脉
                    </button>
                </div>
            </div>
        </div>

        <!-- 筛选栏 -->
        <div class="filter-bar mb-3">
            <div class="row">
                <div class="col-md-4">
                    <select class="form-select form-select-sm" id="importanceFilter">
                        <option value="">所有重要程度</option>
                        <option value="5">⭐⭐⭐⭐⭐ 非常重要</option>
                        <option value="4">⭐⭐⭐⭐ 重要</option>
                        <option value="3">⭐⭐⭐ 一般</option>
                        <option value="2">⭐⭐ 较低</option>
                        <option value="1">⭐ 很低</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <select class="form-select form-select-sm" id="tagFilter">
                        <option value="">所有标签</option>
                        <!-- 标签选项将动态生成 -->
                    </select>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-outline-secondary btn-sm w-100" onclick="resetFilters()">
                        <i class="fas fa-undo"></i> 重置筛选
                    </button>
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-bar mb-3">
            <div class="row text-center">
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number" id="totalContacts">0</div>
                        <div class="stat-label">总人脉</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number" id="recentContacts">0</div>
                        <div class="stat-label">本月新增</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number" id="importantContacts">0</div>
                        <div class="stat-label">重要人脉</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number" id="connectionCount">0</div>
                        <div class="stat-label">关系连接</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 列表视图 -->
            <div id="listViewContainer" class="view-container">
                <div class="row" id="contactsList">
                    <!-- 人脉卡片将在这里动态生成 -->
                </div>
            </div>

            <!-- 关系网络视图 -->
            <div id="networkViewContainer" class="view-container" style="display: none;">
                <div class="network-controls mb-3">
                    <button class="btn btn-outline-primary btn-sm" onclick="resetNetworkView()">
                        <i class="fas fa-expand-arrows-alt"></i> 重置视图
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="centerNetwork()">
                        <i class="fas fa-crosshairs"></i> 居中显示
                    </button>
                    <div class="btn-group ms-2" role="group">
                        <button class="btn btn-outline-secondary btn-sm" onclick="zoomIn()">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="zoomOut()">
                            <i class="fas fa-search-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="network-canvas-container">
                    <canvas id="networkCanvas" width="1200" height="600"></canvas>
                </div>
            </div>

            <!-- 关系树视图 -->
            <div id="treeViewContainer" class="view-container" style="display: none;">
                <div class="tree-controls mb-3">
                    <select class="form-select form-select-sm d-inline-block w-auto" id="treeCenterSelect">
                        <option value="">选择中心人脉</option>
                        <!-- 选项将动态生成 -->
                    </select>
                    <button class="btn btn-outline-primary btn-sm ms-2" onclick="expandAllTree()">
                        <i class="fas fa-expand"></i> 展开全部
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="collapseAllTree()">
                        <i class="fas fa-compress"></i> 收起全部
                    </button>
                </div>
                <div id="treeContainer" class="tree-container">
                    <!-- 关系树将在这里生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑人脉模态框 -->
    <div class="modal fade" id="contactModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="contactModalTitle">添加人脉</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="contactForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">姓名 *</label>
                                    <input type="text" class="form-control" id="contactName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">职位/职业</label>
                                    <input type="text" class="form-control" id="contactPosition">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">重要程度</label>
                                    <select class="form-select" id="contactImportance">
                                        <option value="1">⭐ 很低</option>
                                        <option value="2">⭐⭐ 较低</option>
                                        <option value="3" selected>⭐⭐⭐ 一般</option>
                                        <option value="4">⭐⭐⭐⭐ 重要</option>
                                        <option value="5">⭐⭐⭐⭐⭐ 非常重要</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">标签 (用逗号分隔)</label>
                                    <input type="text" class="form-control" id="contactTags" placeholder="工作,技术,朋友">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">关联人脉</label>
                            <div id="connectionsList">
                                <!-- 关联人脉选择将在这里生成 -->
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addConnection()">
                                <i class="fas fa-plus"></i> 添加关联
                            </button>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveContact()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.min.js"></script>
    <script src="js/contacts.js"></script>
</body>
</html>
