<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>参数比较</title>
    <!-- 添加网站图标 -->
    <link rel="icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <link rel="shortcut icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <!-- 内联PNG图标，用于不支持SVG的浏览器 -->
    <link rel="icon" href="data:image/png;base64,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" type="image/png">

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <!-- Mobile optimization CSS removed -->
    <style>
        :root {
            --primary-color: #2563eb;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #0891b2;
            --text-color: #1e293b;
            --text-muted: #64748b;
            --bg-color: #f8fafc;
            --card-bg: #ffffff;
            --border-color: #e2e8f0;
            --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.08);
            --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.12);
            --border-radius: 10px;
            --transition-speed: 0.2s;
        }

        body {
            background-color: var(--bg-color);
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            color: var(--text-color);
            line-height: 1.5;
        }

        .page-header {
            background-color: var(--primary-color);
            color: white;
            padding: 0.75rem 0;
            margin-bottom: 1rem;
            box-shadow: var(--shadow-medium);
        }

        .header-title {
            font-weight: 600;
            font-size: 1.5rem;
            margin-bottom: 0.25rem;
        }

        .header-description {
            font-size: 0.95rem;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .progress-card {
            background-color: var(--card-bg);
            border: 1px solid #e2e8f0;
            border-radius: var(--border-radius);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
            margin-bottom: 0.75rem;
            transition: all var(--transition-speed) ease;
            overflow: hidden;
        }

        .progress-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
            transform: translateY(-1px);
        }

        .progress-card .card-header {
            background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
            border-bottom: 1px solid #e2e8f0;
            padding: 0.75rem;
        }

        .progress-card .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.95rem;
            margin-bottom: 0.75rem;
        }

        .progress-card .progress-target {
            text-align: right;
            color: var(--text-muted);
        }

        .add-progress-form {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid var(--border-color);
            border-left: 4px solid var(--primary-color);
        }

        .filter-container {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 0.75rem;
            margin-bottom: 1rem;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-light);
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.2);
            outline: none;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            font-weight: 500;
            padding: 0.5rem 1rem;
        }

        .btn-primary:hover {
            background-color: #1d4ed8;
            border-color: #1d4ed8;
        }

        .btn-outline-primary {
            border-color: var(--primary-color);
            color: var(--primary-color);
            font-weight: 500;
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .btn-outline-danger {
            border-color: var(--danger-color);
            color: var(--danger-color);
            font-weight: 500;
        }

        .btn-outline-danger:hover {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
            color: white;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }
        
        /* 模态框样式 */
        .modal-content {
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-medium);
        }

        .modal-header {
            border-bottom: 1px solid var(--border-color);
            background-color: #f8fafc;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            padding: 1rem 1.25rem;
        }

        .modal-footer {
            border-top: 1px solid var(--border-color);
            background-color: #f8fafc;
            border-radius: 0 0 var(--border-radius) var(--border-radius);
            padding: 1rem 1.25rem;
        }

        .modal-title {
            font-weight: 600;
            font-size: 1.125rem;
        }

        /* 下拉菜单样式 */
        .dropdown-menu {
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-medium);
        }

        .dropdown-item:hover {
            background-color: #f3f4f6;
        }

        .dropdown-item.active, .dropdown-item:active {
            background-color: var(--primary-color);
            color: white;
        }
        
        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 2rem 1rem;
            margin: 1rem 0;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-light);
        }

        .empty-state i {
            font-size: 2.5rem;
            color: var(--text-muted);
            margin-bottom: 1rem;
        }

        .empty-state h5 {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 0.5rem;
            font-size: 1.125rem;
        }

        .empty-state p {
            color: var(--text-muted);
            max-width: 320px;
            margin: 0 auto 1rem;
            font-size: 0.95rem;
        }
        
        /* Toast通知样式 */
        .toast {
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-medium);
            background-color: var(--card-bg);
        }

        .toast-header {
            border-bottom: 1px solid var(--border-color);
            background-color: #f8fafc;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        /* 进度条颜色方案 - 优化配色 */
        .progress-bar-health { background: linear-gradient(135deg, #22c55e, #16a34a); }
        .progress-bar-fitness { background: linear-gradient(135deg, #3b82f6, #2563eb); }
        .progress-bar-finance { background: linear-gradient(135deg, #f59e0b, #ea580c); }
        .progress-bar-study { background: linear-gradient(135deg, #a855f7, #9333ea); }
        .progress-bar-work { background: linear-gradient(135deg, #06b6d4, #0891b2); }
        .progress-bar-default { background: linear-gradient(135deg, #6366f1, #4f46e5); }

        /* 快速编辑样式 */
        .quick-edit-input {
            display: none;
            width: 80px;
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            border: 1px solid var(--primary-color);
            border-radius: 4px;
            text-align: center;
        }

        .quick-edit-input:focus {
            outline: none;
            box-shadow: 0 0 0 0.15rem rgba(37, 99, 235, 0.2);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .page-header {
                padding: 0.5rem 0;
            }

            .header-title {
                font-size: 1.25rem;
            }

            .header-description {
                font-size: 0.875rem;
            }

            .progress-card {
                margin-bottom: 0.5rem;
            }

            .add-progress-form {
                padding: 0.75rem;
            }

            .filter-container {
                flex-direction: column;
                gap: 0.5rem;
                padding: 0.5rem;
            }

            .filter-container .d-flex {
                flex-direction: column;
                align-items: stretch !important;
            }

            .filter-container label {
                margin-bottom: 0.25rem;
            }

            .progress-info {
                flex-direction: column;
                align-items: stretch !important;
                gap: 0.25rem;
            }

            .progress-info > span:last-child {
                text-align: right !important;
            }
        }

        @media (max-width: 576px) {
            .container {
                padding-left: 0.5rem;
                padding-right: 0.5rem;
            }

            .card-header {
                padding: 0.5rem;
            }

            .card-body {
                padding: 0.5rem;
            }

            .stats-container {
                padding: 0.5rem;
                margin-bottom: 0.75rem;
            }
        }

        /* 整体容器优化 */
        .container {
            max-width: 1200px;
        }
        
        /* 卡片操作按钮样式 */
        .card-actions {
            opacity: 0;
            transition: opacity var(--transition-speed) ease;
        }

        .progress-card:hover .card-actions {
            opacity: 1;
        }

        .card-actions .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            border-radius: 4px;
        }

        /* 进度条样式增强 */
        .progress {
            height: 24px;
            background-color: #f1f5f9;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
        }

        .progress-bar {
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            transition: width 0.6s ease;
            border-radius: 12px;
        }

        /* 参数类型标识 */
        .param-type-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-weight: 500;
        }

        .param-type-health { background-color: #dcfce7; color: #166534; }
        .param-type-fitness { background-color: #dbeafe; color: #1e40af; }
        .param-type-finance { background-color: #fed7aa; color: #c2410c; }
        .param-type-study { background-color: #e9d5ff; color: #7c2d12; }
        .param-type-work { background-color: #cffafe; color: #155e75; }
        .param-type-default { background-color: #e0e7ff; color: #4338ca; }
        
        /* 统计信息样式 */
        .stats-container {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 0.75rem;
            margin-bottom: 1rem;
            box-shadow: var(--shadow-light);
        }

        .stat-item {
            text-align: center;
            padding: 0.5rem;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-muted);
            font-weight: 500;
        }

        /* 隐藏不需要的元素 */
        .theme-toggle,
        #themeSwitch,
        .search-container,
        #searchInput,
        input[type="search"],
        input[placeholder*="搜索"] {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="page-header">
        <div class="container position-relative">
            <a href="index.html" class="btn btn-light position-absolute" style="top: 10px; right: 15px; z-index: 1000;">
                <i class="fas fa-home me-1"></i>返回首页
            </a>
            <h1 class="header-title">
                <i class="fas fa-chart-line me-2"></i>参数比较
            </h1>
            <p class="header-description">
                跟踪和管理您的各项参数目标及进度
            </p>
        </div>
    </div>

    <div class="container" style="padding-top: 0;">
        <!-- 添加参数比较表单 -->
        <div id="addProgressForm" class="add-progress-form" style="display: none;">
            <div class="row g-3">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">
                            <i class="fas fa-plus-circle me-2 text-primary"></i>添加参数比较
                        </h5>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-magic me-1"></i>快速模板
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="applyTemplate('weight')">体重管理</a></li>
                                <li><a class="dropdown-item" href="#" onclick="applyTemplate('savings')">存款目标</a></li>
                                <li><a class="dropdown-item" href="#" onclick="applyTemplate('reading')">阅读计划</a></li>
                                <li><a class="dropdown-item" href="#" onclick="applyTemplate('exercise')">运动目标</a></li>
                                <li><a class="dropdown-item" href="#" onclick="applyTemplate('study')">学习时长</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <label for="progressName" class="form-label">参数名称</label>
                    <input type="text" class="form-control" id="progressName">
                </div>
                <div class="col-md-6">
                    <label for="progressCategory" class="form-label">分类</label>
                    <select class="form-select" id="progressCategory">
                        <!-- 分类选项将通过JS动态添加 -->
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="progressUserValue" class="form-label">当前值</label>
                    <input type="number" class="form-control" id="progressUserValue">
                </div>
                <div class="col-md-4">
                    <label for="progressTargetValue" class="form-label">目标值</label>
                    <input type="number" class="form-control" id="progressTargetValue">
                </div>
                <div class="col-md-4">
                    <label for="progressUnit" class="form-label">单位</label>
                    <input type="text" class="form-control" id="progressUnit">
                </div>
                <div class="col-12">
                    <label for="progressTargetName" class="form-label">目标名称（可选）</label>
                    <input type="text" class="form-control" id="progressTargetName">
                </div>
                <div class="col-12 mt-3">
                    <div class="d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-outline-secondary" id="cancelAddBtn">
                            <i class="fas fa-times me-1"></i>取消
                        </button>
                        <button type="button" class="btn btn-primary" id="saveProgressBtn">
                            <i class="fas fa-save me-1"></i>保存
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-container" id="statsContainer" style="display: none;">
            <div class="row">
                <div class="col-6 col-md-3">
                    <div class="stat-item">
                        <div class="stat-value" id="totalItems">0</div>
                        <div class="stat-label">总参数</div>
                    </div>
                </div>
                <div class="col-6 col-md-3">
                    <div class="stat-item">
                        <div class="stat-value" id="completedItems">0</div>
                        <div class="stat-label">已完成</div>
                    </div>
                </div>
                <div class="col-6 col-md-3">
                    <div class="stat-item">
                        <div class="stat-value" id="avgProgress">0%</div>
                        <div class="stat-label">平均进度</div>
                    </div>
                </div>
                <div class="col-6 col-md-3">
                    <div class="stat-item">
                        <div class="stat-value" id="activeCategories">0</div>
                        <div class="stat-label">活跃分类</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选控件 -->
        <div class="filter-container d-flex justify-content-between align-items-center flex-wrap">
            <div class="d-flex align-items-center">
                <label for="categoryFilter" class="form-label mb-0 me-2">
                    <i class="fas fa-filter text-primary me-1"></i>按分类筛选:
                </label>
                <select class="form-select form-select-sm" id="categoryFilter" style="width: auto;">
                    <option value="all">全部</option>
                    <!-- 分类选项将通过JS动态添加 -->
                </select>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2" id="manageCategoriesBtn" title="管理分类">
                    <i class="fas fa-tags"></i>
                </button>
            </div>
            <button type="button" class="btn btn-primary" id="toggleFormBtn">
                <i class="fas fa-plus me-1"></i>添加比较
            </button>
        </div>

        <!-- 参数比较卡片容器 -->
        <div class="row" id="dataProgressContainer">
            <!-- 卡片将通过JS动态添加 -->
        </div>
    </div>

    <!-- 编辑模态框 -->
    <div class="modal fade" id="editProgressModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2 text-primary"></i>编辑参数比较
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="editProgressName" class="form-label">参数名称</label>
                            <input type="text" class="form-control" id="editProgressName">
                        </div>
                        <div class="col-md-6">
                            <label for="editProgressCategory" class="form-label">分类</label>
                            <select class="form-select" id="editProgressCategory">
                                <!-- 分类选项将通过JS动态添加 -->
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="editProgressUserValue" class="form-label">当前值</label>
                            <input type="number" class="form-control" id="editProgressUserValue">
                        </div>
                        <div class="col-md-4">
                            <label for="editProgressTargetValue" class="form-label">目标值</label>
                            <input type="number" class="form-control" id="editProgressTargetValue">
                        </div>
                        <div class="col-md-4">
                            <label for="editProgressUnit" class="form-label">单位</label>
                            <input type="text" class="form-control" id="editProgressUnit">
                        </div>
                        <div class="col-12">
                            <label for="editProgressTargetName" class="form-label">目标名称（可选）</label>
                            <input type="text" class="form-control" id="editProgressTargetName">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-primary" id="updateProgressBtn">
                        <i class="fas fa-save me-1"></i>更新
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-trash me-2 text-danger"></i>确认删除
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="mb-0">您确定要删除这个参数比较吗？此操作无法撤销。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                        <i class="fas fa-trash me-1"></i>删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 分类管理模态框 -->
    <div class="modal fade" id="categoryManagementModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-tags me-2 text-primary"></i>分类管理
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="newCategoryName" class="form-label">新分类名称</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="newCategoryName">
                            <button class="btn btn-primary" type="button" id="addCategoryBtn">
                                <i class="fas fa-plus me-1"></i>添加
                            </button>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h6 class="mb-2"><i class="fas fa-list me-1 text-secondary"></i>现有分类</h6>
                        <ul class="list-group" id="categoriesList">
                            <!-- 分类列表将通过JS动态添加 -->
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/app.js"></script>
    <script src="js/data-progress.js"></script>

    <script>
        // 删除可能存在的搜索框和右上角开关
        document.addEventListener('DOMContentLoaded', function() {
            // 删除右上角开关
            const themeToggles = document.querySelectorAll('.theme-toggle');
            themeToggles.forEach(toggle => toggle.remove());
            
            // 删除任何可能存在的搜索框
            const searchBoxes = document.querySelectorAll('input[type="search"], input[placeholder*="搜索"], .search-container, #searchInput');
            searchBoxes.forEach(box => box.remove());
            
            // 查找页面标题下方、筛选控件上方的任何元素并删除
            const container = document.querySelector('.page-header + .container');
            if (container) {
                const children = Array.from(container.children);
                const addProgressForm = document.getElementById('addProgressForm');
                const filterContainer = document.querySelector('.filter-container');
                const dataContainer = document.getElementById('dataProgressContainer');
                
                children.forEach(child => {
                    if (child !== addProgressForm && child !== filterContainer && child !== dataContainer) {
                        child.remove();
                    }
                });
            }
        });
    </script>
</body>
</html> 