<!DOCTYPE html>

<html lang="zh">

<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />
    <title>时间线 - ST计划</title>
    <!-- 添加网站图标 -->
    <link rel="icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <link rel="shortcut icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <!-- 内联PNG图标，用于不支持SVG的浏览器 -->
    <link rel="icon" href="data:image/png;base64,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" type="image/png">
    <!-- 样式文件 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    <link rel="stylesheet" type="text/css" href="css/main.css">
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-light: #818cf8;
            --primary-dark: #4f46e5;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --border-radius: 0.375rem;
            --border-radius-lg: 0.5rem;
        }

        body {
            overflow-x: hidden;
            -webkit-overflow-scrolling: touch;
            touch-action: pan-y;
            position: relative;
            font-family: 'Nunito', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f9fafb;
            color: var(--gray-700);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            padding: 0 15px;
        }

        .card {
            border: none;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid var(--gray-200);
            padding: 1.25rem 1.5rem;
        }

        .card-header h4 {
            font-weight: 700;
            color: var(--gray-800);
            font-size: 1.5rem;
            margin: 0;
        }

        .card-body {
            padding: 1.5rem;
            background-color: white;
        }

        .view-container {
            width: 100%;
            overflow-x: hidden;
        }

        /* 时间线界面 - 紧凑优化版 */
        .timeline-header {
            padding: 8px 0; /* 减少内边距 */
            background-color: white;
            border-bottom: 1px solid var(--gray-200);
            margin-bottom: 12px; /* 减少下边距 */
            box-shadow: 0 1px 2px rgba(0,0,0,0.05); /* 更轻微的阴影 */
        }

        .timeline-container {
            padding: 12px 0; /* 减少内边距 */
            position: relative;
        }

        .timeline-unit {
            position: relative;
            margin-bottom: 15px; /* 从25px减少到15px */
        }

        /* 时间线垂直线 - 紧凑化 */
        .timeline-vertical-line {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 42px; /* 从50px减少到42px */
            width: 2px; /* 从3px减少到2px */
            background-color: var(--primary-light);
            border-radius: 1px;
            z-index: 1;
        }

        /* 时间线项目 - 紧凑化 */
        .timeline-item {
            position: relative;
            padding: 12px 15px 12px 70px; /* 大幅减少内边距 */
            margin-bottom: 8px; /* 从15px减少到8px */
            border-radius: 6px; /* 减小圆角 */
            background-color: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.08); /* 更轻微的阴影 */
            overflow: hidden;
            border-left: 3px solid transparent; /* 从4px减少到3px */
            transition: all 0.2s ease;
        }

        .timeline-item:hover {
            border-left-color: var(--primary-color);
            box-shadow: 0 2px 6px rgba(0,0,0,0.12);
        }

        /* 时间线节点 - 紧凑化 */
        .timeline-dot {
            position: absolute;
            top: 50%;
            left: 42px; /* 对应垂直线位置 */
            width: 18px; /* 从24px减少到18px */
            height: 18px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.15);
        }

        .timeline-dot::after {
            content: '';
            width: 8px; /* 从12px减少到8px */
            height: 8px;
            background-color: white;
            border-radius: 50%;
        }

        /* 时间标签 - 紧凑化 */
        .timeline-time {
            position: absolute;
            top: 50%;
            left: 8px; /* 从15px减少到8px */
            transform: translateY(-50%);
            font-weight: 600; /* 从700减少到600 */
            color: var(--gray-700);
            font-size: 13px; /* 从15px减少到13px */
            width: 26px; /* 从30px减少到26px */
            text-align: center;
        }

        .timeline-unit:hover .timeline-time {
            color: var(--primary-color);
        }

        /* 任务卡片样式 - 紧凑优化版 */
        .task-card {
            padding: 4px 6px; /* 从6px 8px减少到4px 6px */
            border-radius: 4px; /* 减小圆角 */
            margin-bottom: 3px; /* 从5px减少到3px */
            background-color: var(--gray-100);
            border-left: 2px solid var(--primary-color); /* 从4px减少到2px */
            box-shadow: 0 1px 2px rgba(0,0,0,0.06); /* 更轻微的阴影 */
            transition: all 0.2s ease;
        }

        .task-card:hover {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }
        
        /* 高优先级任务 */
        .task-priority-high {
            border-left-color: var(--danger-color);
        }
        .task-priority-high .task-title {
            color: var(--danger-color);
        }

        /* 中优先级任务 */
        .task-priority-medium {
            border-left-color: var(--warning-color);
        }
        .task-priority-medium .task-title {
            color: var(--warning-color);
        }

        /* 低优先级任务 */
        .task-priority-low {
            border-left-color: var(--info-color);
        }
        .task-priority-low .task-title {
            color: var(--info-color);
        }
        
        /* 任务完成状态增强样式 */
        .task-completed {
            background-color: rgba(16, 185, 129, 0.25);
            border-left-color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.4);
            position: relative;
            opacity: 1;
            transition: all 0.3s ease;
            transform: translateZ(0);
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
            --checkmark-scale: 1; /* 添加CSS变量控制对勾大小 */
        }
        
        .task-completed::after {
            content: '✓';
            position: absolute;
            top: 8px;
            right: 8px;
            font-size: 28px;
            color: var(--success-color);
            opacity: 0.7;
            transform: scale(var(--checkmark-scale, 1));
            transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        .task-completed .task-title {
            text-decoration: line-through;
            color: var(--success-color);
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .task-completed .task-description {
            color: var(--gray-500);
            font-style: italic;
            transition: all 0.3s ease;
        }

        /* 添加任务完成时的动画效果 */
        @keyframes taskCompletedPulse {
            0% { 
                background-color: rgba(16, 185, 129, 0.1);
                transform: scale(1);
            }
            50% { 
                background-color: rgba(16, 185, 129, 0.4);
                transform: scale(1.03);
            }
            100% { 
                background-color: rgba(16, 185, 129, 0.25);
                transform: scale(1);
            }
        }

        .task-just-completed {
            animation: taskCompletedPulse 0.6s ease-out forwards;
        }

        /* 任务标题 - 紧凑优化版 */
        .task-title {
            font-weight: 600; /* 从700减少到600 */
            margin-bottom: 2px; /* 从3px减少到2px */
            font-size: 13px; /* 从14px减少到13px */
            cursor: text;
            line-height: 1.3;
        }

        /* 任务描述 - 紧凑优化版 */
        .task-description {
            font-size: 11px; /* 从12px减少到11px */
            color: var(--gray-600);
            line-height: 1.2;
            cursor: text;
            margin-bottom: 2px; /* 从3px减少到2px */
            white-space: pre-wrap;
        }

        /* 任务操作区 - 紧凑优化版 */
        .task-actions {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-top: 3px; /* 从4px减少到3px */
            gap: 2px; /* 从3px减少到2px */
        }

        .task-actions button {
            padding: 1px 4px; /* 从2px 6px减少到1px 4px */
            font-size: 9px; /* 从10px减少到9px */
            border-radius: 3px; /* 减小圆角 */
            border: none;
            min-width: 20px; /* 确保按钮有最小宽度 */
            height: 18px; /* 固定高度 */
        }
        
        .task-actions .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
            transition: all 0.2s ease;
        }
        
        .task-actions .btn-success:hover {
            background-color: rgba(16, 185, 129, 0.8);
            transform: scale(1.1);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .task-actions .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
            transition: all 0.2s ease;
        }
        
        .task-actions .btn-warning:hover {
            background-color: rgba(245, 158, 11, 0.8);
            transform: scale(1.1);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .task-actions .btn-info {
            background-color: var(--info-color);
            border-color: var(--info-color);
            transition: all 0.2s ease;
        }

        .task-actions .btn-info:hover {
            background-color: rgba(59, 130, 246, 0.8);
            transform: scale(1.1);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }



        .task-actions .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            transition: all 0.2s ease;
        }

        .task-actions .btn-primary:hover {
            background-color: rgba(99, 102, 241, 0.8);
            transform: scale(1.1);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .task-actions .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
            transition: all 0.2s ease;
        }

        .task-actions .btn-danger:hover {
            background-color: rgba(239, 68, 68, 0.8);
            transform: scale(1.1);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        /* 编辑任务时样式 */
        .task-title[contenteditable="true"],
        .task-description[contenteditable="true"] {
            background-color: rgba(255, 255, 255, 0.8);
            padding: 2px 5px;
            border-radius: 3px;
            box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
            outline: none;
        }

        /* 无任务时的提示样式 */
        .no-tasks-message {
            text-align: center;
            padding: 40px 20px;
            color: var(--gray-500);
            font-style: italic;
            background-color: var(--gray-100);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow);
            margin: 20px 0;
        }
        
        /* 添加任务按钮样式 - 紧凑优化版 */
        .add-time-slot-btn {
            position: fixed;
            bottom: 20px; /* 从25px减少到20px */
            right: 20px; /* 从25px减少到20px */
            width: 50px; /* 从65px减少到50px */
            height: 50px; /* 从65px减少到50px */
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px; /* 从24px减少到18px */
            box-shadow: 0 2px 8px rgba(0,0,0,0.15); /* 更轻微的阴影 */
            z-index: 100;
            border: none;
            transition: all 0.2s ease;
        }

        .add-time-slot-btn:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        /* 时间段标题编辑样式 - 紧凑优化版 */
        .time-slot-start, .time-slot-end {
            display: inline-block;
            padding: 1px 4px; /* 从2px 5px减少到1px 4px */
            border-radius: 3px; /* 减小圆角 */
            background-color: rgba(255, 255, 255, 0.5);
            min-width: 40px; /* 从45px减少到40px */
            font-weight: 600;
            font-size: 13px; /* 添加字体大小 */
        }

        .time-slot-start:hover, .time-slot-end:hover {
            background-color: var(--gray-100);
        }

        .time-slot-start:focus, .time-slot-end:focus {
            outline: none;
            background-color: white;
            box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.25);
        }

        /* 时间段操作按钮容器 - 紧凑优化版 */
        .timeline-item-actions {
            display: flex;
            gap: 4px; /* 从8px减少到4px */
        }

        /* 时间段操作按钮 - 紧凑优化版 */
        .timeline-item-actions .btn {
            padding: 3px 6px; /* 减小按钮内边距 */
            font-size: 11px; /* 减小字体 */
            border-radius: 3px; /* 减小圆角 */
            min-width: 24px; /* 最小宽度 */
            height: 24px; /* 固定高度 */
        }

        /* 添加全局完成提示样式 */
        .task-complete-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: var(--success-color);
            color: white;
            padding: 10px 20px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            z-index: 9999;
            transform: translateX(200%);
            animation: toastAppear 2.5s ease forwards;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .task-complete-toast i {
            font-size: 20px;
        }

        @keyframes toastAppear {
            0% { transform: translateX(200%); }
            15% { transform: translateX(0); }
            85% { transform: translateX(0); opacity: 1; }
            100% { transform: translateX(200%); opacity: 0; }
        }

        /* 任务完成时的文字动画 */
        @keyframes completedTextEffect {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* 删除文字动画效果应用 */
        .task-completed .task-title,
        .task-completed .task-description {
            /* 移除动画效果 */
            animation: none;
        }

        /* 紧凑化额外优化 */
        .timeline-item-header h5 {
            font-size: 1rem; /* 减小时间段标题字体 */
            font-weight: 600;
            margin-bottom: 0;
        }

        /* 任务列表容器优化 */
        .task-list {
            margin-top: 8px; /* 从默认减少间距 */
        }

        /* 无任务提示优化 */
        .no-tasks-message {
            text-align: center;
            padding: 20px 15px; /* 从40px 20px减少到20px 15px */
            color: var(--gray-500);
            font-style: italic;
            background-color: var(--gray-100);
            border-radius: 6px; /* 减小圆角 */
            box-shadow: 0 1px 3px rgba(0,0,0,0.08); /* 更轻微的阴影 */
            margin: 10px 0; /* 从20px减少到10px */
            font-size: 14px; /* 减小字体 */
        }

        /* 移动端响应式优化 */
        @media (max-width: 768px) {
            .timeline-container {
                padding: 8px 0; /* 移动端进一步减少内边距 */
            }

            .timeline-unit {
                margin-bottom: 12px; /* 移动端减少间距 */
            }

            .timeline-item {
                padding: 10px 12px 10px 60px; /* 移动端减少内边距 */
                margin-bottom: 6px;
            }

            .timeline-vertical-line {
                left: 35px; /* 移动端调整位置 */
            }

            .timeline-dot {
                left: 35px; /* 对应垂直线位置 */
                width: 16px; /* 移动端更小的节点 */
                height: 16px;
            }

            .timeline-dot::after {
                width: 6px;
                height: 6px;
            }

            .timeline-time {
                left: 5px;
                font-size: 11px; /* 移动端更小的字体 */
                width: 22px;
            }

            .task-card {
                padding: 3px 5px; /* 移动端更紧凑的任务卡片 */
                margin-bottom: 2px;
            }

            .task-title {
                font-size: 12px; /* 移动端减小字体 */
            }

            .task-description {
                font-size: 10px; /* 移动端减小字体 */
            }

            .add-time-slot-btn {
                width: 45px; /* 移动端更小的悬浮按钮 */
                height: 45px;
                font-size: 16px;
                bottom: 15px;
                right: 15px;
            }
        }

        /* 统计信息显示 */
        .timeline-stats {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 8px 12px;
            margin-bottom: 12px;
            font-size: 12px;
            color: var(--gray-600);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .timeline-stats .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .timeline-stats .stat-item i {
            font-size: 11px;
        }

        /* 批量复制模式样式 */
        .batch-copy-mode .task-card {
            position: relative;
            padding-left: 30px; /* 为复选框留出空间 */
        }

        .batch-copy-mode .task-checkbox {
            position: absolute;
            left: 6px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            cursor: pointer;
        }

        .batch-copy-controls {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 8px 12px;
            margin-bottom: 12px;
            display: none;
            align-items: center;
            justify-content: space-between;
            border: 1px solid var(--gray-300);
        }

        .batch-copy-controls.active {
            display: flex;
        }

        .batch-copy-controls .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .batch-copy-controls .btn {
            padding: 4px 8px;
            font-size: 12px;
        }

        /* 已复制任务的视觉标记 */
        .task-copied {
            position: relative;
            opacity: 0.7;
        }

        .task-copied::before {
            content: '已复制';
            position: absolute;
            top: 2px;
            right: 2px;
            background: var(--success-color);
            color: white;
            font-size: 8px;
            padding: 1px 4px;
            border-radius: 2px;
            z-index: 10;
        }


    </style>
</head>

<body>
    <div class="container mt-3" id="appContainer">
        <!-- 顶部导航按钮 - 右上角布局 -->
        <div class="row mb-2">
            <div class="col-12 d-flex justify-content-end">
                <button class="btn btn-primary btn-sm" onclick="window.location.href='index.html'">
                    <i class="fas fa-home me-1" style="font-size: 14px;"></i>🏠 返回主页
                </button>
            </div>
        </div>

        <!-- 时间线主视图 - 紧凑优化版 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body" style="padding: 1rem;"> <!-- 减少卡片内边距 -->
                        <!-- 标题栏和按钮组 - 紧凑优化版 -->
                        <div class="d-flex justify-content-between align-items-center mb-3"> <!-- 从mb-4减少到mb-3 -->
                            <h6 class="text-primary mb-0" style="font-size: 1.1rem; font-weight: 600;">日视图</h6> <!-- 从h5改为h6，减小字体 -->
                            <div class="d-flex gap-2">
                                <button id="batchCopyBtn" class="btn btn-outline-primary" style="padding: 4px 8px; font-size: 12px;"> <!-- 批量复制按钮 -->
                                    <i class="fas fa-copy" style="font-size: 11px;"></i> 批量复制
                                </button>
                                <button id="settingsBtn" class="btn btn-outline-secondary" style="padding: 4px 8px; font-size: 12px;"> <!-- 更小的按钮 -->
                                    <i class="fas fa-cog" style="font-size: 11px;"></i> 设置
                                </button>
                            </div>
                        </div>

                        <!-- 日期显示和导航 - 紧凑优化版 -->
                        <div class="d-flex justify-content-center align-items-center mb-3"> <!-- 从mb-4减少到mb-3 -->
                            <button id="prevDateBtn" class="btn btn-outline-primary btn-sm me-2"> <!-- 添加btn-sm，从me-3减少到me-2 -->
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <div id="currentDate" class="current-date fw-bold" style="font-size: 1.1rem;">2024年4月15日</div> <!-- 从fs-5减小字体 -->
                            <button id="nextDateBtn" class="btn btn-outline-primary btn-sm ms-2"> <!-- 添加btn-sm，从ms-3减少到ms-2 -->
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                            
                        <!-- 统计信息显示 -->
                        <div class="timeline-stats" id="timelineStats" style="display: none;">
                            <div class="stat-item">
                                <i class="fas fa-clock"></i>
                                <span id="totalTimeSlots">0个时间段</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-tasks"></i>
                                <span id="totalTasks">0个任务</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-check-circle"></i>
                                <span id="completedTasks">0个已完成</span>
                            </div>
                        </div>

                        <!-- 批量复制控制栏 -->
                        <div class="batch-copy-controls" id="batchCopyControls">
                            <div class="control-group">
                                <label class="form-check-label">
                                    <input type="checkbox" id="selectAllTasks" class="form-check-input"> 全选
                                </label>
                                <span id="selectedCount">已选择 0 个任务</span>
                            </div>
                            <div class="control-group">
                                <button id="copySelectedBtn" class="btn btn-primary" disabled>
                                    <i class="fas fa-copy"></i> 复制选中任务到明天
                                </button>
                                <button id="cancelBatchCopyBtn" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> 取消
                                </button>
                            </div>
                        </div>

                        <!-- 时间线容器 -->
                        <div id="timelineContainer" class="timeline-container">
                            <!-- 时间线垂直线和连接器 -->
                            <div class="timeline-vertical-line"></div>
                            <div class="timeline-connector"></div>

                            <!-- 时间单位和任务将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 添加新时间段的悬浮按钮 -->
    <button id="addTimeSlotBtn" class="add-time-slot-btn">
        <i class="fas fa-plus"></i>
    </button>
    
    <!-- 批量添加任务模态框 -->
    <div class="modal fade" id="addTaskModal" tabindex="-1" aria-labelledby="addTaskModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addTaskModalLabel">批量添加任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addTaskForm">
                        <input type="hidden" id="timeSlotId">
                        <input type="hidden" id="taskPositionIndex">

                        <div class="mb-3">
                            <label for="batchTaskInput" class="form-label">任务列表（一行一个任务）</label>
                            <textarea class="form-control" id="batchTaskInput" rows="8" placeholder="请输入任务，每行一个任务，例如：&#10;完成项目报告&#10;回复邮件&#10;准备会议材料&#10;整理文档"></textarea>
                            <small class="form-text text-muted">每行输入一个任务名称，系统会自动为每个任务创建独立的卡片</small>
                        </div>

                        <div class="mb-3">
                            <label for="taskPriority" class="form-label">统一优先级</label>
                            <select class="form-select" id="taskPriority">
                                <option value="high">高</option>
                                <option value="medium" selected>中</option>
                                <option value="low">低</option>
                            </select>
                            <small class="form-text text-muted">所有添加的任务将使用相同的优先级</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveTaskBtn">批量添加</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑任务模态框 -->
    <div class="modal fade" id="editTaskModal" tabindex="-1" aria-labelledby="editTaskModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editTaskModalLabel">编辑任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editTaskForm">
                        <input type="hidden" id="editTaskId">

                        <div class="mb-3">
                            <label for="editTaskTitle" class="form-label">任务名称</label>
                            <input type="text" class="form-control" id="editTaskTitle" required>
                        </div>

                        <div class="mb-3">
                            <label for="editTaskDescription" class="form-label">任务描述</label>
                            <textarea class="form-control" id="editTaskDescription" rows="3"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="editTaskPriority" class="form-label">优先级</label>
                            <select class="form-select" id="editTaskPriority">
                                <option value="high">高</option>
                                <option value="medium">中</option>
                                <option value="low">低</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="editTaskTimeslot" class="form-label">时间段</label>
                            <select class="form-select" id="editTaskTimeslot">
                                <!-- 时间段选项将动态生成 -->
                            </select>
                            <small class="form-text text-muted">选择任务所属的时间段</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveEditTaskBtn">保存修改</button>
                </div>
            </div>
        </div>
    </div>


    <!-- 添加设置模态框 -->
    <div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="settingsModalLabel">设置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="settingsForm">
                        <div class="mb-3">
                            <label for="timeSlotDuration" class="form-label">时间段默认时长（小时）</label>
                            <select class="form-select" id="timeSlotDuration">
                                <option value="1">1小时</option>
                                <option value="2">2小时</option>
                                <option value="3">3小时</option>
                                <option value="4">4小时</option>
                            </select>
                        </div>

                        <div class="mb-3 d-none">
                            <label for="maxDataDays" class="form-label">最多保存天数</label>
                            <select class="form-select" id="maxDataDays">
                                <option value="7">7天</option>
                                <option value="14">14天</option>
                                <option value="30">30天</option>
                                <option value="90">90天</option>
                            </select>
                            <small class="form-text text-muted">超过设定天数的数据将被自动删除</small>
                        </div>

                        <div class="mb-3 form-check d-none">
                            <input type="checkbox" class="form-check-input" id="autoSyncData" checked>
                            <label class="form-check-label" for="autoSyncData">自动同步数据到主系统</label>
                        </div>

                        <div class="mb-3">
                            <button type="button" class="btn btn-danger w-100" id="clearCurrentTimelineBtn">
                                <i class="fas fa-trash-alt me-2"></i>清空当前时间线数据
                            </button>
                            <small class="form-text text-muted">此操作将永久删除当前日期的所有时间段和任务</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveSettingsBtn">保存设置</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.min.js"></script>
    
    <script>
        // 确保DOM完全加载后运行脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 为完成任务添加滚动时的视觉效果
            function initCompletedTasksAnimation() {
                // 使用 Intersection Observer 检测元素进入视口
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            // 如果任务卡片进入视口，添加对勾缩放动画
                            const taskCard = entry.target;
                            
                            // 只对已完成的任务添加动画
                            if (taskCard.classList.contains('task-completed')) {
                                // 延迟一点以使动画在滚动后触发，效果更明显
                                setTimeout(() => {
                                    taskCard.style.setProperty('--checkmark-scale', '1.5');
                                    setTimeout(() => {
                                        taskCard.style.setProperty('--checkmark-scale', '1');
                                    }, 400);
                                }, 100);
                            }
                        }
                    });
                }, { threshold: 0.5 }); // 当元素50%可见时触发
                
                // 观察所有现有和未来的任务卡片
                function observeTaskCards() {
                    try {
                        // 获取所有任务卡片
                        const taskCards = document.querySelectorAll('.task-card');
                        taskCards.forEach(card => {
                            observer.observe(card);
                        });
                        console.log('已观察', taskCards.length, '个任务卡片');
                    } catch (error) {
                        console.error('观察任务卡片时出错:', error);
                    }
                }
                
                // 初始观察现有任务卡片
                try {
                    observeTaskCards();
                } catch (error) {
                    console.error('初始化观察任务卡片时出错:', error);
                }
                
                // 每次数据加载后重新观察
                // document.addEventListener('DOMContentLoaded', observeTaskCards); // 移除这行，因为我们已经在DOMContentLoaded回调内
                
                // 返回观察函数，供任务添加后调用
                return observeTaskCards;
            }

            // 初始化动画观察器
            const observeTaskCards = initCompletedTasksAnimation();

            // 获取模态框实例
            const addTaskModal = new bootstrap.Modal(document.getElementById('addTaskModal'));
            const settingsModal = new bootstrap.Modal(document.getElementById('settingsModal'));

            // 绑定编辑任务保存按钮
            const saveEditTaskBtn = document.getElementById('saveEditTaskBtn');
            if (saveEditTaskBtn) {
                saveEditTaskBtn.addEventListener('click', handleSaveEditTask);
            }


            
            // 监听添加任务模态框显示事件，用于调试
            document.getElementById('addTaskModal').addEventListener('shown.bs.modal', function() {
                console.log('添加任务模态框已显示');
                // 不再重复绑定保存按钮，避免重复事件
            });
            
            // 绑定添加时间段按钮的函数
            function bindAddTimeSlotButton() {
                const addTimeSlotBtn = document.getElementById('addTimeSlotBtn');
                if (addTimeSlotBtn) {
                    // 先移除所有已有的事件监听器
                    const newAddTimeSlotBtn = addTimeSlotBtn.cloneNode(true);
                    addTimeSlotBtn.parentNode.replaceChild(newAddTimeSlotBtn, addTimeSlotBtn);

                    // 添加新的事件监听器
                    newAddTimeSlotBtn.addEventListener('click', function() {
                        console.log('添加时间段按钮被点击！');

                        try {
                            // 查找现有的时间段
                            const timeUnits = Array.from(document.querySelectorAll('.timeline-unit'));
                            console.log('现有时间段数量:', timeUnits.length);

                            // 默认从当前小时开始
                            let startHour = new Date().getHours();
                            console.log('当前小时:', startHour);

                            if (timeUnits.length > 0) {
                                // 更简单直接的方法：找到最后添加的时间段（DOM中的最后一个）
                                const lastUnit = timeUnits[timeUnits.length - 1];
                                const lastStartTime = parseInt(lastUnit.dataset.hour, 10);
                                const lastDuration = parseInt(lastUnit.dataset.duration || 1, 10);

                                // 计算最后一个时间段的结束时间
                                let lastEndTime = lastStartTime + lastDuration;

                                console.log(`最后一个时间段: ${lastStartTime}:00 - ${lastEndTime % 24}:00`);
                                console.log(`计算的结束时间: ${lastEndTime}`);

                                // 新时间段从最后一个时间段的结束时间开始
                                startHour = lastEndTime % 24;
                            }

                            console.log('新时间段开始时间:', startHour);
                    
                    // 获取当前设置的时间段时长，如果appSettings未定义则使用默认值
                    const duration = (typeof appSettings !== 'undefined' && appSettings.timeSlotDuration)
                        ? parseInt(appSettings.timeSlotDuration, 10)
                        : 1;
                    
                    // 计算结束小时
                    const endHour = (startHour + duration) % 24;
                    
                    // 创建唯一ID
                    const timeSlotId = 'timeslot-' + Date.now();
                    
                    // 创建新时间段的HTML
                    const timeSlotHtml = `
                        <div class="timeline-unit" data-hour="${startHour}" data-duration="${duration}">
                            <div class="timeline-time">${startHour}:00</div>
                            <div class="timeline-dot"></div>
                            <div class="timeline-item" data-id="${timeSlotId}">
                                <div class="timeline-item-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <span class="time-slot-start" contenteditable="true">${startHour}:00</span> - 
                                        <span class="time-slot-end" contenteditable="true">${endHour}:00</span>
                                    </h5>
                                    <div class="timeline-item-actions">
                                        <button class="btn btn-sm btn-outline-primary add-task-btn">
                                            <i class="fas fa-plus"></i> 添加任务
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger delete-timeslot-btn ml-2">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="task-list mt-3">
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // 将新时间段添加到时间线容器的最底部
                    const timelineContainer = document.getElementById('timelineContainer');
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = timeSlotHtml.trim();

                    // 直接添加到末尾，不进行排序
                    timelineContainer.appendChild(tempDiv.firstChild);

                    console.log('新时间段已添加到底部');
                    
                    // 重新绑定所有按钮事件
                    bindAddTaskButtons();
                    bindTimeSlotEditEvents();
                    bindDeleteTimeSlotButtons(); // 绑定删除时间段按钮事件
                    bindTaskContentEditEvents(); // 绑定任务内容编辑事件

                    // 更新统计信息
                    updateTimelineStats();

                    console.log('添加新时间段:', { startHour, endHour, timeSlotId });

                    } catch (error) {
                        console.error('添加时间段时出错:', error);
                        alert('添加时间段时出错: ' + error.message);
                    }
                });
                }
            }
            
            // 专门绑定保存任务按钮的函数
            function bindSaveTaskButton() {
                const saveTaskBtn = document.getElementById('saveTaskBtn');
                if (!saveTaskBtn) {
                    console.error('未找到保存任务按钮!');
                    return;
                }
                
                console.log('开始绑定保存任务按钮...');
                
                // 移除已有的点击事件(如果有)
                saveTaskBtn.removeEventListener('click', handleSaveTaskClick);
                
                // 添加新的点击事件
                saveTaskBtn.addEventListener('click', handleSaveTaskClick);
                console.log('保存任务按钮事件已绑定');
            }
            
            // 保存任务按钮点击处理函数 - 支持批量添加
            function handleSaveTaskClick(e) {
                console.log('批量保存任务按钮被点击');

                try {
                    // 获取批量任务输入
                    const batchTaskInput = document.getElementById('batchTaskInput').value.trim();
                    const priority = document.getElementById('taskPriority').value;
                    const timeSlotId = document.getElementById('timeSlotId').value;

                    if (!batchTaskInput) {
                        alert('请输入至少一个任务');
                        return;
                    }

                    // 按行分割任务
                    const taskLines = batchTaskInput.split('\n').filter(line => line.trim() !== '');

                    if (taskLines.length === 0) {
                        alert('请输入至少一个任务');
                        return;
                    }

                    // 找到正确的时间段
                    const timeSlots = document.querySelectorAll('.timeline-item');
                    let targetTimeSlot;

                    if (timeSlotId) {
                        targetTimeSlot = document.querySelector(`.timeline-item[data-id="${timeSlotId}"]`);
                    } else {
                        targetTimeSlot = timeSlots[0];
                    }

                    if (!targetTimeSlot) {
                        console.error('未找到目标时间段');
                        alert('未找到目标时间段，请先添加一个时间段');
                        return;
                    }

                    const taskList = targetTimeSlot.querySelector('.task-list');
                    if (!taskList) {
                        console.error('未找到任务列表容器');
                        return;
                    }

                    // 批量创建任务
                    taskLines.forEach((taskTitle, index) => {
                        const trimmedTitle = taskTitle.trim();
                        if (trimmedTitle) {
                            createSingleTask(trimmedTitle, '', priority, taskList);
                        }
                    });

                    // 重新绑定所有事件
                    bindTaskContentEditEvents();
                    bindAllTaskButtons();

                    // 保存当前状态
                    saveCurrentDateData();
                    updateTimelineStats();

                    // 使新任务也被观察以添加动画效果
                    if (typeof observeTaskCards === 'function') {
                        try {
                            console.log('为新添加的任务添加观察器');
                            observeTaskCards();
                        } catch (error) {
                            console.error('添加任务观察器时出错:', error);
                        }
                    }

                    console.log(`批量添加了 ${taskLines.length} 个任务`);

                    // 清空表单
                    document.getElementById('addTaskForm').reset();
                    document.getElementById('timeSlotId').value = '';
                    document.getElementById('taskPositionIndex').value = '';

                    // 关闭模态框
                    addTaskModal.hide();

                } catch (error) {
                    console.error('批量添加任务时出错:', error);
                    alert('批量添加任务时出错: ' + error.message);
                }
            }

            // 创建单个任务的函数
            function createSingleTask(title, description, priority, taskList) {
                // 生成唯一ID
                const taskId = 'task-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

                // 创建新任务卡片的HTML
                const taskHtml = `
                    <div class="task-card task-priority-${priority}" data-task-id="${taskId}">
                        <div class="task-title">${title}</div>
                        <div class="task-description">${description}</div>
                        <div class="task-actions">
                            <button class="btn btn-sm btn-success complete-task-btn" title="完成任务">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-sm btn-warning uncomplete-task-btn" style="display: none;" title="取消完成">
                                <i class="fas fa-undo"></i>
                            </button>
                            <button class="btn btn-sm btn-info edit-task-btn" title="编辑任务">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger delete-task-btn" title="删除任务">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;

                // 将新任务添加到任务列表
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = taskHtml.trim();
                const newTaskCard = tempDiv.firstChild;
                taskList.appendChild(newTaskCard);

                // 为新任务添加直接的点击事件
                bindTaskCardEvents(newTaskCard);

                // 如果当前处于批量复制模式，为新任务添加复选框
                if (batchCopyMode) {
                    const checkbox = document.createElement('input');
                    checkbox.type = 'checkbox';
                    checkbox.className = 'task-checkbox';
                    checkbox.addEventListener('change', function() {
                        const taskId = newTaskCard.dataset.taskId;
                        if (this.checked) {
                            selectedTasks.add(taskId);
                        } else {
                            selectedTasks.delete(taskId);
                        }
                        updateSelectedCount();
                    });
                    newTaskCard.appendChild(checkbox);
                }

                return newTaskCard;
            }

            // 绑定单个任务卡片的事件
            function bindTaskCardEvents(taskCard) {
                const completeBtn = taskCard.querySelector('.complete-task-btn');
                if (completeBtn) {
                    completeBtn.onclick = function(e) {
                        e.stopPropagation();
                        toggleTaskComplete(taskCard, true);
                    };
                }

                const uncompleteBtn = taskCard.querySelector('.uncomplete-task-btn');
                if (uncompleteBtn) {
                    uncompleteBtn.onclick = function(e) {
                        e.stopPropagation();
                        toggleTaskComplete(taskCard, false);
                    };
                }

                const editBtn = taskCard.querySelector('.edit-task-btn');
                if (editBtn) {
                    editBtn.onclick = function(e) {
                        e.stopPropagation();
                        openEditTaskModal(taskCard);
                    };
                }



                const deleteBtn = taskCard.querySelector('.delete-task-btn');
                if (deleteBtn) {
                    deleteBtn.onclick = function(e) {
                        e.stopPropagation();
                        // 直接删除，不显示确认弹窗
                        taskCard.remove();
                        updateTimelineStats();
                        saveCurrentDateData();
                    };
                }
            }

            // 打开编辑任务模态框
            function openEditTaskModal(taskCard) {
                const taskId = taskCard.dataset.taskId;
                const taskTitle = taskCard.querySelector('.task-title').textContent;
                const taskDescription = taskCard.querySelector('.task-description').innerHTML.replace(/<br>/g, '\n');

                // 获取当前优先级
                let currentPriority = 'medium';
                if (taskCard.classList.contains('task-priority-high')) {
                    currentPriority = 'high';
                } else if (taskCard.classList.contains('task-priority-low')) {
                    currentPriority = 'low';
                }

                // 获取当前任务所在的时间段
                const currentTimeSlot = taskCard.closest('.timeline-item');
                const currentTimeSlotId = currentTimeSlot ? currentTimeSlot.dataset.id : '';

                // 填充时间段选择器
                const editTaskTimeslotSelect = document.getElementById('editTaskTimeslot');
                editTaskTimeslotSelect.innerHTML = '';

                // 获取所有时间段
                const timeSlots = document.querySelectorAll('.timeline-item');
                timeSlots.forEach((slot, index) => {
                    const timeSlotId = slot.dataset.id;
                    const timeSlotHeader = slot.querySelector('.timeline-item-header h5');
                    const timeSlotText = timeSlotHeader ? timeSlotHeader.textContent.trim() : `时间段 ${index + 1}`;

                    const option = document.createElement('option');
                    option.value = timeSlotId;
                    option.textContent = timeSlotText;

                    // 设置当前时间段为选中状态
                    if (timeSlotId === currentTimeSlotId) {
                        option.selected = true;
                    }

                    editTaskTimeslotSelect.appendChild(option);
                });

                // 填充编辑表单
                document.getElementById('editTaskId').value = taskId;
                document.getElementById('editTaskTitle').value = taskTitle;
                document.getElementById('editTaskDescription').value = taskDescription;
                document.getElementById('editTaskPriority').value = currentPriority;

                // 显示编辑模态框
                const editTaskModal = new bootstrap.Modal(document.getElementById('editTaskModal'));
                editTaskModal.show();
            }

            // 保存编辑任务
            function handleSaveEditTask() {
                try {
                    const taskId = document.getElementById('editTaskId').value;
                    const newTitle = document.getElementById('editTaskTitle').value.trim();
                    const newDescription = document.getElementById('editTaskDescription').value.trim();
                    const newPriority = document.getElementById('editTaskPriority').value;
                    const newTimeslotId = document.getElementById('editTaskTimeslot').value;

                    if (!newTitle) {
                        alert('请输入任务名称');
                        return;
                    }

                    // 找到对应的任务卡片
                    const taskCard = document.querySelector(`[data-task-id="${taskId}"]`);
                    if (!taskCard) {
                        alert('未找到要编辑的任务');
                        return;
                    }

                    // 获取当前任务所在的时间段
                    const currentTimeSlot = taskCard.closest('.timeline-item');
                    const currentTimeSlotId = currentTimeSlot ? currentTimeSlot.dataset.id : '';

                    // 更新任务内容
                    taskCard.querySelector('.task-title').textContent = newTitle;
                    taskCard.querySelector('.task-description').innerHTML = newDescription.replace(/\n/g, '<br>');

                    // 更新优先级样式
                    taskCard.classList.remove('task-priority-high', 'task-priority-medium', 'task-priority-low');
                    taskCard.classList.add(`task-priority-${newPriority}`);

                    // 如果时间段发生了变化，移动任务
                    if (newTimeslotId && newTimeslotId !== currentTimeSlotId) {
                        const targetTimeSlot = document.querySelector(`[data-id="${newTimeslotId}"]`);
                        if (targetTimeSlot) {
                            const targetTaskList = targetTimeSlot.querySelector('.task-list');
                            if (targetTaskList) {
                                // 移动任务到新时间段
                                taskCard.remove();
                                targetTaskList.appendChild(taskCard);
                                console.log('任务已移动到新时间段:', { taskId, newTimeslotId });
                            }
                        }
                    }

                    // 保存数据
                    saveCurrentDateData();
                    updateTimelineStats();

                    // 关闭模态框
                    const editTaskModal = bootstrap.Modal.getInstance(document.getElementById('editTaskModal'));
                    editTaskModal.hide();

                    console.log('任务编辑完成:', { taskId, newTitle, newDescription, newPriority, newTimeslotId });

                } catch (error) {
                    console.error('编辑任务时出错:', error);
                    alert('编辑任务时出错: ' + error.message);
                }
            }



            // 复制任务到明天
            function copyTaskToTomorrow(taskCard) {
                try {
                    const taskId = taskCard.dataset.taskId;

                    // 检查这个任务是否已经复制过
                    if (copiedTasksToday.has(taskId)) {
                        alert('该任务已经复制到明天，无法重复复制');
                        return;
                    }

                    const taskTitle = taskCard.querySelector('.task-title').textContent.trim();
                    const taskDescription = taskCard.querySelector('.task-description').innerHTML.replace(/<br>/g, '\n');

                    // 创建明天的日期
                    const tomorrow = new Date(currentDate);
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    const tomorrowKey = formatDateKey(tomorrow);

                    // 获取任务优先级
                    let priority = 'medium';
                    if (taskCard.classList.contains('task-priority-high')) {
                        priority = 'high';
                    } else if (taskCard.classList.contains('task-priority-low')) {
                        priority = 'low';
                    }

                    // 获取当前任务所在的时间段信息
                    const currentTimeSlot = taskCard.closest('.timeline-item');
                    const currentTimeSlotHeader = currentTimeSlot.querySelector('.timeline-item-header h5');
                    const currentTimeSlotText = currentTimeSlotHeader ? currentTimeSlotHeader.textContent.trim() : '';

                    // 创建任务数据
                    const taskData = {
                        title: taskTitle,
                        description: taskDescription,
                        priority: priority,
                        timeSlotText: currentTimeSlotText,
                        id: 'task-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9),
                        originalTaskId: taskId // 记录原始任务ID
                    };

                    // 保存到明天的数据中
                    if (!dateData[tomorrowKey]) {
                        dateData[tomorrowKey] = {
                            html: `
                                <div class="timeline-vertical-line"></div>
                                <div class="timeline-connector"></div>
                            `,
                            timestamp: new Date().getTime(),
                            copiedTasks: []
                        };
                    }

                    if (!dateData[tomorrowKey].copiedTasks) {
                        dateData[tomorrowKey].copiedTasks = [];
                    }

                    dateData[tomorrowKey].copiedTasks.push(taskData);

                    // 标记这个任务已经复制过
                    copiedTasksToday.add(taskId);

                    // 为已复制的任务添加视觉标记
                    taskCard.classList.add('task-copied');

                    // 保存数据
                    saveCurrentDateData();

                    console.log('任务已复制到明天:', taskData);

                } catch (error) {
                    console.error('复制任务到明天时出错:', error);
                    alert('复制任务到明天时出错: ' + error.message);
                }
            }

            // 恢复已复制任务的状态
            function restoreCopiedTasksStatus() {
                // 获取明天的日期键
                const tomorrow = new Date(currentDate);
                tomorrow.setDate(tomorrow.getDate() + 1);
                const tomorrowKey = formatDateKey(tomorrow);

                // 检查明天是否有复制的任务
                if (dateData[tomorrowKey] && dateData[tomorrowKey].copiedTasks) {
                    dateData[tomorrowKey].copiedTasks.forEach(copiedTask => {
                        if (copiedTask.originalTaskId) {
                            // 标记原始任务为已复制
                            copiedTasksToday.add(copiedTask.originalTaskId);

                            // 为原始任务添加视觉标记
                            const originalTaskCard = document.querySelector(`[data-task-id="${copiedTask.originalTaskId}"]`);
                            if (originalTaskCard) {
                                originalTaskCard.classList.add('task-copied');
                            }
                        }
                    });
                }

                console.log('已恢复复制状态，已复制任务数量:', copiedTasksToday.size);
            }

            // 批量复制模式相关变量
            let batchCopyMode = false;
            let selectedTasks = new Set();
            let copiedTasksToday = new Set(); // 记录今天已经复制到明天的任务ID

            // 进入批量复制模式
            function enterBatchCopyMode() {
                batchCopyMode = true;
                selectedTasks.clear();

                // 显示控制栏
                const controls = document.getElementById('batchCopyControls');
                controls.classList.add('active');

                // 为时间线容器添加批量复制模式类
                const timelineContainer = document.getElementById('timelineContainer');
                timelineContainer.classList.add('batch-copy-mode');

                // 为所有任务卡片添加复选框
                addCheckboxesToTasks();

                // 更新选择计数
                updateSelectedCount();

                console.log('进入批量复制模式');
            }

            // 退出批量复制模式
            function exitBatchCopyMode() {
                batchCopyMode = false;
                selectedTasks.clear();

                // 隐藏控制栏
                const controls = document.getElementById('batchCopyControls');
                controls.classList.remove('active');

                // 移除时间线容器的批量复制模式类
                const timelineContainer = document.getElementById('timelineContainer');
                timelineContainer.classList.remove('batch-copy-mode');

                // 移除所有复选框
                removeCheckboxesFromTasks();

                console.log('退出批量复制模式');
            }

            // 为所有任务添加复选框
            function addCheckboxesToTasks() {
                const taskCards = document.querySelectorAll('.task-card');
                taskCards.forEach(taskCard => {
                    if (!taskCard.querySelector('.task-checkbox')) {
                        const checkbox = document.createElement('input');
                        checkbox.type = 'checkbox';
                        checkbox.className = 'task-checkbox';
                        checkbox.addEventListener('change', function() {
                            const taskId = taskCard.dataset.taskId;
                            if (this.checked) {
                                selectedTasks.add(taskId);
                            } else {
                                selectedTasks.delete(taskId);
                            }
                            updateSelectedCount();
                        });
                        taskCard.appendChild(checkbox);
                    }
                });
            }

            // 移除所有任务的复选框
            function removeCheckboxesFromTasks() {
                const checkboxes = document.querySelectorAll('.task-checkbox');
                checkboxes.forEach(checkbox => checkbox.remove());
            }

            // 更新选择计数
            function updateSelectedCount() {
                const countElement = document.getElementById('selectedCount');
                const copyBtn = document.getElementById('copySelectedBtn');
                const selectAllCheckbox = document.getElementById('selectAllTasks');

                countElement.textContent = `已选择 ${selectedTasks.size} 个任务`;
                copyBtn.disabled = selectedTasks.size === 0;

                // 更新全选复选框状态
                const totalTasks = document.querySelectorAll('.task-card').length;
                if (selectedTasks.size === 0) {
                    selectAllCheckbox.indeterminate = false;
                    selectAllCheckbox.checked = false;
                } else if (selectedTasks.size === totalTasks) {
                    selectAllCheckbox.indeterminate = false;
                    selectAllCheckbox.checked = true;
                } else {
                    selectAllCheckbox.indeterminate = true;
                    selectAllCheckbox.checked = false;
                }
            }

            // 批量复制选中的任务到明天
            function copySelectedTasksToTomorrow() {
                if (selectedTasks.size === 0) {
                    alert('请先选择要复制的任务');
                    return;
                }

                try {
                    // 创建明天的日期
                    const tomorrow = new Date(currentDate);
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    const tomorrowKey = formatDateKey(tomorrow);

                    let copiedCount = 0;
                    let skippedCount = 0;

                    // 复制选中的任务
                    selectedTasks.forEach(taskId => {
                        // 检查这个任务是否已经复制过
                        if (copiedTasksToday.has(taskId)) {
                            skippedCount++;
                            console.log(`跳过已复制的任务: ${taskId}`);
                            return; // 跳过这个任务
                        }

                        const taskCard = document.querySelector(`[data-task-id="${taskId}"]`);
                        if (taskCard) {
                            const taskTitle = taskCard.querySelector('.task-title').textContent.trim();
                            const taskDescription = taskCard.querySelector('.task-description').innerHTML.replace(/<br>/g, '\n');

                            // 获取任务优先级
                            let priority = 'medium';
                            if (taskCard.classList.contains('task-priority-high')) {
                                priority = 'high';
                            } else if (taskCard.classList.contains('task-priority-low')) {
                                priority = 'low';
                            }

                            // 获取当前任务所在的时间段信息
                            const currentTimeSlot = taskCard.closest('.timeline-item');
                            const currentTimeSlotHeader = currentTimeSlot.querySelector('.timeline-item-header h5');
                            const currentTimeSlotText = currentTimeSlotHeader ? currentTimeSlotHeader.textContent.trim() : '';

                            // 创建任务数据
                            const taskData = {
                                title: taskTitle,
                                description: taskDescription,
                                priority: priority,
                                timeSlotText: currentTimeSlotText,
                                id: 'task-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9),
                                originalTaskId: taskId // 记录原始任务ID
                            };

                            // 保存到明天的数据中
                            if (!dateData[tomorrowKey]) {
                                dateData[tomorrowKey] = {
                                    html: `
                                        <div class="timeline-vertical-line"></div>
                                        <div class="timeline-connector"></div>
                                    `,
                                    timestamp: new Date().getTime(),
                                    copiedTasks: []
                                };
                            }

                            if (!dateData[tomorrowKey].copiedTasks) {
                                dateData[tomorrowKey].copiedTasks = [];
                            }

                            dateData[tomorrowKey].copiedTasks.push(taskData);

                            // 标记这个任务已经复制过
                            copiedTasksToday.add(taskId);

                            // 为已复制的任务添加视觉标记
                            taskCard.classList.add('task-copied');

                            copiedCount++;
                        }
                    });

                    // 保存数据
                    saveCurrentDateData();

                    // 只在有跳过的任务时显示提示
                    if (skippedCount > 0) {
                        alert(`已复制 ${copiedCount} 个任务，跳过 ${skippedCount} 个已复制的任务`);
                    }

                    // 退出批量复制模式
                    exitBatchCopyMode();

                    console.log(`批量复制了 ${copiedCount} 个任务到明天，跳过了 ${skippedCount} 个已复制的任务`);

                } catch (error) {
                    console.error('批量复制任务时出错:', error);
                    alert('批量复制任务时出错: ' + error.message);
                }
            }

            // 应用程序设置
            const appSettings = {
                timeSlotDuration: 1, // 默认1小时
                maxDataDays: 7,      // 默认保存7天
                autoSyncData: true,   // 默认自动同步
                
                // 加载设置
                loadSettings: function() {
                    const savedSettings = localStorage.getItem('dailyTimeSettings');
                    if (savedSettings) {
                        const parsed = JSON.parse(savedSettings);
                        this.timeSlotDuration = parsed.timeSlotDuration || 1;
                        this.maxDataDays = parsed.maxDataDays || 7;
                        this.autoSyncData = parsed.autoSyncData !== undefined ? parsed.autoSyncData : true;
                    }
                    
                    // 更新设置表单
                    document.getElementById('timeSlotDuration').value = this.timeSlotDuration;
                    document.getElementById('maxDataDays').value = this.maxDataDays;
                    document.getElementById('autoSyncData').checked = this.autoSyncData;
                },
                
                // 保存设置
                saveSettings: function() {
                    localStorage.setItem('dailyTimeSettings', JSON.stringify({
                        timeSlotDuration: this.timeSlotDuration,
                        maxDataDays: this.maxDataDays,
                        autoSyncData: this.autoSyncData
                    }));
                }
            };
            
            // 加载保存的设置
            appSettings.loadSettings();
            
            // 日期控制功能
            const currentDateEl = document.getElementById('currentDate');
            let currentDate = new Date();
            
            // 存储不同日期的数据
            const dateData = {};
            
            // 更新显示的日期
            function updateDateDisplay() {
                const year = currentDate.getFullYear();
                const month = currentDate.getMonth() + 1;
                const day = currentDate.getDate();
                currentDateEl.textContent = `${year}年${month}月${day}日`;
            }
            
            // 保存当前日期的数据
            function saveCurrentDateData() {
                // 创建日期键
                const dateKey = formatDateKey(currentDate);
                
                // 获取时间线容器中的所有内容
                const timelineContainer = document.getElementById('timelineContainer');
                const timelineData = {
                    html: timelineContainer.innerHTML,
                    timestamp: new Date().getTime()
                };
                
                // 保存到日期数据对象
                dateData[dateKey] = timelineData;
                console.log('保存日期数据:', dateKey, timelineData);
                
                // 清理过期数据
                cleanupOldData();
                
                // 如果启用了自动同步，则同步到主系统
                if (appSettings.autoSyncData) {
                    syncToMainSystem();
                }
            }
            
            // 清理过期数据
            function cleanupOldData() {
                // 获取所有日期键并排序，从最早到最新
                const keys = Object.keys(dateData).sort((a, b) => {
                    return dateData[a].timestamp - dateData[b].timestamp;
                });
                
                // 如果数据天数超过设置的最大值，删除最早的数据
                while (keys.length > appSettings.maxDataDays) {
                    const oldestKey = keys.shift();
                    console.log('删除过期数据:', oldestKey);
                    delete dateData[oldestKey];
                }
            }
            
            // 加载指定日期的数据
            function loadDateData(date) {
                // 重置复制状态（切换日期时清空已复制任务记录）
                copiedTasksToday.clear();

                // 创建日期键
                const dateKey = formatDateKey(date);
                console.log('开始加载日期数据:', dateKey);

                // 获取时间线容器
                const timelineContainer = document.getElementById('timelineContainer');
                
                // 检查是否有该日期的数据
                if (dateData[dateKey]) {
                    // 加载保存的HTML
                    timelineContainer.innerHTML = dateData[dateKey].html;
                    console.log('从内存加载日期数据:', dateKey);
                    
                    // 使用统一的初始化函数重新绑定所有事件
                    initializeAllBindings();

                    // 处理复制的任务
                    processCopiedTasks(dateKey);

                    // 恢复已复制任务的状态
                    restoreCopiedTasksStatus();
                } else {
                    // 尝试从主系统获取数据
                    const mainSystemData = getDataFromMainSystem(dateKey);

                    if (mainSystemData) {
                        // 从主系统加载数据
                        timelineContainer.innerHTML = mainSystemData.html;
                        dateData[dateKey] = mainSystemData;

                        // 重新绑定事件
                        bindAddTaskButtons();
                        bindTimeSlotEditEvents();
                        bindDeleteTimeSlotButtons();
                        bindTaskContentEditEvents();
                        bindAllTaskButtons(); // 新增：直接绑定所有任务按钮的点击事件

                        // 重新观察任务卡片以添加动画效果
                        if (typeof observeTaskCards === 'function') {
                            try {
                                console.log('从主系统加载数据后重新观察任务卡片');
                                setTimeout(function() {
                                    try {
                                        observeTaskCards();
                                        console.log('任务卡片重新观察成功');
                                    } catch (error) {
                                        console.error('延迟重新观察任务卡片时出错:', error);
                                    }
                                }, 100);
                            } catch (error) {
                                console.error('设置任务卡片重新观察超时时出错:', error);
                            }
                        } else {
                            console.warn('观察函数未定义，跳过任务卡片重新观察');
                        }

                        console.log('从主系统加载数据:', dateKey);

                        // 处理复制的任务
                        processCopiedTasks(dateKey);

                        // 恢复已复制任务的状态
                        restoreCopiedTasksStatus();
                    } else {
                        // 没有数据，显示空的时间线
                        timelineContainer.innerHTML = `
                            <div class="timeline-vertical-line"></div>
                            <div class="timeline-connector"></div>
                        `;
                        console.log('创建新日期数据:', dateKey);

                        // 处理复制的任务
                        processCopiedTasks(dateKey);

                        // 恢复已复制任务的状态
                        restoreCopiedTasksStatus();
                    }
                }
            }

            // 处理复制的任务
            function processCopiedTasks(dateKey) {
                if (!dateData[dateKey] || !dateData[dateKey].copiedTasks) {
                    return;
                }

                const copiedTasks = dateData[dateKey].copiedTasks;
                if (copiedTasks.length === 0) {
                    return;
                }

                console.log('处理复制的任务:', copiedTasks.length, '个任务');

                copiedTasks.forEach(taskData => {
                    // 尝试找到匹配的时间段
                    let targetTimeSlot = null;
                    const timeSlots = document.querySelectorAll('.timeline-item');

                    // 首先尝试找到相同时间段文本的时间段
                    for (let slot of timeSlots) {
                        const slotHeader = slot.querySelector('.timeline-item-header h5');
                        if (slotHeader && slotHeader.textContent.trim() === taskData.timeSlotText) {
                            targetTimeSlot = slot;
                            break;
                        }
                    }

                    // 如果没找到匹配的时间段，创建一个新的时间段
                    if (!targetTimeSlot && taskData.timeSlotText) {
                        // 解析时间段文本，例如 "9:00 - 10:00"
                        const timeMatch = taskData.timeSlotText.match(/(\d+):(\d+)\s*-\s*(\d+):(\d+)/);
                        if (timeMatch) {
                            const startHour = parseInt(timeMatch[1], 10);
                            const endHour = parseInt(timeMatch[3], 10);
                            const duration = endHour - startHour;

                            // 创建新时间段
                            const timeSlotId = 'timeslot-copied-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
                            const timeSlotHtml = `
                                <div class="timeline-unit" data-hour="${startHour}" data-duration="${duration}">
                                    <div class="timeline-time">${startHour}:00</div>
                                    <div class="timeline-dot"></div>
                                    <div class="timeline-item" data-id="${timeSlotId}">
                                        <div class="timeline-item-header d-flex justify-content-between align-items-center">
                                            <h5 class="mb-0">
                                                <span class="time-slot-start" contenteditable="true">${startHour}:00</span> -
                                                <span class="time-slot-end" contenteditable="true">${endHour}:00</span>
                                            </h5>
                                            <div class="timeline-item-actions">
                                                <button class="btn btn-sm btn-outline-primary add-task-btn">
                                                    <i class="fas fa-plus"></i> 添加任务
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger delete-timeslot-btn ml-2">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="task-list mt-3">
                                        </div>
                                    </div>
                                </div>
                            `;

                            const timelineContainer = document.getElementById('timelineContainer');
                            const tempDiv = document.createElement('div');
                            tempDiv.innerHTML = timeSlotHtml.trim();
                            const newTimeSlot = tempDiv.firstChild;
                            timelineContainer.appendChild(newTimeSlot);
                            targetTimeSlot = newTimeSlot.querySelector('.timeline-item');

                            console.log('为复制的任务创建了新时间段:', taskData.timeSlotText);
                        }
                    }

                    // 如果还是没有目标时间段，使用第一个可用的时间段
                    if (!targetTimeSlot) {
                        const timeSlots = document.querySelectorAll('.timeline-item');
                        if (timeSlots.length > 0) {
                            targetTimeSlot = timeSlots[0];
                        }
                    }

                    // 创建任务并添加到目标时间段
                    if (targetTimeSlot) {
                        const taskList = targetTimeSlot.querySelector('.task-list');
                        if (taskList) {
                            createSingleTask(taskData.title, taskData.description, taskData.priority, taskList);
                            console.log('已添加复制的任务:', taskData.title);
                        }
                    }
                });

                // 清空已处理的复制任务
                dateData[dateKey].copiedTasks = [];

                // 重新绑定事件和更新统计
                setTimeout(() => {
                    initializeAllBindings();
                    updateTimelineStats();
                    saveCurrentDateData();
                }, 100);
            }

            // 与主系统同步数据
            function syncToMainSystem() {
                // 这里实现与主系统的数据同步逻辑
                console.log('同步数据到主系统:', Object.keys(dateData).length + ' 天的数据');
                // 实际应用中可能需要使用AJAX或其他方式发送数据到服务器
                
                // 示例: 将数据保存到localStorage作为演示
                localStorage.setItem('mainSystemTimelineData', JSON.stringify(dateData));
            }
            
            // 从主系统获取数据
            function getDataFromMainSystem(dateKey) {
                // 这里实现从主系统获取数据的逻辑
                console.log('从主系统请求数据:', dateKey);
                // 实际应用中可能需要使用AJAX或其他方式从服务器获取数据
                
                // 示例: 从localStorage获取数据作为演示
                const mainData = localStorage.getItem('mainSystemTimelineData');
                if (mainData) {
                    const parsed = JSON.parse(mainData);
                    return parsed[dateKey];
                }
                
                return null;
            }
            
            // 格式化日期键
            function formatDateKey(date) {
                const year = date.getFullYear();
                const month = date.getMonth() + 1;
                const day = date.getDate();
                return `${year}-${month}-${day}`;
            }
            
            // 更新统计信息显示
            function updateTimelineStats() {
                const timeSlots = document.querySelectorAll('.timeline-unit').length;
                const allTasks = document.querySelectorAll('.task-card').length;
                const completedTasks = document.querySelectorAll('.task-card.task-completed').length;

                // 更新统计显示
                document.getElementById('totalTimeSlots').textContent = `${timeSlots}个时间段`;
                document.getElementById('totalTasks').textContent = `${allTasks}个任务`;
                document.getElementById('completedTasks').textContent = `${completedTasks}个已完成`;

                // 显示或隐藏统计信息
                const statsElement = document.getElementById('timelineStats');
                if (timeSlots > 0 || allTasks > 0) {
                    statsElement.style.display = 'flex';
                } else {
                    statsElement.style.display = 'none';
                }

                console.log('统计信息已更新:', { timeSlots, allTasks, completedTasks });
            }

            // 任务完成切换函数（优化版）
            function toggleTaskComplete(taskCard, isComplete) {
                if (isComplete) {
                    taskCard.classList.add('task-completed');
                    taskCard.classList.add('task-just-completed');

                    // 显示取消完成按钮，隐藏完成按钮
                    const completeBtn = taskCard.querySelector('.complete-task-btn');
                    const uncompleteBtn = taskCard.querySelector('.uncomplete-task-btn');
                    if (completeBtn) completeBtn.style.display = 'none';
                    if (uncompleteBtn) uncompleteBtn.style.display = 'inline-block';

                    // 移除动画类
                    setTimeout(() => {
                        taskCard.classList.remove('task-just-completed');
                    }, 600);
                } else {
                    taskCard.classList.remove('task-completed');

                    // 显示完成按钮，隐藏取消完成按钮
                    const completeBtn = taskCard.querySelector('.complete-task-btn');
                    const uncompleteBtn = taskCard.querySelector('.uncomplete-task-btn');
                    if (completeBtn) completeBtn.style.display = 'inline-block';
                    if (uncompleteBtn) uncompleteBtn.style.display = 'none';
                }

                // 更新统计信息
                updateTimelineStats();

                // 保存数据
                saveCurrentDateData();
            }

            // 初始更新日期显示和加载数据
            updateDateDisplay();
            loadDateData(currentDate);

            // 初始更新统计信息
            setTimeout(() => {
                updateTimelineStats();
            }, 100);
            
            // 前一天按钮点击事件
            document.getElementById('prevDateBtn').addEventListener('click', function() {
                // 保存当前日期数据
                saveCurrentDateData();
                
                // 切换到前一天
                currentDate.setDate(currentDate.getDate() - 1);
                updateDateDisplay();
                
                // 加载对应日期的数据
                loadDateData(currentDate);

                // 初始化所有绑定和更新统计
                setTimeout(() => {
                    initializeAllBindings();
                    updateTimelineStats();
                }, 100);
            });
            
            // 后一天按钮点击事件
            document.getElementById('nextDateBtn').addEventListener('click', function() {
                // 保存当前日期数据
                saveCurrentDateData();
                
                // 切换到后一天
                currentDate.setDate(currentDate.getDate() + 1);
                updateDateDisplay();
                
                // 加载对应日期的数据
                loadDateData(currentDate);

                // 初始化所有绑定和更新统计
                setTimeout(() => {
                    initializeAllBindings();
                    updateTimelineStats();
                }, 100);
            });
            
            // 绑定所有添加任务按钮的点击事件
            function bindAddTaskButtons() {
                console.log('绑定添加任务按钮...');
                const addTaskBtns = document.querySelectorAll('.add-task-btn');
                addTaskBtns.forEach(btn => {
                    // 移除已有的点击事件监听器
                    btn.removeEventListener('click', handleAddTaskClick);
                    // 添加新的点击事件监听器
                    btn.addEventListener('click', handleAddTaskClick);
                });
                console.log(`已绑定 ${addTaskBtns.length} 个添加任务按钮`);
            }
            
            // 添加任务按钮点击处理函数
            function handleAddTaskClick() {
                console.log('添加任务按钮点击');
                // 获取相关的时间段ID
                const timeSlotEl = this.closest('.timeline-item');
                const timeSlotId = timeSlotEl ? timeSlotEl.dataset.id || '' : ''; // 获取时间段ID，如果没有则为空字符串
                
                // 设置表单隐藏字段
                document.getElementById('timeSlotId').value = timeSlotId;
                document.getElementById('taskPositionIndex').value = ''; // 默认添加到末尾
                
                // 重置表单
                document.getElementById('addTaskForm').reset();
                
                // 显示模态框
                addTaskModal.show();
                console.log('已打开添加任务模态框，时间段ID:', timeSlotId);
            }
            
            // 初始绑定添加任务按钮
            bindAddTaskButtons();
            
            // 改进删除时间段按钮绑定
            function bindDeleteTimeSlotButtons() {
                const deleteTimeSlotBtns = document.querySelectorAll('.delete-timeslot-btn');
                deleteTimeSlotBtns.forEach(btn => {
                    // 移除已有的所有事件监听器
                    const newBtn = btn.cloneNode(true);
                    btn.parentNode.replaceChild(newBtn, btn);
                    
                    // 添加新的事件监听器，使用 onclick 直接绑定
                    newBtn.onclick = function(e) {
                        e.stopPropagation(); // 防止事件冒泡
                        // 直接删除，不显示确认弹窗
                        const timelineUnit = this.closest('.timeline-unit');
                        if (timelineUnit) {
                            timelineUnit.remove();
                            // 更新统计信息
                            updateTimelineStats();
                            // 保存数据
                            saveCurrentDateData();
                        }
                    };
                });
            }
            
            // 绑定时间段编辑事件
            function bindTimeSlotEditEvents() {
                const timeSlotStarts = document.querySelectorAll('.time-slot-start');
                const timeSlotEnds = document.querySelectorAll('.time-slot-end');
                
                // 时间段开始时间编辑事件
                timeSlotStarts.forEach(el => {
                    el.addEventListener('blur', function() {
                        updateTimelineUnitHour(this);
                    });
                    el.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            this.blur();
                        }
                    });
                });
                
                // 时间段结束时间编辑事件
                timeSlotEnds.forEach(el => {
                    el.addEventListener('blur', function() {
                        // 结束时间不会影响timeline-unit的data-hour
                    });
                    el.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            this.blur();
                        }
                    });
                });
            }
            
            // 更新时间线单元的小时属性
            function updateTimelineUnitHour(startTimeEl) {
                const timeValue = startTimeEl.textContent.trim();
                // 简单提取小时部分
                const hour = parseInt(timeValue.split(':')[0], 10);
                
                if (!isNaN(hour)) {
                    const timelineUnit = startTimeEl.closest('.timeline-unit');
                    if (timelineUnit) {
                        // 获取结束时间元素
                        const endTimeEl = timelineUnit.querySelector('.time-slot-end');
                        if (endTimeEl) {
                            // 获取当前设置的时间段时长
                            const duration = parseInt(timelineUnit.dataset.duration || 1, 10);
                            
                            // 更新结束时间
                            const endHour = (hour + duration) % 24;
                            endTimeEl.textContent = `${endHour}:00`;
                        }
                        
                        // 更新data-hour属性
                        timelineUnit.dataset.hour = hour;
                        // 更新时间标签显示
                        const timeLabel = timelineUnit.querySelector('.timeline-time');
                        if (timeLabel) {
                            timeLabel.textContent = `${hour}:00`;
                        }
                        
                        // 可能还需要根据小时重新排序时间线单元
                        reorderTimelineUnits();
                    }
                }
            }
            
            // 根据data-hour属性重新排序时间线单元
            function reorderTimelineUnits() {
                const timelineContainer = document.getElementById('timelineContainer');
                const timeUnits = Array.from(document.querySelectorAll('.timeline-unit'));
                
                // 按小时排序
                timeUnits.sort((a, b) => {
                    const hourA = parseInt(a.dataset.hour, 10);
                    const hourB = parseInt(b.dataset.hour, 10);
                    return hourA - hourB;
                });
                
                // 保存垂直线和连接器
                const verticalLine = document.querySelector('.timeline-vertical-line');
                const connector = document.querySelector('.timeline-connector');
                
                // 清空容器
                timelineContainer.innerHTML = '';
                
                // 重新添加垂直线和连接器
                if (verticalLine) timelineContainer.appendChild(verticalLine);
                if (connector) timelineContainer.appendChild(connector);
                
                // 按排序重新添加所有时间单元
                timeUnits.forEach(unit => {
                    timelineContainer.appendChild(unit);
                });
            }
            
            // 初始绑定时间段编辑事件
            bindTimeSlotEditEvents();
            
            // 绑定任务内容双击编辑事件
            function bindTaskContentEditEvents() {
                // 获取所有任务标题和描述
                const taskTitles = document.querySelectorAll('.task-title');
                const taskDescriptions = document.querySelectorAll('.task-description');
                
                // 为标题添加双击编辑功能
                taskTitles.forEach(el => {
                    // 移除已有的事件监听器，避免重复绑定
                    el.removeEventListener('dblclick', handleTaskTitleDblClick);
                    el.addEventListener('dblclick', handleTaskTitleDblClick);
                });
                
                // 为描述添加双击编辑功能
                taskDescriptions.forEach(el => {
                    // 移除已有的事件监听器，避免重复绑定
                    el.removeEventListener('dblclick', handleTaskDescriptionDblClick);
                    el.addEventListener('dblclick', handleTaskDescriptionDblClick);
                });
            }
            
            // 处理任务标题双击事件
            function handleTaskTitleDblClick(event) {
                // 检查任务是否已完成
                if (this.closest('.task-card').classList.contains('task-completed')) {
                    return; // 已完成的任务不允许编辑
                }
                
                // 设置为可编辑状态
                this.setAttribute('contenteditable', 'true');
                this.focus();
                
                // 添加失去焦点和按键事件
                this.addEventListener('blur', handleTaskContentBlur);
                this.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.blur(); // Enter键触发blur事件
                    }
                });
            }
            
            // 处理任务描述双击事件
            function handleTaskDescriptionDblClick(event) {
                // 检查任务是否已完成
                if (this.closest('.task-card').classList.contains('task-completed')) {
                    return; // 已完成的任务不允许编辑
                }
                
                // 设置为可编辑状态
                this.setAttribute('contenteditable', 'true');
                this.focus();
                
                // 添加失去焦点事件
                this.addEventListener('blur', handleTaskContentBlur);
                // 对于描述，我们允许多行输入，但Ctrl+Enter或Escape键保存
                this.addEventListener('keydown', function(e) {
                    if ((e.ctrlKey && e.key === 'Enter') || e.key === 'Escape') {
                        e.preventDefault();
                        this.blur(); // 触发blur事件
                    }
                });
            }
            
            // 处理任务内容失去焦点事件
            function handleTaskContentBlur(event) {
                // 移除contenteditable属性
                this.removeAttribute('contenteditable');
                // 移除自身的blur监听器以防止内存泄漏
                this.removeEventListener('blur', handleTaskContentBlur);
            }
            
            // 切换任务完成状态
            function toggleTaskComplete(taskCard, isComplete) {
                if (!taskCard) return;
                
                if (isComplete) {
                    // 标记为已完成
                    taskCard.classList.add('task-completed');
                    
                    // 显示"撤销完成"按钮，隐藏"完成"按钮
                    const completeBtn = taskCard.querySelector('.complete-task-btn');
                    const uncompleteBtn = taskCard.querySelector('.uncomplete-task-btn');
                    
                    if (completeBtn) completeBtn.style.display = 'none';
                    if (uncompleteBtn) uncompleteBtn.style.display = 'inline-block';
                    
                    // 记录日志
                    console.log("任务已标记为完成状态");
                } else {
                    // 标记为未完成
                    taskCard.classList.remove('task-completed');
                    
                    // 显示"完成"按钮，隐藏"撤销完成"按钮
                    const completeBtn = taskCard.querySelector('.complete-task-btn');
                    const uncompleteBtn = taskCard.querySelector('.uncomplete-task-btn');
                    
                    if (completeBtn) completeBtn.style.display = 'inline-block';
                    if (uncompleteBtn) uncompleteBtn.style.display = 'none';
                    
                    // 记录日志
                    console.log("任务已恢复为未完成状态");
                }
                
                // 保存状态
                saveCurrentDateData();
            }
            
            // 显示任务完成提示框
            function showTaskCompleteToast(taskTitle) {
                // 创建提示元素
                const toast = document.createElement('div');
                toast.className = 'task-complete-toast';
                toast.innerHTML = `
                    <i class="fas fa-check-circle"></i>
                    <span>已完成: ${taskTitle || '任务'}</span>
                `;
                
                // 添加到页面
                document.body.appendChild(toast);
                
                // 动画结束后移除元素
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 2500);
            }
            
            // 注释：重复的保存任务按钮绑定代码已删除，统一使用 bindSaveTaskButton() 函数处理
            
            // 重新绑定批量复制按钮点击事件
            const batchCopyBtn = document.getElementById('batchCopyBtn');
            if (batchCopyBtn) {
                // 先移除所有已有的事件监听器
                const newBatchCopyBtn = batchCopyBtn.cloneNode(true);
                batchCopyBtn.parentNode.replaceChild(newBatchCopyBtn, batchCopyBtn);

                // 添加新的事件监听器
                newBatchCopyBtn.addEventListener('click', function() {
                    console.log('批量复制按钮点击');
                    if (batchCopyMode) {
                        exitBatchCopyMode();
                    } else {
                        enterBatchCopyMode();
                    }
                });
            }

            // 重新绑定设置按钮点击事件
            const settingsBtn = document.getElementById('settingsBtn');
            if (settingsBtn) {
                // 先移除所有已有的事件监听器
                const newSettingsBtn = settingsBtn.cloneNode(true);
                settingsBtn.parentNode.replaceChild(newSettingsBtn, settingsBtn);

                // 添加新的事件监听器
                newSettingsBtn.addEventListener('click', function() {
                    console.log('设置按钮点击');
                // 显示设置模态框
                settingsModal.show();
            });
            }

            // 绑定批量复制控制栏按钮事件
            const selectAllCheckbox = document.getElementById('selectAllTasks');
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    const taskCards = document.querySelectorAll('.task-card');
                    const checkboxes = document.querySelectorAll('.task-checkbox');

                    if (this.checked) {
                        // 全选
                        selectedTasks.clear();
                        taskCards.forEach(taskCard => {
                            selectedTasks.add(taskCard.dataset.taskId);
                        });
                        checkboxes.forEach(checkbox => {
                            checkbox.checked = true;
                        });
                    } else {
                        // 取消全选
                        selectedTasks.clear();
                        checkboxes.forEach(checkbox => {
                            checkbox.checked = false;
                        });
                    }
                    updateSelectedCount();
                });
            }

            const copySelectedBtn = document.getElementById('copySelectedBtn');
            if (copySelectedBtn) {
                copySelectedBtn.addEventListener('click', function() {
                    copySelectedTasksToTomorrow();
                });
            }

            const cancelBatchCopyBtn = document.getElementById('cancelBatchCopyBtn');
            if (cancelBatchCopyBtn) {
                cancelBatchCopyBtn.addEventListener('click', function() {
                    exitBatchCopyMode();
                });
            }

            // 重新绑定保存设置按钮点击事件
            const saveSettingsBtn = document.getElementById('saveSettingsBtn');
            if (saveSettingsBtn) {
                // 先移除所有已有的事件监听器
                const newSaveSettingsBtn = saveSettingsBtn.cloneNode(true);
                saveSettingsBtn.parentNode.replaceChild(newSaveSettingsBtn, saveSettingsBtn);

                // 添加新的事件监听器
                newSaveSettingsBtn.addEventListener('click', function() {
                    // 获取表单值并保存设置
                const timeSlotDuration = document.getElementById('timeSlotDuration').value;
                const maxDataDays = document.getElementById('maxDataDays').value;
                const autoSyncData = document.getElementById('autoSyncData').checked;
                
                // 更新设置
                appSettings.timeSlotDuration = timeSlotDuration;
                appSettings.maxDataDays = maxDataDays;
                appSettings.autoSyncData = autoSyncData;
                
                // 保存设置
                appSettings.saveSettings();
                
                // 清理过期数据
                cleanupOldData();
                
                // 如果开启了自动同步，立即同步
                if (autoSyncData) {
                    syncToMainSystem();
                }
                
                // 关闭模态框
                settingsModal.hide();
                
                console.log('保存设置:', { timeSlotDuration, maxDataDays, autoSyncData });
            });
            }

            // 重新绑定清空当前时间线数据按钮点击事件
            const clearCurrentTimelineBtn = document.getElementById('clearCurrentTimelineBtn');
            if (clearCurrentTimelineBtn) {
                // 先移除所有已有的事件监听器
                const newClearCurrentTimelineBtn = clearCurrentTimelineBtn.cloneNode(true);
                clearCurrentTimelineBtn.parentNode.replaceChild(newClearCurrentTimelineBtn, clearCurrentTimelineBtn);
                
                // 添加新的事件监听器
                newClearCurrentTimelineBtn.addEventListener('click', function() {
                if (confirm('确定要清空当前日期的所有时间段和任务数据吗？此操作无法撤销！')) {
                    // 获取时间线容器
                    const timelineContainer = document.getElementById('timelineContainer');
                    
                    // 清空时间线，只保留垂直线和连接器
                    timelineContainer.innerHTML = `
                        <div class="timeline-vertical-line"></div>
                        <div class="timeline-connector"></div>
                    `;
                    
                    // 从存储中删除当前日期的数据
                    const dateKey = formatDateKey(currentDate);
                    delete dateData[dateKey];
                    
                    // 如果设置了自动同步，则同步到主系统
                    if (appSettings.autoSyncData) {
                        syncToMainSystem();
                    }
                    
                    // 关闭模态框
                    settingsModal.hide();
                    
                    // 更新统计信息
                    updateTimelineStats();

                    // 显示成功消息
                    alert('当前时间线数据已清空');
                }
            });
            }
            
            // 在页面卸载前保存数据
            window.addEventListener('beforeunload', function() {
                saveCurrentDateData();
                if (appSettings.autoSyncData) {
                    syncToMainSystem();
                }
            });
            
            // 初始绑定任务内容编辑事件
            bindTaskContentEditEvents();

            // 确保所有任务操作按钮都能正确响应点击事件
            function bindAllTaskButtons() {
                // 为所有完成任务按钮添加直接点击事件
                document.querySelectorAll('.complete-task-btn').forEach(btn => {
                    btn.onclick = function(e) {
                        e.stopPropagation(); // 防止事件冒泡
                        const taskCard = this.closest('.task-card');
                        if (taskCard) {
                            toggleTaskComplete(taskCard, true);
                        }
                    };
                });
                
                // 为所有撤销完成按钮添加直接点击事件
                document.querySelectorAll('.uncomplete-task-btn').forEach(btn => {
                    btn.onclick = function(e) {
                        e.stopPropagation(); // 防止事件冒泡
                        const taskCard = this.closest('.task-card');
                        if (taskCard) {
                            toggleTaskComplete(taskCard, false);
                            console.log("撤销完成状态 - 直接点击"); // 调试输出
                        }
                    };
                });
                


                // 为所有删除任务按钮添加直接点击事件
                document.querySelectorAll('.delete-task-btn').forEach(btn => {
                    btn.onclick = function(e) {
                        e.stopPropagation(); // 防止事件冒泡
                        const taskCard = this.closest('.task-card');
                        if (taskCard) {
                            taskCard.remove();
                            updateTimelineStats();
                            saveCurrentDateData();
                        }
                    };
                });
            }

            // 在初始化时调用所有绑定函数
            function initializeAllBindings() {
                console.log('开始初始化所有绑定...');
                
                // 重新绑定时间线容器的点击事件委托
                const timelineContainer = document.getElementById('timelineContainer');
                if (timelineContainer) {
                    // 先移除所有已有的事件监听器
                    const newTimelineContainer = timelineContainer.cloneNode(true);
                    timelineContainer.parentNode.replaceChild(newTimelineContainer, timelineContainer);
                    
                    // 添加点击事件委托
                    newTimelineContainer.addEventListener('click', function(event) {
                        // 查找触发事件的元素及其父元素中是否有所需的按钮类
                        let completeBtn = event.target.closest('.complete-task-btn');
                        let uncompleteBtn = event.target.closest('.uncomplete-task-btn');
                        let editBtn = event.target.closest('.edit-task-btn');

                        let deleteBtn = event.target.closest('.delete-task-btn');
                        
                        // 处理完成按钮点击
                        if (completeBtn) {
                            event.stopPropagation(); // 防止事件冒泡
                            const taskCard = completeBtn.closest('.task-card');
                            if (taskCard) {
                                toggleTaskComplete(taskCard, true);
                                console.log('委托事件: 完成按钮点击');
                            }
                        }
                        
                        // 处理撤销完成按钮点击
                        else if (uncompleteBtn) {
                            event.stopPropagation(); // 防止事件冒泡
                            const taskCard = uncompleteBtn.closest('.task-card');
                            if (taskCard) {
                                toggleTaskComplete(taskCard, false);
                                console.log('委托事件: 撤销完成按钮点击');
                            }
                        }

                        // 处理编辑任务按钮点击
                        else if (editBtn) {
                            event.stopPropagation(); // 防止事件冒泡
                            const taskCard = editBtn.closest('.task-card');
                            if (taskCard) {
                                openEditTaskModal(taskCard);
                                console.log('委托事件: 编辑任务按钮点击');
                            }
                        }



                        // 处理删除任务按钮点击
                        else if (deleteBtn) {
                            event.stopPropagation(); // 防止事件冒泡
                            const taskCard = deleteBtn.closest('.task-card');
                            if (taskCard) {
                                taskCard.remove();
                                updateTimelineStats();
                                saveCurrentDateData();
                                console.log('委托事件: 删除任务按钮点击');
                            }
                        }
                    });
                }
                
                // 重新绑定批量复制按钮点击事件
                const batchCopyBtn = document.getElementById('batchCopyBtn');
                if (batchCopyBtn) {
                    // 先移除所有已有的事件监听器
                    const newBatchCopyBtn = batchCopyBtn.cloneNode(true);
                    batchCopyBtn.parentNode.replaceChild(newBatchCopyBtn, batchCopyBtn);

                    // 添加新的事件监听器
                    newBatchCopyBtn.addEventListener('click', function() {
                        console.log('批量复制按钮点击');
                        if (batchCopyMode) {
                            exitBatchCopyMode();
                        } else {
                            enterBatchCopyMode();
                        }
                    });
                }

                // 重新绑定设置按钮点击事件
                const settingsBtn = document.getElementById('settingsBtn');
                if (settingsBtn) {
                    // 先移除所有已有的事件监听器
                    const newSettingsBtn = settingsBtn.cloneNode(true);
                    settingsBtn.parentNode.replaceChild(newSettingsBtn, settingsBtn);

                    // 添加新的事件监听器
                    newSettingsBtn.addEventListener('click', function() {
                        console.log('设置按钮点击');
                        // 显示设置模态框
                        settingsModal.show();
                    });
                }

                // 重新绑定保存设置按钮点击事件
                const saveSettingsBtn = document.getElementById('saveSettingsBtn');
                if (saveSettingsBtn) {
                    // 先移除所有已有的事件监听器
                    const newSaveSettingsBtn = saveSettingsBtn.cloneNode(true);
                    saveSettingsBtn.parentNode.replaceChild(newSaveSettingsBtn, saveSettingsBtn);
                    
                    // 添加新的事件监听器
                    newSaveSettingsBtn.addEventListener('click', function() {
                        // 获取表单值并保存设置
                        const timeSlotDuration = document.getElementById('timeSlotDuration').value;
                        const maxDataDays = document.getElementById('maxDataDays').value;
                        const autoSyncData = document.getElementById('autoSyncData').checked;
                        
                        // 更新设置
                        appSettings.timeSlotDuration = timeSlotDuration;
                        appSettings.maxDataDays = maxDataDays;
                        appSettings.autoSyncData = autoSyncData;
                        
                        // 保存设置
                        appSettings.saveSettings();
                        
                        // 清理过期数据
                        cleanupOldData();
                        
                        // 如果开启了自动同步，立即同步
                        if (autoSyncData) {
                            syncToMainSystem();
                        }
                        
                        // 关闭模态框
                        settingsModal.hide();
                        
                        console.log('保存设置:', { timeSlotDuration, maxDataDays, autoSyncData });
                    });
                }

                // 绑定批量复制控制栏按钮事件
                const selectAllCheckbox = document.getElementById('selectAllTasks');
                if (selectAllCheckbox) {
                    // 先移除已有的事件监听器
                    const newSelectAllCheckbox = selectAllCheckbox.cloneNode(true);
                    selectAllCheckbox.parentNode.replaceChild(newSelectAllCheckbox, selectAllCheckbox);

                    newSelectAllCheckbox.addEventListener('change', function() {
                        const taskCards = document.querySelectorAll('.task-card');
                        const checkboxes = document.querySelectorAll('.task-checkbox');

                        if (this.checked) {
                            // 全选
                            selectedTasks.clear();
                            taskCards.forEach(taskCard => {
                                selectedTasks.add(taskCard.dataset.taskId);
                            });
                            checkboxes.forEach(checkbox => {
                                checkbox.checked = true;
                            });
                        } else {
                            // 取消全选
                            selectedTasks.clear();
                            checkboxes.forEach(checkbox => {
                                checkbox.checked = false;
                            });
                        }
                        updateSelectedCount();
                    });
                }

                const copySelectedBtn = document.getElementById('copySelectedBtn');
                if (copySelectedBtn) {
                    // 先移除已有的事件监听器
                    const newCopySelectedBtn = copySelectedBtn.cloneNode(true);
                    copySelectedBtn.parentNode.replaceChild(newCopySelectedBtn, copySelectedBtn);

                    newCopySelectedBtn.addEventListener('click', function() {
                        copySelectedTasksToTomorrow();
                    });
                }

                const cancelBatchCopyBtn = document.getElementById('cancelBatchCopyBtn');
                if (cancelBatchCopyBtn) {
                    // 先移除已有的事件监听器
                    const newCancelBatchCopyBtn = cancelBatchCopyBtn.cloneNode(true);
                    cancelBatchCopyBtn.parentNode.replaceChild(newCancelBatchCopyBtn, cancelBatchCopyBtn);

                    newCancelBatchCopyBtn.addEventListener('click', function() {
                        exitBatchCopyMode();
                    });
                }

                // 绑定添加时间段按钮
                bindAddTimeSlotButton();

                // 绑定其他所有事件
                bindAddTaskButtons();
                bindTimeSlotEditEvents();
                bindDeleteTimeSlotButtons();
                bindTaskContentEditEvents();
                bindAllTaskButtons();

                // 绑定保存任务按钮 - 确保添加任务功能正常工作
                bindSaveTaskButton();
                
                // 如果使用了Intersection Observer，重新初始化
                if (typeof observeTaskCards === 'function') {
                    try {
                        console.log('初始化任务卡片观察器');
                        setTimeout(function() {
                            try {
                                observeTaskCards();
                                console.log('任务卡片观察器初始化成功');
                            } catch (error) {
                                console.error('延迟初始化任务卡片观察器时出错:', error);
                            }
                        }, 100);
                    } catch (error) {
                        console.error('设置任务卡片观察器超时时出错:', error);
                    }
                } else {
                    console.warn('观察函数未定义，跳过初始化任务卡片观察器');
                }

                // 更新统计信息
                setTimeout(() => {
                    updateTimelineStats();
                }, 150);

                console.log('所有绑定初始化完成');
            }


            // 页面加载完成后调用初始化
            initializeAllBindings();
        });
    </script>
</body>

</html>