// 购物清单管理系统
const ShoppingList = {
    // 存储键名 - 合并到主系统
    STORAGE_KEY: 'savingsData',

    // 数据存储
    data: {
        categories: [],
        shoppingItems: []
    },
    
    // 编辑状态
    editingItemId: null,
    selectedCategoryId: null,
    
    // 初始化
    init() {
        this.loadData();
        this.bindEvents();
        this.updateDisplay();
        this.updateCategoriesDisplay();
        this.updateCategorySelect();
        this.updateStats();

        // 设置默认日期为今天（北京时间）
        const today = this.getBeiJingTime().split('T')[0];
        const deadlineInput = document.getElementById('itemDeadline');
        if (deadlineInput) {
            deadlineInput.value = today;
        }
    },
    
    // 绑定事件
    bindEvents() {
        // 添加分类事件
        const addCategoryBtn = document.getElementById('addCategoryBtn');
        if (addCategoryBtn) {
            addCategoryBtn.addEventListener('click', () => {
                this.addCategory();
            });
        }
        
        // 分类输入框回车事件
        const newCategoryName = document.getElementById('newCategoryName');
        if (newCategoryName) {
            newCategoryName.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.addCategory();
                }
            });
        }
        
        // 购物项目表单提交事件
        const shoppingForm = document.getElementById('shoppingForm');
        if (shoppingForm) {
            shoppingForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.addShoppingItem();
            });
        }
        
        // 视图筛选事件
        const viewFilters = document.querySelectorAll('input[name="viewFilter"]');
        viewFilters.forEach(filter => {
            filter.addEventListener('change', () => {
                this.updateDisplay();
            });
        });
    },
    
    // 加载数据 - 从主系统数据中加载
    loadData() {
        try {
            const savedData = localStorage.getItem(this.STORAGE_KEY);
            if (savedData) {
                const mainData = JSON.parse(savedData);
                // 从主系统数据中加载购物清单数据
                this.data.categories = mainData.shoppingCategories || [];
                this.data.shoppingItems = mainData.shoppingItems || [];
                console.log('购物清单数据加载成功');
            } else {
                // 初始化空数据
                this.data.categories = [];
                this.data.shoppingItems = [];
            }

            // 数据迁移：检查是否有旧的独立存储数据需要迁移
            this.migrateOldData();
        } catch (error) {
            console.error('加载购物清单数据失败:', error);
            // 初始化空数据
            this.data.categories = [];
            this.data.shoppingItems = [];
        }
    },

    // 迁移旧数据
    migrateOldData() {
        try {
            const oldData = localStorage.getItem('shoppingListData');
            if (oldData) {
                const parsedOldData = JSON.parse(oldData);

                // 如果主系统中没有购物清单数据，但旧存储中有，则迁移
                if ((!this.data.categories.length && !this.data.shoppingItems.length) &&
                    (parsedOldData.categories?.length || parsedOldData.shoppingItems?.length)) {

                    console.log('发现旧的购物清单数据，开始迁移...');
                    this.data.categories = parsedOldData.categories || [];
                    this.data.shoppingItems = parsedOldData.shoppingItems || [];

                    // 保存到主系统
                    this.saveData();

                    // 删除旧的独立存储
                    localStorage.removeItem('shoppingListData');
                    console.log('购物清单数据迁移完成');
                }
            }
        } catch (error) {
            console.error('迁移购物清单数据失败:', error);
        }
    },

    // 保存数据 - 合并到主系统数据中
    saveData() {
        try {
            // 获取主系统数据
            const savedData = localStorage.getItem(this.STORAGE_KEY);
            let mainData = savedData ? JSON.parse(savedData) : {};

            // 更新购物清单相关数据
            mainData.shoppingCategories = this.data.categories;
            mainData.shoppingItems = this.data.shoppingItems;

            // 保存回主系统
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(mainData));
            console.log('购物清单数据保存成功');

            // 触发数据更改事件
            const dataChangeEvent = new CustomEvent('appDataChanged', {
                detail: { source: 'shoppingList' }
            });
            window.dispatchEvent(dataChangeEvent);
        } catch (error) {
            console.error('保存购物清单数据失败:', error);
        }
    },
    
    // 添加分类
    addCategory() {
        const nameInput = document.getElementById('newCategoryName');
        const name = nameInput.value.trim();
        
        if (!name) {
            alert('请输入分类名称');
            return;
        }
        
        // 检查是否已存在
        if (this.data.categories.some(cat => cat.name === name)) {
            alert('分类已存在');
            return;
        }
        
        const category = {
            id: Date.now().toString(),
            name: name,
            color: this.getRandomColor()
        };
        
        this.data.categories.push(category);
        this.saveData();
        this.updateCategoriesDisplay();
        this.updateCategorySelect();
        
        // 清空输入框
        nameInput.value = '';
    },
    
    // 删除分类
    deleteCategory(categoryId) {
        if (confirm('确定要删除这个分类吗？该分类下的所有购物项目也会被删除。')) {
            // 删除分类下的所有购物项目
            this.data.shoppingItems = this.data.shoppingItems.filter(item => item.categoryId !== categoryId);
            // 删除分类
            this.data.categories = this.data.categories.filter(cat => cat.id !== categoryId);
            
            this.saveData();
            this.updateCategoriesDisplay();
            this.updateCategorySelect();
            this.updateDisplay();
            this.updateStats();
        }
    },
    
    // 添加购物项目
    addShoppingItem() {
        const categoryId = document.getElementById('itemCategory').value;
        const name = document.getElementById('itemName').value.trim();
        const quantity = document.getElementById('itemQuantity').value.trim();
        const budget = parseFloat(document.getElementById('itemBudget').value) || 0;
        const deadline = document.getElementById('itemDeadline').value;
        const note = document.getElementById('itemNote').value.trim();
        
        if (!categoryId) {
            alert('请选择分类');
            return;
        }
        
        if (!name) {
            alert('请输入物品名称');
            return;
        }
        
        if (!quantity) {
            alert('请输入数量');
            return;
        }
        
        const item = {
            id: this.editingItemId || Date.now().toString(),
            categoryId: categoryId,
            name: name,
            quantity: quantity,
            budget: budget,
            deadline: deadline,
            note: note,
            completed: false,
            completedAt: null,
            createdAt: this.editingItemId ? this.data.shoppingItems.find(item => item.id === this.editingItemId).createdAt : this.getBeiJingTime()
        };
        
        if (this.editingItemId) {
            // 编辑模式
            const index = this.data.shoppingItems.findIndex(item => item.id === this.editingItemId);
            if (index !== -1) {
                this.data.shoppingItems[index] = item;
            }
            this.editingItemId = null;
        } else {
            // 添加模式
            this.data.shoppingItems.unshift(item);
        }
        
        this.saveData();
        this.clearForm();
        this.updateSubmitButton(false);
        this.updateDisplay();
        this.updateStats();
    },
    
    // 编辑购物项目
    editItem(itemId) {
        const item = this.data.shoppingItems.find(item => item.id === itemId);
        if (!item) return;
        
        // 填充表单
        document.getElementById('itemCategory').value = item.categoryId;
        document.getElementById('itemName').value = item.name;
        document.getElementById('itemQuantity').value = item.quantity;
        document.getElementById('itemBudget').value = item.budget;
        document.getElementById('itemDeadline').value = item.deadline;
        document.getElementById('itemNote').value = item.note;
        
        this.editingItemId = itemId;
        this.updateSubmitButton(true);
        
        // 滚动到表单
        document.getElementById('shoppingForm').scrollIntoView({ behavior: 'smooth' });
    },
    
    // 删除购物项目
    deleteItem(itemId) {
        if (confirm('确定要删除这个购物项目吗？')) {
            this.data.shoppingItems = this.data.shoppingItems.filter(item => item.id !== itemId);
            this.saveData();
            this.updateDisplay();
            this.updateStats();
        }
    },
    
    // 切换完成状态
    toggleItemComplete(itemId) {
        const item = this.data.shoppingItems.find(item => item.id === itemId);
        if (item) {
            const wasCompleted = item.completed;
            item.completed = !item.completed;
            item.completedAt = item.completed ? this.getBeiJingTime() : null;

            // 添加动画效果
            const itemElement = document.querySelector(`[data-id="${itemId}"]`);
            if (itemElement) {
                if (item.completed) {
                    itemElement.classList.add('just-completed');
                    setTimeout(() => {
                        itemElement.classList.remove('just-completed');
                    }, 600);
                } else {
                    itemElement.classList.add('just-uncompleted');
                    setTimeout(() => {
                        itemElement.classList.remove('just-uncompleted');
                    }, 400);
                }
            }

            this.saveData();
            this.updateDisplay();
            this.updateStats();
        }
    },
    
    // 取消编辑
    cancelEdit() {
        this.editingItemId = null;
        this.clearForm();
        this.updateSubmitButton(false);
    },
    
    // 清空表单
    clearForm() {
        document.getElementById('itemCategory').value = '';
        document.getElementById('itemName').value = '';
        document.getElementById('itemQuantity').value = '';
        document.getElementById('itemBudget').value = '';
        document.getElementById('itemNote').value = '';
        
        // 重置日期为今天（北京时间）
        const today = this.getBeiJingTime().split('T')[0];
        document.getElementById('itemDeadline').value = today;
    },
    
    // 更新提交按钮状态
    updateSubmitButton(isEditing) {
        const submitBtn = document.getElementById('submitBtn');
        const submitIcon = document.getElementById('submitIcon');
        const submitText = document.getElementById('submitText');
        const cancelBtn = document.getElementById('cancelBtn');
        
        if (isEditing) {
            submitIcon.className = 'fas fa-save me-2';
            submitText.textContent = '保存修改';
            cancelBtn.style.display = 'block';
        } else {
            submitIcon.className = 'fas fa-plus me-2';
            submitText.textContent = '添加项目';
            cancelBtn.style.display = 'none';
        }
    },
    
    // 获取随机颜色
    getRandomColor() {
        const colors = ['#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6', '#1abc9c', '#34495e', '#e67e22'];
        return colors[Math.floor(Math.random() * colors.length)];
    },
    
    // 获取北京时间
    getBeiJingTime() {
        const now = new Date();
        const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
        const beijing = new Date(utc + (8 * 3600000));
        return beijing.toISOString();
    },
    
    // 格式化时间
    formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    },
    
    // 格式化日期
    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        const today = new Date(this.getBeiJingTime());
        const diffTime = date.getTime() - today.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays < 0) {
            return `已过期 ${Math.abs(diffDays)} 天`;
        } else if (diffDays === 0) {
            return '今天';
        } else if (diffDays === 1) {
            return '明天';
        } else {
            return `${diffDays} 天后`;
        }
    },

    // 更新分类显示
    updateCategoriesDisplay() {
        const container = document.getElementById('categoriesContainer');
        if (!container) return;

        if (this.data.categories.length === 0) {
            container.innerHTML = `
                <div class="text-muted text-center py-3">
                    <i class="fas fa-tags me-2"></i>暂无分类
                </div>
            `;
            return;
        }

        container.innerHTML = this.data.categories.map(category => `
            <div class="category-item ${this.selectedCategoryId === category.id ? 'active' : ''}" data-category-id="${category.id}">
                <div class="category-name" onclick="ShoppingList.selectCategory('${category.id}')" style="color: ${category.color}">
                    <i class="fas fa-tag me-2"></i>${category.name}
                </div>
                <div class="category-actions">
                    <button class="btn delete-category-btn" onclick="ShoppingList.deleteCategory('${category.id}')" title="删除分类">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `).join('');
    },

    // 更新分类选择下拉框
    updateCategorySelect() {
        const select = document.getElementById('itemCategory');
        if (!select) return;

        select.innerHTML = '<option value="">选择分类</option>' +
            this.data.categories.map(category =>
                `<option value="${category.id}">${category.name}</option>`
            ).join('');
    },

    // 选择分类
    selectCategory(categoryId) {
        this.selectedCategoryId = this.selectedCategoryId === categoryId ? null : categoryId;
        this.updateCategoriesDisplay();
        this.updateDisplay();
    },

    // 更新购物项目显示
    updateDisplay() {
        const container = document.getElementById('shoppingItemsContainer');
        if (!container) return;

        let filteredItems = [...this.data.shoppingItems];

        // 根据选中的分类筛选
        if (this.selectedCategoryId) {
            filteredItems = filteredItems.filter(item => item.categoryId === this.selectedCategoryId);
        }

        // 根据视图筛选
        const activeFilter = document.querySelector('input[name="viewFilter"]:checked');
        if (activeFilter) {
            switch (activeFilter.id) {
                case 'pendingItems':
                    filteredItems = filteredItems.filter(item => !item.completed);
                    break;
                case 'completedItems':
                    filteredItems = filteredItems.filter(item => item.completed);
                    break;
                // 'allItems' 不需要额外筛选
            }
        }

        if (filteredItems.length === 0) {
            container.innerHTML = this.getEmptyStateHTML();
            return;
        }

        // 按创建时间排序，未完成的在前
        filteredItems.sort((a, b) => {
            if (a.completed !== b.completed) {
                return a.completed ? 1 : -1;
            }
            return new Date(b.createdAt) - new Date(a.createdAt);
        });

        container.innerHTML = filteredItems.map(item => this.getItemHTML(item)).join('');
    },

    // 获取购物项目HTML
    getItemHTML(item) {
        const category = this.data.categories.find(cat => cat.id === item.categoryId);
        const categoryName = category ? category.name : '未分类';
        const categoryColor = category ? category.color : '#6c757d';

        const deadlineInfo = this.getDeadlineInfo(item.deadline);

        return `
            <div class="shopping-item ${item.completed ? 'completed' : ''} ${deadlineInfo.class}">
                <input type="checkbox" class="item-checkbox" ${item.completed ? 'checked' : ''}
                       onchange="ShoppingList.toggleItemComplete('${item.id}')">

                <div class="item-content">
                    <div class="item-main">
                        <div class="item-name-row">
                            <div class="item-category" style="background-color: ${categoryColor}20; color: ${categoryColor}">
                                ${categoryName}
                            </div>
                            <div class="item-name">${item.name}</div>
                        </div>
                    </div>

                    <div class="item-quantity">
                        <i class="fas fa-cube me-1"></i>${item.quantity}
                    </div>

                    <div class="item-budget">
                        ¥${item.budget.toFixed(2)}
                    </div>

                    <div class="item-deadline ${deadlineInfo.class}">
                        <i class="fas fa-calendar me-1"></i>${deadlineInfo.text}
                    </div>

                    ${item.note ? `<div class="item-note">
                        <i class="fas fa-sticky-note me-2"></i>${item.note}
                    </div>` : ''}
                </div>

                <div class="item-actions">
                    <button class="btn btn-outline-primary btn-sm" onclick="ShoppingList.editItem('${item.id}')">
                        <i class="fas fa-edit me-1"></i>编辑
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="ShoppingList.deleteItem('${item.id}')">
                        <i class="fas fa-trash me-1"></i>删除
                    </button>
                </div>
            </div>
        `;
    },

    // 获取截止日期信息
    getDeadlineInfo(deadline) {
        if (!deadline) {
            return { text: '无期限', class: '' };
        }

        const today = new Date(this.getBeiJingTime());
        const deadlineDate = new Date(deadline);
        const diffTime = deadlineDate.getTime() - today.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays < 0) {
            return {
                text: `已过期 ${Math.abs(diffDays)} 天`,
                class: 'overdue'
            };
        } else if (diffDays === 0) {
            return { text: '今天', class: 'urgent' };
        } else if (diffDays === 1) {
            return { text: '明天', class: 'urgent' };
        } else if (diffDays <= 3) {
            return { text: `${diffDays} 天后`, class: 'urgent' };
        } else {
            return { text: `${diffDays} 天后`, class: '' };
        }
    },

    // 获取空状态HTML
    getEmptyStateHTML() {
        return `
            <div class="empty-state">
                <i class="fas fa-shopping-cart"></i>
                <h5>暂无购物项目</h5>
                <p>开始添加您的购物清单，让购物更有条理！</p>
                <button class="btn btn-primary" onclick="document.getElementById('itemName').focus()">
                    <i class="fas fa-plus me-2"></i>添加第一个项目
                </button>
            </div>
        `;
    },

    // 更新统计信息
    updateStats() {
        const totalItems = this.data.shoppingItems.length;
        const completedItems = this.data.shoppingItems.filter(item => item.completed).length;
        const totalBudget = this.data.shoppingItems.reduce((sum, item) => sum + item.budget, 0);
        const spentBudget = this.data.shoppingItems
            .filter(item => item.completed)
            .reduce((sum, item) => sum + item.budget, 0);

        document.getElementById('totalItems').textContent = totalItems;
        document.getElementById('completedItems').textContent = completedItems;
        document.getElementById('totalBudget').textContent = `¥${totalBudget.toFixed(2)}`;
        document.getElementById('spentBudget').textContent = `¥${spentBudget.toFixed(2)}`;
    }
};

// 导出到全局作用域
window.ShoppingList = ShoppingList;
