/* 优先级管理样式 */

/* 整体布局优化 */
.card {
    border-radius: 8px;
}

.card-header {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(0,0,0,.125);
}

.card-body {
    padding: 1rem;
}

/* 列表标签样式 */
.list-tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 0;
}

.list-tab {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.list-tab:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.list-tab.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
}

.list-tab-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    margin-right: 0.5rem;
}

.list-name {
    font-weight: 500;
    margin-right: 0.5rem;
}

.list-count {
    background: rgba(0, 0, 0, 0.1);
    color: inherit;
    padding: 0.2rem 0.4rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.list-tab.active .list-count {
    background: rgba(255, 255, 255, 0.2);
}

.list-tab-actions {
    display: flex;
    gap: 0.25rem;
}

.list-tab-actions .btn {
    padding: 0.2rem 0.4rem;
    font-size: 0.7rem;
    border: none;
    background: rgba(0, 0, 0, 0.1);
    color: inherit;
}

.list-tab-actions .btn:hover {
    background: rgba(0, 0, 0, 0.2);
}

.list-tab.active .list-tab-actions .btn {
    background: rgba(255, 255, 255, 0.2);
}

.list-tab.active .list-tab-actions .btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 优先级列表卡片样式 */
.priority-list-card {
    transition: all 0.3s ease;
}

.priority-list-card.active-list {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.list-header {
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.list-header:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.collapse-icon {
    transition: transform 0.3s ease;
    color: #6c757d;
}

.list-content {
    transition: all 0.3s ease;
}

/* 页面头部样式 */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 0;
    margin-bottom: 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.1rem;
}

.header-subtitle {
    font-size: 0.8rem;
    opacity: 0.9;
    margin-bottom: 0;
}

/* 优先级列表容器 */
.priority-list {
    min-height: 200px;
    padding: 0.5rem 0;
}

/* 优先级项目样式 */
.priority-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    margin-bottom: 0.75rem;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    background: white;
    cursor: grab;
    position: relative;
}

.priority-item:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.priority-item.dragging {
    opacity: 0.7;
    cursor: grabbing;
    z-index: 1000;
}

/* 优先级颜色渐变 - 从红色到绿色 */
.priority-item.priority-1 {
    border-left: 6px solid #dc3545;
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.05) 100%);
}

.priority-item.priority-2 {
    border-left: 6px solid #fd7e14;
    background: linear-gradient(135deg, rgba(253, 126, 20, 0.1) 0%, rgba(253, 126, 20, 0.05) 100%);
}

.priority-item.priority-3 {
    border-left: 6px solid #ffc107;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
}

.priority-item.priority-4 {
    border-left: 6px solid #20c997;
    background: linear-gradient(135deg, rgba(32, 201, 151, 0.1) 0%, rgba(32, 201, 151, 0.05) 100%);
}

.priority-item.priority-5 {
    border-left: 6px solid #198754;
    background: linear-gradient(135deg, rgba(25, 135, 84, 0.1) 0%, rgba(25, 135, 84, 0.05) 100%);
}

/* 默认优先级（低优先级） */
.priority-item.priority-default {
    border-left: 6px solid #6c757d;
    background: linear-gradient(135deg, rgba(108, 117, 125, 0.1) 0%, rgba(108, 117, 125, 0.05) 100%);
}

/* 已完成状态 */
.priority-item.completed {
    opacity: 0.6;
    background: linear-gradient(135deg, rgba(25, 135, 84, 0.15) 0%, rgba(25, 135, 84, 0.08) 100%);
    border-left-color: #198754;
}

.priority-item.completed .priority-text {
    text-decoration: line-through;
    color: #6c757d;
}



/* 优先级序号 */
.priority-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.1);
    color: #495057;
    font-weight: 600;
    font-size: 0.9rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

/* 复选框样式 */
.priority-checkbox {
    width: 20px;
    height: 20px;
    margin-right: 1rem;
    cursor: pointer;
    flex-shrink: 0;
}

/* 优先级文本 */
.priority-text {
    flex: 1;
    font-size: 1rem;
    color: #495057;
    margin-right: 1rem;
    word-break: break-word;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}



/* 编辑输入框样式 */
.priority-edit-input {
    flex: 1;
    margin-right: 1rem;
    font-size: 1rem;
    border: 2px solid #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 操作按钮 */
.priority-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

.priority-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    border-radius: 4px;
}

/* 拖拽放置区域 */
.drop-zone {
    min-height: 60px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
}

.drop-zone.drag-over {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
}

/* 空状态样式 */
.empty-state {
    color: #6c757d;
    padding: 2rem 0 !important;
}

.empty-state i {
    opacity: 0.5;
}

/* 统计信息 */
.priority-stats .badge {
    margin-left: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        padding: 0.4rem 0;
    }

    .header-title {
        font-size: 1.1rem;
    }

    .priority-item {
        padding: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .priority-item::before {
        display: none;
    }

    .priority-number {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
        margin-right: 0.75rem;
    }

    .priority-text {
        font-size: 0.9rem;
    }

    .priority-actions .btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.75rem;
    }

    /* 列表标签响应式 */
    .list-tabs {
        flex-direction: column;
        gap: 0.25rem;
    }

    .list-tab {
        min-width: auto;
        width: 100%;
    }

    .list-tab-actions .btn {
        padding: 0.15rem 0.3rem;
        font-size: 0.65rem;
    }
}

@media (max-width: 576px) {
    .priority-item {
        flex-wrap: wrap;
        padding: 0.5rem;
    }

    .priority-text {
        width: 100%;
        margin: 0.5rem 0;
    }

    .priority-actions {
        width: 100%;
        justify-content: flex-end;
    }

    /* 小屏幕下列表标签优化 */
    .list-tab {
        padding: 0.4rem 0.6rem;
    }

    .list-name {
        font-size: 0.9rem;
    }

    .list-count {
        font-size: 0.7rem;
        padding: 0.15rem 0.3rem;
    }
}



/* 拖拽时的视觉反馈 */
.priority-item.drag-over-top {
    border-top: 3px solid #0d6efd;
}

.priority-item.drag-over-bottom {
    border-bottom: 3px solid #0d6efd;
}





/* 批量导入预览样式 */
#batchPreview {
}

#batchPreview .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
    border-radius: 6px;
}

#batchPreview .alert {
    border-radius: 8px;
    border: none;
    background: linear-gradient(135deg, rgba(13, 202, 240, 0.1) 0%, rgba(13, 110, 253, 0.1) 100%);
    border-left: 4px solid #0dcaf0;
}

/* 表单提示文字样式 */
.form-text {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 0.5rem;
}

.form-text i {
    color: #ffc107;
}

/* 操作按钮组样式 */
.btn-group-compact .btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
    border-radius: 6px;
}

.btn-group-compact .btn i {
    font-size: 1rem;
}

/* 添加事项区域按钮样式 */
#addPriorityForm .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.8rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#addPriorityForm .btn i {
    font-size: 0.9rem;
}

/* 响应式按钮调整 */
@media (max-width: 768px) {
    #addPriorityForm .btn {
        padding: 0.25rem 0.4rem;
        font-size: 0.7rem;
    }

    #addPriorityForm .btn i {
        font-size: 0.8rem;
    }

    /* 小屏幕下只显示图标 */
    #addPriorityForm .btn .btn-text {
        display: none;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 0.3rem 0;
    }

    .container.mt-2 {
        margin-top: 0.5rem !important;
    }

    .card-header {
        padding: 0.5rem 0.75rem;
    }

    .card-body {
        padding: 0.75rem;
    }

    #addPriorityForm .row.g-2 {
        --bs-gutter-x: 0.25rem;
    }

    #addPriorityForm .btn {
        padding: 0.2rem 0.3rem;
        font-size: 0.65rem;
    }
}
