<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>树状分支图 - 人生路径规划</title>
    <link rel="icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            min-height: calc(100vh - 40px);
            position: relative;
        }
        
        .header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 20px 20px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .content-area {
            padding: 30px;
        }

        .toolbar {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }



        .zoom-controls {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .zoom-level {
            min-width: 50px;
            text-align: center;
            font-weight: 500;
        }

        .tree-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 0;
            min-height: 600px;
            position: relative;
            overflow: hidden;
        }

        .tree-svg {
            width: 100%;
            height: 600px;
            cursor: grab;
        }

        .tree-svg:active {
            cursor: grabbing;
        }



        .path-info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            max-width: 300px;
            z-index: 100;
            display: none;
        }

        .path-info.show {
            display: block;
        }



        .empty-state {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
        }

        .tree-node {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tree-node:hover {
            filter: brightness(1.1);
        }



        .tree-node.selected {
            filter: url(#glow);
        }

        .tree-node.highlighted {
            filter: brightness(1.2) saturate(1.3);
        }

        .tree-node.dimmed {
            opacity: 0.3;
        }

        .tree-node.editing .node-rect {
            stroke: #ffc107;
            stroke-width: 3;
        }

        .node-controls {
            opacity: 0;
            transition: opacity 0.2s ease;
            pointer-events: none;
        }

        .tree-node.selected .node-controls {
            opacity: 1;
            pointer-events: auto;
        }

        /* 扩大悬停区域 */
        .node-hover-area {
            fill: transparent;
            pointer-events: all;
        }

        .node-control-btn {
            transition: transform 0.2s ease;
        }

        .node-control-btn:hover {
            transform: scale(1.1);
        }

        .node-input {
            background: white;
            border: 2px solid #ffc107;
            border-radius: 15px;
            padding: 5px 10px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            outline: none;
            width: 110px;
        }

        .tree-link {
            stroke: #667eea;
            stroke-width: 2;
            fill: none;
            marker-end: url(#arrowhead);
            transition: all 0.3s ease;
        }

        .tree-link.highlighted {
            stroke: #28a745;
            stroke-width: 4;
            filter: url(#glow);
        }

        .tree-link.dimmed {
            opacity: 0.2;
        }

        .node-label {
            font-size: 12px;
            font-weight: 500;
            text-anchor: middle;
            fill: white;
            pointer-events: none;
        }

        .depth-indicator {
            font-size: 10px;
            fill: rgba(255, 255, 255, 0.8);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
        }
        
        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
            border-radius: 25px;
            padding: 8px 20px;
            font-weight: 500;
        }
        
        .btn-outline-primary:hover {
            background: #667eea;
            border-color: #667eea;
        }
        
        .home-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="main-container">

        
        <!-- 头部 -->
        <div class="header">
            <div>
                <h5 class="mb-0">
                    <i class="fas fa-sitemap me-1" style="font-size: 0.9rem;"></i>
                    树状分支图
                </h5>
                <small class="opacity-75">可视化您的人生选择路径</small>
            </div>
            <div>
                <a href="index.html" class="btn btn-outline-light btn-sm">
                    <i class="fas fa-home me-1"></i> 返回主系统
                </a>
            </div>
        </div>
        
        <!-- 工具栏 -->
        <div class="content-area">
            <div class="toolbar">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <div class="d-flex gap-2 align-items-center">
                        <button class="btn btn-outline-primary btn-sm" onclick="addRootNode()">
                            添加根节点
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="autoLayout()">
                            整理布局
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="fitToScreen()">
                            适应屏幕
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="togglePathMode()">
                            路径模式
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="resetTree()">
                            重置
                        </button>
                        <span class="sync-status ms-2" id="syncStatus">
                            <i class="fas fa-check-circle text-success"></i>
                            <small class="text-muted">已同步</small>
                        </span>
                    </div>

                    <!-- 搜索和控制面板 -->
                    <div class="d-flex gap-2 align-items-center">
                        <div class="input-group" style="width: 200px;">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control form-control-sm" id="searchInput" placeholder="搜索节点..." onkeyup="searchNodes()">
                        </div>
                        <div class="input-group" style="width: 180px;">
                            <span class="input-group-text"><i class="fas fa-filter"></i></span>
                            <select class="form-select form-select-sm" id="depthFilter" onchange="filterByDepth()">
                                <option value="">显示所有层级</option>
                                <option value="1">仅显示第1层</option>
                                <option value="2">显示到第2层</option>
                                <option value="3">显示到第3层</option>
                                <option value="4">显示到第4层</option>
                                <option value="5">显示到第5层</option>
                            </select>
                        </div>
                        <div class="d-flex gap-1 align-items-center">
                            <button class="btn btn-outline-secondary btn-sm" onclick="zoomOut()">
                                <i class="fas fa-minus"></i>
                            </button>
                            <span class="zoom-level mx-2" id="zoomLevel" style="min-width: 45px; text-align: center; font-size: 0.8rem;">100%</span>
                            <button class="btn btn-outline-secondary btn-sm" onclick="zoomIn()">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 树状图容器 -->
            <div class="tree-container" id="treeContainer">
                <!-- 路径信息面板 -->
                <div class="path-info" id="pathInfo">
                    <h6><i class="fas fa-route me-2"></i>当前路径</h6>
                    <div id="pathContent">点击节点查看路径</div>
                </div>



                <!-- SVG画布 -->
                <svg id="treeSvg" class="tree-svg">
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7"
                                refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#667eea" />
                        </marker>
                        <filter id="glow">
                            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                            <feMerge>
                                <feMergeNode in="coloredBlur"/>
                                <feMergeNode in="SourceGraphic"/>
                            </feMerge>
                        </filter>
                    </defs>
                    <g id="treeGroup"></g>
                </svg>

                <div class="empty-state text-center text-muted mt-5" id="emptyState">
                    <i class="fas fa-mouse-pointer fa-3x mb-3"></i>
                    <h5>开始创建您的人生路径树</h5>
                    <p>点击"添加根节点"开始，或者点击现有节点来添加分支</p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.min.js"></script>
    <script src="js/tree-path-new.js"></script>
</body>
</html>
