// 每日时间分配系统 JavaScript

// 获取北京时间
function getBeiJingTime() {
    const now = new Date();
    const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
    const beijing = new Date(utc + (8 * 3600000));
    return beijing.toISOString();
}

// 数据结构
let timeData = {
    // 总可用时间(分钟)
    totalAvailableTime: 24 * 60, // 24小时 = 1440分钟
    // 已分配时间(分钟)
    allocatedTime: 0,
    // 任务列表
    tasks: [],
    // 模板
    templates: []
};

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    // 加载数据
    loadData();
    
    // 与主系统数据同步
    syncWithMainSystem();
    
    // 绑定表单提交事件
    document.getElementById('timeTaskForm').addEventListener('submit', addTimeTask);
    
    // 绑定重置按钮事件
    document.getElementById('resetDayBtn').addEventListener('click', resetDay);
    
    // 绑定修改可用时间按钮事件
    document.getElementById('editTimeBtn').addEventListener('click', showEditTimeModal);
    document.getElementById('confirmEditTime').addEventListener('click', saveEditedTime);
    
    // 绑定保存模板按钮事件
    document.getElementById('saveTemplateBtn').addEventListener('click', showSaveTemplateModal);
    document.getElementById('confirmSaveTemplate').addEventListener('click', saveTemplate);
    
    // 绑定加载模板按钮事件
    document.getElementById('loadTemplateBtn').addEventListener('click', showTemplateModal);
    document.getElementById('loadSelectedTemplate').addEventListener('click', loadSelectedTemplate);
    
    // 绑定排序按钮事件
    document.getElementById('sortByTimeBtn').addEventListener('click', () => sortTasks('time'));
    document.getElementById('sortByPriorityBtn').addEventListener('click', () => sortTasks('priority'));
    
    // 绑定编辑任务按钮事件
    document.getElementById('confirmEditTask').addEventListener('click', saveEditedTask);
    
    // 绑定已完成任务折叠事件
    document.querySelector('[href="#completedTasksCollapse"]').addEventListener('click', function() {
        // 旋转箭头图标
        const icon = this.querySelector('.collapse-icon');
        if (icon.classList.contains('fa-caret-right')) {
            icon.classList.replace('fa-caret-right', 'fa-caret-down');
        } else {
            icon.classList.replace('fa-caret-down', 'fa-caret-right');
        }
    });
    
    // 渲染界面
    updateTimeDisplay();
    renderTasks();
    renderCompletedTasks();
    renderDailySchedule();
    updateTaskCounts();
});

// 加载数据
function loadData() {
    try {
        // 首先尝试从localStorage直接读取
        const savedData = localStorage.getItem('dailyTimeData');
        
        // 如果localStorage中有数据，使用它
        if (savedData) {
            const parsedData = JSON.parse(savedData);
            
            // 确保数据结构完整
            timeData = {
                totalAvailableTime: parsedData.totalAvailableTime || 24 * 60,
                allocatedTime: parsedData.allocatedTime || 0,
                tasks: parsedData.tasks || [],
                templates: parsedData.templates || []
            };
        } 
        // 如果localStorage中没有数据，但主系统appData中有，使用主系统数据
        else if (typeof appData !== 'undefined' && appData.dailyTime) {
            timeData = {
                totalAvailableTime: appData.dailyTime.totalAvailableTime || 24 * 60,
                allocatedTime: appData.dailyTime.allocatedTime || 0,
                tasks: appData.dailyTime.tasks || [],
                templates: appData.dailyTime.templates || []
            };
            
            // 将主系统数据保存回localStorage
            localStorage.setItem('dailyTimeData', JSON.stringify(timeData));
        }
        
        // 更新界面
        updateTimeDisplay();
        renderTasks();
        renderCompletedTasks();
        renderDailySchedule();
        updateTaskCounts();
    } catch (error) {
        console.error('加载数据失败:', error);
    }
}

// 保存数据
function saveData() {
    try {
        localStorage.setItem('dailyTimeData', JSON.stringify(timeData));
        
        // 如果主系统的appData对象存在，同步数据到appData
        if (typeof appData !== 'undefined') {
            // 将每日时间数据也保存到主系统中
            appData.dailyTime = JSON.parse(JSON.stringify(timeData)); // 使用深拷贝确保数据独立
            
            // 如果主系统有saveAppData函数，调用它来保存数据
            if (typeof window.saveAppData === 'function') {
                window.saveAppData();
            } else if (typeof window.saveData === 'function') {
                window.saveData();
            }
        }
    } catch (error) {
        console.error('保存数据失败:', error);
    }
}

// 添加时间任务
function addTimeTask(event) {
    event.preventDefault();
    
    // 获取表单数据
    const taskName = document.getElementById('taskName').value.trim();
    const taskHours = parseInt(document.getElementById('taskHours').value) || 0;
    const taskMinutes = parseInt(document.getElementById('taskMinutes').value) || 0;
    const startTime = document.getElementById('startTime').value;
    const endTime = document.getElementById('endTime').value;
    const taskPriority = document.getElementById('taskPriority').value;
    
    // 验证任务名称
    if (!taskName) {
        alert('请输入任务名称');
        return;
    }
    
    // 计算任务时间（分钟）
    const taskTimeMinutes = (taskHours * 60) + taskMinutes;
    
    // 验证时间是否有效
    if (taskTimeMinutes <= 0) {
        alert('请设置有效的任务时间');
        return;
    }
    
    // 验证剩余时间是否足够
    if (taskTimeMinutes > (timeData.totalAvailableTime - timeData.allocatedTime)) {
        alert('剩余时间不足，无法分配该任务');
        return;
    }
    
    // 创建新任务
    const newTask = {
        id: Date.now().toString(),
        name: taskName,
        timeMinutes: taskTimeMinutes,
        startTime: startTime,
        endTime: endTime,
        priority: taskPriority,
        completed: false,
        createdAt: getBeiJingTime()
    };
    
    // 添加到任务列表
    timeData.tasks.push(newTask);
    
    // 更新已分配时间
    timeData.allocatedTime += taskTimeMinutes;
    
    // 保存数据
    saveData();
    
    // 更新界面
    updateTimeDisplay();
    renderTasks();
    renderCompletedTasks();
    renderDailySchedule();
    updateTaskCounts();
    
    // 重置表单
    document.getElementById('timeTaskForm').reset();
}

// 删除任务
function deleteTask(taskId) {
    // 查找任务
    const taskIndex = timeData.tasks.findIndex(task => task.id === taskId);
    if (taskIndex === -1) return;
    
    // 获取任务信息
    const task = timeData.tasks[taskIndex];
    
    // 更新已分配时间（只有未完成的任务占用时间）
    if (!task.completed) {
        timeData.allocatedTime -= task.timeMinutes;
    }
    
    // 从任务列表中移除
    timeData.tasks.splice(taskIndex, 1);
    
    // 保存数据
    saveData();
    
    // 更新界面
    updateTimeDisplay();
    renderTasks();
    renderCompletedTasks();
    renderDailySchedule();
    updateTaskCounts();
}

// 完成任务
function completeTask(taskId) {
    // 查找任务
    const task = timeData.tasks.find(task => task.id === taskId);
    if (!task) return;
    
    // 更新任务状态
    task.completed = true;
    
    // 不再改变已分配时间，保持总时间不变
    // 已分配的时间依然算作被占用
    
    // 保存数据
    saveData();
    
    // 更新界面
    updateTimeDisplay();
    renderTasks();
    renderCompletedTasks();
    renderDailySchedule();
    updateTaskCounts();
}

// 取消完成任务（恢复任务）
function uncompleteTask(taskId) {
    // 查找任务
    const task = timeData.tasks.find(task => task.id === taskId);
    if (!task) return;
    
    // 更新任务状态
    task.completed = false;
    
    // 不需要更新已分配时间，因为完成任务时没有减少
    
    // 保存数据
    saveData();
    
    // 更新界面
    updateTimeDisplay();
    renderTasks();
    renderCompletedTasks();
    renderDailySchedule();
    updateTaskCounts();
}

// 显示编辑任务模态框
function showEditTaskModal(taskId) {
    // 查找任务
    const task = timeData.tasks.find(task => task.id === taskId);
    if (!task) return;
    
    // 填充表单
    document.getElementById('editTaskId').value = task.id;
    document.getElementById('editTaskName').value = task.name;
    
    // 设置时间
    const hours = Math.floor(task.timeMinutes / 60);
    const minutes = task.timeMinutes % 60;
    document.getElementById('editTaskHours').value = hours;
    document.getElementById('editTaskMinutes').value = minutes;
    
    // 设置时间段
    document.getElementById('editStartTime').value = task.startTime || '';
    document.getElementById('editEndTime').value = task.endTime || '';
    
    // 设置优先级
    document.getElementById('editTaskPriority').value = task.priority;
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('editTaskModal'));
    modal.show();
}

// 保存编辑的任务
function saveEditedTask() {
    // 获取任务ID
    const taskId = document.getElementById('editTaskId').value;
    
    // 查找任务
    const taskIndex = timeData.tasks.findIndex(task => task.id === taskId);
    if (taskIndex === -1) return;
    
    const task = timeData.tasks[taskIndex];
    
    // 获取表单数据
    const taskName = document.getElementById('editTaskName').value.trim();
    const taskHours = parseInt(document.getElementById('editTaskHours').value) || 0;
    const taskMinutes = parseInt(document.getElementById('editTaskMinutes').value) || 0;
    const startTime = document.getElementById('editStartTime').value;
    const endTime = document.getElementById('editEndTime').value;
    const taskPriority = document.getElementById('editTaskPriority').value;
    
    // 验证任务名称
    if (!taskName) {
        alert('请输入任务名称');
        return;
    }
    
    // 计算新的任务时间（分钟）
    const newTaskTimeMinutes = (taskHours * 60) + taskMinutes;
    
    // 验证时间是否有效
    if (newTaskTimeMinutes <= 0) {
        alert('请设置有效的任务时间');
        return;
    }
    
    // 计算时间差
    const timeDifference = newTaskTimeMinutes - task.timeMinutes;
    
    // 验证剩余时间是否足够（只检查未完成的任务）
    if (!task.completed && timeDifference > 0) {
        if (timeDifference > (timeData.totalAvailableTime - timeData.allocatedTime)) {
            alert('剩余时间不足，无法增加该任务的时间');
            return;
        }
        // 增加已分配时间
        timeData.allocatedTime += timeDifference;
    } else if (!task.completed && timeDifference < 0) {
        // 减少已分配时间
        timeData.allocatedTime += timeDifference; // 这里是加上负数，相当于减法
    }
    
    // 更新任务
    task.name = taskName;
    task.timeMinutes = newTaskTimeMinutes;
    task.startTime = startTime;
    task.endTime = endTime;
    task.priority = taskPriority;
    
    // 保存数据
    saveData();
    
    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('editTaskModal'));
    modal.hide();
    
    // 更新界面
    updateTimeDisplay();
    renderTasks();
    renderCompletedTasks();
    renderDailySchedule();
    updateTaskCounts();
}

// 重置每日时间
function resetDay() {
    if (confirm('确定要重置每日时间吗？所有任务将被清除。')) {
        // 重置数据
        timeData.totalAvailableTime = 24 * 60;
        timeData.allocatedTime = 0;
        timeData.tasks = [];
        
        // 保存数据
        saveData();
        
        // 更新界面
        updateTimeDisplay();
        renderTasks();
        renderCompletedTasks();
        renderDailySchedule();
        updateTaskCounts();
    }
}

// 显示保存模板模态框
function showSaveTemplateModal() {
    // 检查是否有任务可以保存
    if (timeData.tasks.length === 0) {
        alert('没有任务可以保存为模板');
        return;
    }
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('saveTemplateModal'));
    modal.show();
}

// 保存模板
function saveTemplate() {
    // 获取模板名称
    const templateName = document.getElementById('templateName').value.trim();
    
    // 验证模板名称
    if (!templateName) {
        alert('请输入模板名称');
        return;
    }
    
    // 创建新模板
    const newTemplate = {
        id: Date.now().toString(),
        name: templateName,
        tasks: JSON.parse(JSON.stringify(timeData.tasks)), // 深拷贝任务列表
        createdAt: getBeiJingTime()
    };
    
    // 添加到模板列表
    timeData.templates.push(newTemplate);
    
    // 保存数据
    saveData();
    
    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('saveTemplateModal'));
    modal.hide();
}

// 显示模板选择模态框
function showTemplateModal() {
    // 检查是否有模板可以加载
    if (timeData.templates.length === 0) {
        alert('没有可用的模板');
        return;
    }
    
    // 渲染模板列表
    renderTemplateList();
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('templateModal'));
    modal.show();
}

// 渲染模板列表
function renderTemplateList() {
    const templateList = document.getElementById('templateList');
    templateList.innerHTML = '';
    
    timeData.templates.forEach(template => {
        // 计算模板中任务的总时间
        const totalTime = template.tasks.reduce((total, task) => total + task.timeMinutes, 0);
        const formattedTime = formatTime(totalTime);
        
        // 创建模板项
        const templateItem = document.createElement('div');
        templateItem.className = 'form-check mb-2';
        templateItem.innerHTML = `
            <input class="form-check-input" type="radio" name="templateRadio" id="template-${template.id}" value="${template.id}">
            <label class="form-check-label" for="template-${template.id}">
                ${template.name} <span class="text-muted">- ${formattedTime} (${template.tasks.length} 个任务)</span>
                <button class="btn btn-sm btn-danger float-end" onclick="deleteTemplate('${template.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </label>
        `;
        
        templateList.appendChild(templateItem);
    });
}

// 删除模板
function deleteTemplate(templateId) {
    // 阻止事件冒泡
    event.stopPropagation();
    
    if (confirm('确定要删除此模板吗？')) {
        // 查找模板
        const templateIndex = timeData.templates.findIndex(template => template.id === templateId);
        if (templateIndex === -1) return;
        
        // 从模板列表中移除
        timeData.templates.splice(templateIndex, 1);
        
        // 保存数据
        saveData();
        
        // 重新渲染模板列表
        renderTemplateList();
        
        // 如果没有模板了，关闭模态框
        if (timeData.templates.length === 0) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('templateModal'));
            modal.hide();
        }
    }
}

// 加载选中的模板
function loadSelectedTemplate() {
    // 获取选中的模板ID
    const selectedTemplate = document.querySelector('input[name="templateRadio"]:checked');
    
    if (!selectedTemplate) {
        alert('请选择一个模板');
        return;
    }
    
    // 找到对应的模板
    const templateId = selectedTemplate.value;
    const template = timeData.templates.find(template => template.id === templateId);
    
    if (!template) {
        alert('模板不存在');
        return;
    }
    
    // 确认是否替换当前任务
    if (timeData.tasks.length > 0) {
        if (!confirm('加载模板将替换当前所有任务，确定要继续吗？')) {
            return;
        }
    }
    
    // 复制模板中的任务
    const newTasks = JSON.parse(JSON.stringify(template.tasks)); // 深拷贝
    
    // 重新生成任务ID
    newTasks.forEach(task => {
        task.id = Date.now().toString() + Math.random().toString(36).substr(2, 5);
        task.completed = false; // 重置完成状态
    });
    
    // 计算总分配时间
    const newAllocatedTime = newTasks.reduce((total, task) => total + task.timeMinutes, 0);
    
    // 更新数据
    timeData.tasks = newTasks;
    timeData.allocatedTime = newAllocatedTime;
    
    // 保存数据
    saveData();
    
    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('templateModal'));
    modal.hide();
    
    // 更新界面
    updateTimeDisplay();
    renderTasks();
    renderCompletedTasks();
    renderDailySchedule();
    updateTaskCounts();
}

// 排序任务
function sortTasks(sortBy) {
    switch (sortBy) {
        case 'time':
            // 按开始时间排序
            timeData.tasks.sort((a, b) => {
                // 优先比较是否有开始时间
                if (a.startTime && !b.startTime) return -1;
                if (!a.startTime && b.startTime) return 1;
                if (!a.startTime && !b.startTime) return 0;
                
                // 比较开始时间
                return a.startTime.localeCompare(b.startTime);
            });
            break;
            
        case 'priority':
            // 按优先级排序（高 > 中 > 低）
            const priorityOrder = { 'high': 0, 'medium': 1, 'low': 2 };
            timeData.tasks.sort((a, b) => {
                return priorityOrder[a.priority] - priorityOrder[b.priority];
            });
            break;
    }
    
    // 保存数据
    saveData();
    
    // 重新渲染任务
    renderTasks();
}

// 格式化时间显示（改进版）
function formatTime(minutes) {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    // 更友好的时间显示
    if (hours > 0 && mins > 0) {
        return `${hours}小时${mins}分钟`;
    } else if (hours > 0) {
        return `${hours}小时`;
    } else {
        return `${mins}分钟`;
    }
}

// 精简版时间格式化（用于标签和进度条）
function formatTimeShort(minutes) {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
}

// 渲染任务列表（只显示未完成的任务）
function renderTasks() {
    const tasksContainer = document.getElementById('timeTasksContainer');
    tasksContainer.innerHTML = '';
    
    // 过滤出未完成任务
    const activeTasks = timeData.tasks.filter(task => !task.completed);
    
    if (activeTasks.length === 0) {
        tasksContainer.innerHTML = '<div class="alert alert-info">还没有添加任务，请使用左侧表单添加任务。</div>';
        return;
    }
    
    // 创建网格容器
    const tasksGrid = document.createElement('div');
    tasksGrid.className = 'tasks-grid';
    tasksContainer.appendChild(tasksGrid);
    
    activeTasks.forEach(task => {
        // 任务时间格式化
        const taskTime = formatTime(task.timeMinutes);
        
        // 时间段显示
        let timeRangeDisplay = '';
        if (task.startTime && task.endTime) {
            timeRangeDisplay = `<div class="time-range"><i class="far fa-calendar-alt me-1"></i>${task.startTime} - ${task.endTime}</div>`;
        } else if (task.startTime) {
            timeRangeDisplay = `<div class="time-range"><i class="far fa-calendar-alt me-1"></i>开始于 ${task.startTime}</div>`;
        }
        
        // 优先级颜色和标签
        let priorityBadge = '';
        switch (task.priority) {
            case 'high':
                priorityBadge = '<span class="badge bg-danger">高优先级</span>';
                break;
            case 'medium':
                priorityBadge = '<span class="badge bg-warning text-dark">中优先级</span>';
                break;
            case 'low':
                priorityBadge = '<span class="badge bg-info text-dark">低优先级</span>';
                break;
        }
        
        // 创建任务卡片
        const taskCard = document.createElement('div');
        taskCard.className = 'card time-task-card';
        taskCard.innerHTML = `
            <div class="card-body p-3">
                <div class="d-flex align-items-center mb-2">
                    <h5 class="card-title task-title mb-0 me-2">
                        ${task.name}
                    </h5>
                    ${priorityBadge}
                </div>
                
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <div class="task-info">
                        <div class="d-flex flex-wrap align-items-center">
                            <div class="task-time">
                                <i class="fas fa-hourglass-half"></i>
                                ${taskTime}
                            </div>
                            ${timeRangeDisplay}
                        </div>
                    </div>
                    
                    <div class="task-actions">
                        <button class="btn btn-sm btn-outline-primary me-2" onclick="showEditTaskModal('${task.id}')">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button class="btn btn-sm btn-success me-2" onclick="completeTask('${task.id}')">
                            <i class="fas fa-check"></i> 完成
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteTask('${task.id}')">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        tasksGrid.appendChild(taskCard);
    });
}

// 渲染已完成任务列表
function renderCompletedTasks() {
    const completedTasksContainer = document.getElementById('completedTasksContainer');
    completedTasksContainer.innerHTML = '';
    
    // 过滤出已完成任务
    const completedTasks = timeData.tasks.filter(task => task.completed);
    
    if (completedTasks.length === 0) {
        completedTasksContainer.innerHTML = '<div class="alert alert-info">暂无已完成任务。</div>';
        return;
    }
    
    // 创建网格容器
    const completedTasksGrid = document.createElement('div');
    completedTasksGrid.className = 'tasks-grid';
    completedTasksContainer.appendChild(completedTasksGrid);
    
    completedTasks.forEach(task => {
        // 任务时间格式化
        const taskTime = formatTime(task.timeMinutes);
        
        // 时间段显示
        let timeRangeDisplay = '';
        if (task.startTime && task.endTime) {
            timeRangeDisplay = `<div class="time-range"><i class="far fa-calendar-alt me-1"></i>${task.startTime} - ${task.endTime}</div>`;
        } else if (task.startTime) {
            timeRangeDisplay = `<div class="time-range"><i class="far fa-calendar-alt me-1"></i>开始于 ${task.startTime}</div>`;
        }
        
        // 创建任务卡片
        const taskCard = document.createElement('div');
        taskCard.className = 'card time-task-card border-success mb-2 completed-task';
        taskCard.innerHTML = `
            <div class="card-body p-3">
                <div class="d-flex align-items-center mb-2">
                    <h5 class="card-title task-title mb-0 me-2">
                        ${task.name}
                    </h5>
                    <span class="badge bg-success">已完成</span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <div class="task-info">
                        <div class="d-flex flex-wrap align-items-center">
                            <div class="task-time">
                                <i class="fas fa-hourglass-half"></i>
                                ${taskTime}
                            </div>
                            ${timeRangeDisplay}
                        </div>
                    </div>
                    
                    <div class="task-actions">
                        <button class="btn btn-sm btn-outline-success me-2" onclick="uncompleteTask('${task.id}')">
                            <i class="fas fa-undo"></i> 恢复
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteTask('${task.id}')">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        completedTasksGrid.appendChild(taskCard);
    });
}

// 更新时间显示
function updateTimeDisplay() {
    // 计算剩余时间
    const remainingMinutes = timeData.totalAvailableTime - timeData.allocatedTime;
    
    // 格式化显示
    const remainingTime = formatTimeShort(remainingMinutes);
    
    // 更新显示
    document.getElementById('remainingTime').textContent = remainingTime;
    
    // 更新进度条 - 修改为显示剩余时间的百分比
    const remainingPercentage = Math.floor((remainingMinutes / timeData.totalAvailableTime) * 100);
    const progressBar = document.getElementById('timeProgressBar');
    progressBar.style.width = `${remainingPercentage}%`;
    progressBar.textContent = `${remainingPercentage}%`;
    
    // 根据剩余时间百分比调整颜色
    if (remainingPercentage < 10) {
        progressBar.className = 'progress-bar bg-danger';
    } else if (remainingPercentage < 30) {
        progressBar.className = 'progress-bar bg-warning';
    } else {
        progressBar.className = 'progress-bar bg-primary';
    }
}

// 渲染每日时间表
function renderDailySchedule() {
    const scheduleContainer = document.getElementById('dailyScheduleContainer');
    scheduleContainer.innerHTML = '';
    
    // 过滤出有开始时间的未完成任务
    const scheduledTasks = timeData.tasks.filter(task => task.startTime && !task.completed);
    
    if (scheduledTasks.length === 0) {
        scheduleContainer.innerHTML = '<div class="alert alert-info">还没有设置任务时间段。</div>';
        return;
    }
    
    // 按开始时间排序
    scheduledTasks.sort((a, b) => a.startTime.localeCompare(b.startTime));
    
    // 创建时间表
    const schedule = document.createElement('div');
    schedule.className = 'list-group';
    
    scheduledTasks.forEach(task => {
        // 格式化显示
        const timeDisplay = task.startTime && task.endTime 
            ? `${task.startTime} - ${task.endTime}` 
            : task.startTime;
        
        // 创建时间块
        const timeBlock = document.createElement('div');
        timeBlock.className = 'list-group-item';
        timeBlock.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <span class="badge bg-success mb-1 d-inline-block">${timeDisplay}</span>
                    <div class="fw-bold">${task.name}</div>
                </div>
                <span class="badge bg-primary rounded-pill">${formatTime(task.timeMinutes)}</span>
            </div>
        `;
        
        schedule.appendChild(timeBlock);
    });
    
    scheduleContainer.appendChild(schedule);
}

// 更新任务计数
function updateTaskCounts() {
    // 获取未完成任务数量
    const activeTasks = timeData.tasks.filter(task => !task.completed);
    document.getElementById('taskCount').textContent = activeTasks.length;
    
    // 获取已完成任务数量
    const completedTasks = timeData.tasks.filter(task => task.completed);
    document.getElementById('completedTaskCount').textContent = completedTasks.length;
    
    // 如果有已完成任务，显示完成任务区域的标记
    const completedCountBadge = document.getElementById('completedTaskCount');
    if (completedTasks.length > 0) {
        completedCountBadge.style.display = '';
    } else {
        completedCountBadge.style.display = 'none';
    }
}

// 与主系统同步数据
function syncWithMainSystem() {
    // 如果主系统appData存在但尚未初始化dailyTime字段
    if (typeof appData !== 'undefined' && !appData.dailyTime) {
        // 将当前timeData保存到appData中
        appData.dailyTime = { ...timeData };
        
        // 如果主系统有保存数据的函数，调用它
        if (typeof window.saveData === 'function') {
            window.saveData();
        }
        
        console.log('每日时间分配数据已同步到主系统');
    }
}

// 显示修改剩余可用时间模态框
function showEditTimeModal() {
    // 计算当前的剩余可用时间
    const remainingMinutes = timeData.totalAvailableTime - timeData.allocatedTime;
    const hours = Math.floor(remainingMinutes / 60);
    const minutes = remainingMinutes % 60;
    
    // 填充表单
    document.getElementById('availableHours').value = hours;
    document.getElementById('availableMinutes').value = minutes;
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('editTimeModal'));
    modal.show();
}

// 保存修改后的剩余可用时间
function saveEditedTime() {
    // 获取表单数据
    const hours = parseInt(document.getElementById('availableHours').value) || 0;
    const minutes = parseInt(document.getElementById('availableMinutes').value) || 0;
    
    // 计算总分钟数
    const totalAvailableMinutes = (hours * 60) + minutes;
    
    // 验证时间是否有效
    if (totalAvailableMinutes <= 0) {
        alert('请设置有效的可用时间');
        return;
    }
    
    // 计算新的总可用时间（当前已分配时间 + 新设置的剩余时间）
    const newTotalAvailableTime = timeData.allocatedTime + totalAvailableMinutes;
    
    // 更新数据
    timeData.totalAvailableTime = newTotalAvailableTime;
    
    // 保存数据
    saveData();
    
    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('editTimeModal'));
    modal.hide();
    
    // 更新界面
    updateTimeDisplay();
}
