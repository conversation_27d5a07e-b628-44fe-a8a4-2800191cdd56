/* Inspiration management styles - 紧凑优化版 */

/* Container layout */
.inspiration-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0.4rem; /* 减少内边距 */
}

.inspiration-wrapper {
    width: 100%;
    max-width: 800px; /* 增加最大宽度以利用空间 */
    margin: 0 auto;
    padding: 0.2rem; /* 减少内边距 */
}

/* Header and search */
.inspiration-header {
    background: #fff;
    padding: 0.3rem 0.4rem; /* 减少内边距 */
    border-radius: 3px; /* 减小圆角 */
    box-shadow: 0 1px 2px rgba(0,0,0,0.04);
    margin-bottom: 0.2rem; /* 减少下边距 */
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #f0f0f0;
    min-height: 36px; /* 固定最小高度 */
}

.inspiration-search {
    position: relative;
    flex: 1;
    max-width: 200px; /* 减小最大宽度 */
    margin-right: 0.3rem; /* 减少右边距 */
}

.inspiration-search input {
    width: 100%;
    padding: 0.2rem 0.4rem 0.2rem 1.6rem; /* 减少内边距 */
    border: 1px solid #dee2e6;
    border-radius: 3px; /* 减小圆角 */
    font-size: 0.75rem; /* 减小字体 */
    transition: all 0.2s;
    height: 26px; /* 减小高度 */
}

.inspiration-search input:focus {
    outline: none;
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.1rem rgba(13,110,253,.15); /* 减小阴影 */
}

.inspiration-search i {
    position: absolute;
    left: 0.5rem; /* 调整位置 */
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 0.75rem; /* 减小图标 */
}

/* Filters and tags - 紧凑优化 */
.inspiration-filters {
    display: flex;
    align-items: center;
    gap: 0.2rem; /* 减少间距 */
    margin: 0.3rem 0; /* 减少上下边距 */
    padding: 0.25rem 0.4rem; /* 减少内边距 */
    background: #fff;
    border-radius: 3px; /* 减小圆角 */
    overflow-x: auto;
    white-space: nowrap;
    border: 1px solid #eaeaea;
    box-shadow: 0 1px 2px rgba(0,0,0,0.04);
    min-height: 32px; /* 固定最小高度 */
}

.tag-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.15rem; /* 减少间距 */
}

.tag-btn {
    padding: 0.2rem 0.35rem; /* 减少内边距 */
    font-size: 0.75rem; /* 减小字体 */
    border: 1px solid #ddd;
    border-radius: 2px; /* 减小圆角 */
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0,0,0,0.04);
    height: 24px; /* 固定高度 */
    display: flex;
    align-items: center;
    white-space: nowrap;
}

.tag-btn:hover {
    background: #e4e6eb;
    color: #333;
    transform: translateY(-1px);
    box-shadow: 0 2px 3px rgba(0,0,0,0.1);
}

.tag-btn.active {
    background: #3498db;
    color: white;
    border-color: #3498db;
    box-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

/* Tag selection and management */
.tag-selection {
    display: flex;
    flex-wrap: wrap;
    gap: 0.35rem;
    margin-top: 0.35rem;
}

.tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.35rem;
    margin-top: 0.35rem;
}

.tag-item {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    background: #e2e8f0;
    padding: 0.2rem 0.5rem;
    border-radius: 0.75rem;
}

.tag-item button {
    padding: 0;
    width: 1.2rem;
    height: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    border: none;
    background: rgba(0,0,0,0.1);
    color: #666;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tag-item button:hover {
    background: rgba(0,0,0,0.2);
    color: #333;
}

.current-tags {
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid #e2e8f0;
}

.current-tags .form-label {
    margin-bottom: 0.75rem;
    color: #4a5568;
    font-weight: 500;
}

/* Inspiration grid - 紧凑优化 */
.inspiration-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 改为两列网格布局 */
    gap: 0.1rem; /* 减少间距 */
    padding: 0.1rem 0; /* 减少内边距 */
}

/* Inspiration items - 紧凑优化 */
.inspiration-item {
    background: white;
    border-radius: 2px; /* 减小圆角 */
    padding: 0.15rem 0.2rem; /* 减少内边距 */
    box-shadow: 0 1px 2px rgba(0,0,0,0.04);
    border: 1px solid #eee;
    cursor: move;
    transition: all 0.2s;
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 0.1rem; /* 减少间距 */
    width: 100%;
    margin-bottom: 0.1rem; /* 减少下边距 */
    min-height: auto; /* 移除最小高度限制 */
}

.inspiration-item:hover {
    border-color: #3498db;
    background-color: #f8fafd;
}

.inspiration-header-row {
    display: flex;
    align-items: flex-start;
    gap: 0.1rem; /* 减少间距 */
    margin-bottom: 0.03rem; /* 减少下边距 */
}

.inspiration-number {
    font-size: 0.65rem; /* 减小字体 */
    color: #666;
    min-width: 14px; /* 减小宽度 */
    text-align: center;
    background: #f5f5f5;
    border-radius: 2px;
    padding: 0.02rem 0.08rem; /* 减少内边距 */
    margin-top: 0.05rem; /* 减少顶部边距 */
    line-height: 1.2;
}

.inspiration-content {
    flex: 1;
    min-width: 0;
    width: 100%;
}

.inspiration-text {
    color: #4a5568;
    margin: 0;
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.3; /* 减少行高 */
    padding: 1px 0; /* 减少内边距 */
    font-size: 0.85rem; /* 减小字体 */
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3; /* 限制最多显示3行 */
    -webkit-box-orient: vertical;
}

.inspiration-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.03rem; /* 减少顶部边距 */
    font-size: 0.65rem; /* 减小字体 */
    flex-wrap: wrap;
    gap: 0.1rem; /* 减少间距 */
    padding-top: 0.05rem; /* 减少内边距 */
    border-top: 1px dashed #f0f0f0;
}

.inspiration-info {
    display: flex;
    flex-direction: column;
    gap: 0.15rem; /* 减少间距 */
}

.inspiration-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.1rem; /* 减少间距 */
    margin-right: 0.2rem; /* 减少右边距 */
}

.inspiration-tag {
    background: #f0f0f0;
    color: #666;
    padding: 0.02rem 0.15rem; /* 减少内边距 */
    border-radius: 2px;
    font-size: 0.6rem; /* 减小字体 */
    white-space: nowrap;
    line-height: 1.2;
}

/* 添加时间显示样式 - 紧凑优化 */
.inspiration-time {
    margin-left: auto;
    font-size: 0.65rem; /* 减小字体 */
    color: #888;
    padding: 0.05rem 0.2rem; /* 减少内边距 */
    background: #f8f9fa;
    border-radius: 2px;
    white-space: nowrap;
}

/* Actions and buttons - 紧凑优化 */
.inspiration-actions {
    display: flex;
    gap: 0.15rem; /* 减少间距 */
    align-items: center;
    flex-wrap: nowrap;
}

.inspiration-actions button {
    background: none;
    border: none;
    color: #666;
    padding: 0.1rem 0.2rem; /* 减少内边距 */
    border-radius: 2px; /* 减小圆角 */
    cursor: pointer;
    transition: all 0.15s;
    font-size: 0.7rem; /* 减小字体 */
    display: inline-flex;
    align-items: center;
    min-width: 20px; /* 设置最小宽度 */
    height: 20px; /* 固定高度 */
    justify-content: center;
}

/* 添加置顶和标记重点按钮样式 */
.inspiration-actions .btn-pin,
.inspiration-actions .btn-important {
    padding: 0.2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 25px;
    height: 25px;
}

.inspiration-actions .btn-pin.active {
    color: #e74c3c;
    background-color: rgba(231, 76, 60, 0.1);
}

.inspiration-actions .btn-important.active {
    color: #f39c12;
    background-color: rgba(243, 156, 18, 0.1);
}

.inspiration-actions .btn-edit {
    color: #3498db;
}

.inspiration-actions .btn-delete {
    color: #e74c3c;
}

.inspiration-actions button:hover {
    background: rgba(0,0,0,0.05);
}

/* 标记重点按钮悬停效果 - 黄色 */
.inspiration-actions .btn-important:hover {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

/* 编辑按钮悬停效果 - 蓝色 */
.inspiration-actions .btn-edit:hover {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
}

/* 删除按钮悬停效果 - 红色 */
.inspiration-actions .btn-delete:hover {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.inspiration-actions button i {
    font-size: 0.8rem;
}

/* 操作组样式优化 */
.inspiration-actions-group {
    display: flex;
    gap: 0.25rem;
}

.inspiration-actions-group .btn {
    padding: 0.25rem 0.4rem;
    border-radius: 3px;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.2rem;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.inspiration-actions-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 3px rgba(0,0,0,0.1);
}

.inspiration-actions-group .btn i {
    font-size: 0.85rem;
}

/* 优化checkbox样式 */
.inspiration-checkbox {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.15rem;
}

.inspiration-select {
    margin-top: 0.2rem;
}

/* 标记为重点的灵感项样式 */
.inspiration-item.important-item {
    border-left: 3px solid #f39c12;
    background-color: rgba(243, 156, 18, 0.04);
}

.inspiration-item.important-item .inspiration-text {
    font-weight: 500;
}

/* 置顶的灵感项样式 */
.inspiration-item[draggable="true"][data-id] {
    transition: transform 0.2s, box-shadow 0.2s;
}

.inspiration-item[draggable="true"][data-id] .btn-pin.active ~ .btn-edit {
    margin-left: 2px;
}

/* 添加统计信息显示 */
.inspiration-stats {
    background: #f8f9fa;
    border-radius: 3px;
    padding: 0.3rem 0.5rem;
    margin-bottom: 0.3rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: #495057;
    border: 1px solid #e9ecef;
}

.inspiration-stats .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.1rem;
}

.inspiration-stats .stat-label {
    font-size: 0.65rem;
    color: #6c757d;
}

.inspiration-stats .stat-value {
    font-weight: 600;
    color: #2c3e50;
}

/* 空状态提示 */
.inspiration-empty-state {
    text-align: center;
    padding: 2rem 1rem;
    color: #6c757d;
    font-size: 0.9rem;
    grid-column: 1 / -1; /* 占满整行 */
}

.inspiration-empty-state i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #adb5bd;
}

/* 响应式设计 - 紧凑优化 */
@media (max-width: 992px) {
    .inspiration-grid {
        grid-template-columns: 1fr; /* 中等屏幕单列 */
        gap: 0.08rem;
    }

    .inspiration-wrapper {
        max-width: 600px;
    }
}

@media (max-width: 768px) {
    .inspiration-container {
        padding: 0.3rem;
    }

    .inspiration-header {
        flex-direction: column;
        gap: 0.3rem;
        padding: 0.3rem;
    }

    .inspiration-search {
        max-width: 100%;
        margin-right: 0;
    }

    .inspiration-filters {
        padding: 0.2rem 0.3rem;
        margin: 0.2rem 0;
    }

    .tag-btn {
        padding: 0.15rem 0.25rem;
        font-size: 0.7rem;
        height: 22px;
    }

    .inspiration-item {
        padding: 0.1rem 0.15rem;
    }

    .inspiration-text {
        font-size: 0.8rem;
        -webkit-line-clamp: 2; /* 移动端只显示2行 */
    }

    .inspiration-actions button {
        min-width: 18px;
        height: 18px;
        font-size: 0.65rem;
    }

    .inspiration-stats {
        flex-direction: column;
        gap: 0.2rem;
        padding: 0.2rem;
    }

    .inspiration-stats .stat-item {
        flex-direction: row;
        gap: 0.3rem;
    }
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
    width: 4px; /* 减小滚动条宽度 */
    height: 4px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
    background: #aaa;
}