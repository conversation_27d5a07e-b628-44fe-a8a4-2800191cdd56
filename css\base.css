/* Base styles */

/* Typography */
body {
    font-family: system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    color: #2d3748;
    line-height: 1.4;
}

.card-title {
    font-size: 0.95rem !important;
    font-weight: 500;
    color: #2d3748;
    margin: 0;
}

.text-muted {
    color: #718096 !important;
    font-size: 0.8rem !important;
}

.small {
    font-size: 0.85rem !important;
    color: #4a5568;
}

/* Margin and Padding helpers */
.mb-3 {
    margin-bottom: 0.75rem !important;
}

.mb-2 {
    margin-bottom: 0.5rem !important;
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Remove spinners from number inputs */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield; /* Firefox */
}

/* Toast notification */
.toast {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 0.9rem;
    z-index: 1000;
} 