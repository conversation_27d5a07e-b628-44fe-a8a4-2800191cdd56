/* 数据同步界面美化样式 */

/* 同步卡片整体样式 */
#data-sync-container .card,
#sync-panel.card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    overflow: hidden;
    transition: all 0.3s ease;
}

#data-sync-container .card:hover,
#sync-panel.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.25);
}

/* 卡片头部样式 */
#data-sync-container .card-header,
#sync-panel .card-header {
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    backdrop-filter: blur(10px);
}

#data-sync-container .card-header i,
#sync-panel .card-header i {
    margin-right: 8px;
    color: rgba(255, 255, 255, 0.9);
}

/* 状态徽章样式 */
#sync-status {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 0.25rem 0.75rem;
    font-size: 0.7rem;
    font-weight: 500;
    backdrop-filter: blur(5px);
}

#sync-status.bg-success {
    background: rgba(40, 167, 69, 0.8) !important;
    border-color: rgba(40, 167, 69, 0.5);
}

#sync-status.bg-warning {
    background: rgba(255, 193, 7, 0.8) !important;
    border-color: rgba(255, 193, 7, 0.5);
}

/* 卡片主体样式 */
#data-sync-container .card-body,
#sync-panel .card-body {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 0.5rem;
}

/* 输入框组样式 */
#sync-panel .input-group-text {
    background: rgba(102, 126, 234, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-right: none;
    color: #667eea;
    border-radius: 6px 0 0 6px;
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
}

#sync-panel .input-group .form-control {
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-left: none;
    border-radius: 0 6px 6px 0;
    padding: 0.4rem 0.6rem;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

#sync-panel .input-group:focus-within .input-group-text {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.15);
}

#sync-panel .input-group:focus-within .form-control {
    border-color: #667eea;
    box-shadow: 0 0 0 0.15rem rgba(102, 126, 234, 0.25);
    background: white;
}

#sync-panel .form-control::placeholder {
    color: rgba(102, 126, 234, 0.6);
    font-weight: 400;
}

/* 按钮样式 */
#sync-panel .btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
}

#sync-panel .btn-sm {
    padding: 0.3rem 0.8rem;
    font-size: 0.8rem;
}

#sync-panel .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

#sync-panel .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

#sync-panel .btn-outline-secondary {
    background: rgba(108, 117, 125, 0.1);
    border: 2px solid rgba(108, 117, 125, 0.3);
    color: #6c757d;
}

#sync-panel .btn-outline-secondary:hover {
    background: rgba(108, 117, 125, 0.2);
    border-color: #6c757d;
    color: #495057;
    transform: translateY(-1px);
}

#sync-panel .btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

#sync-panel .btn-success:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

#sync-panel .btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

#sync-panel .btn-warning:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
}

#sync-panel .btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

#sync-panel .btn-danger:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

/* 用户信息显示 */
#sync-username-display {
    color: #667eea;
    font-weight: 600;
    font-size: 1.1rem;
}

/* 用户面板居中布局 */
#sync-user-panel .user-info-compact {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 0.4rem;
    backdrop-filter: blur(5px);
}

#sync-user-panel .fas.fa-user-circle {
    color: #667eea;
    font-size: 1.1rem;
}

#sync-user-panel .fas.fa-clock {
    color: rgba(102, 126, 234, 0.7);
    font-size: 0.8rem;
}

/* 同步状态提示 */
#sync-cloud-status {
    border: none;
    border-radius: 6px;
    background: rgba(102, 126, 234, 0.1);
    border-left: 3px solid #667eea;
    color: #495057;
    font-size: 0.75rem;
    padding: 0.3rem 0.5rem;
    margin-bottom: 0.5rem !important;
}

#sync-cloud-status.alert-success {
    background: rgba(40, 167, 69, 0.1);
    border-left-color: #28a745;
}

#sync-cloud-status.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    border-left-color: #ffc107;
}

#sync-cloud-status.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    border-left-color: #dc3545;
}

/* 同步时间显示 */
#sync-last-time {
    color: rgba(102, 126, 234, 0.7);
    font-size: 0.75rem;
    font-style: italic;
}

/* 按钮组样式 */
#sync-panel .d-grid {
    gap: 0.5rem;
}

/* 登录按钮居中样式 */
#sync-panel .text-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.3rem;
}

#sync-panel .text-center .btn {
    min-width: 100px;
}

/* 同步按钮紧凑样式 */
#sync-panel .row .btn-sm {
    padding: 0.4rem 0.5rem;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

#sync-panel .row .btn-sm i {
    font-size: 0.8rem;
}

/* 加载状态 */
#sync-panel .btn:disabled {
    opacity: 0.7;
    transform: none !important;
    cursor: not-allowed;
}

/* 响应式优化 */
@media (max-width: 768px) {
    #data-sync-container .card-body,
    #sync-panel .card-body {
        padding: 0.75rem;
    }
    
    #sync-panel .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
    
    #sync-panel .form-control {
        padding: 0.5rem 0.6rem;
        font-size: 0.8rem;
    }
}



/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
    }
}

#sync-panel {
    animation: fadeInUp 0.5s ease-out;
}

#sync-panel .btn:active {
    animation: pulse 0.6s ease-out;
}

/* 加载状态动画 */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 成功状态动画 */
.sync-success {
    animation: pulse 0.6s ease-out;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}
