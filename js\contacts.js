// 人脉管理系统 JavaScript

// 配置
const ContactsConfig = {
    STORAGE_KEY: 'contactsData',
    VERSION: '1.0.0'
};

// 获取北京时间
function getBeiJingTime() {
    const now = new Date();
    const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
    const beijing = new Date(utc + (8 * 3600000));
    return beijing.toISOString();
}

// 数据结构
let contactsData = {
    contacts: [],
    connections: [],
    tags: new Set(),
    lastId: 0,
    statistics: {
        total: 0,
        recentCount: 0,
        importantCount: 0,
        connectionCount: 0
    }
};

// 当前状态
let currentView = 'list';
let currentEditingId = null;
let searchQuery = '';
let activeFilters = {
    relationType: '',
    importance: '',
    tag: ''
};

// 网络视图相关
let networkCanvas = null;
let networkCtx = null;
let networkData = {
    nodes: [],
    links: [],
    scale: 1,
    offsetX: 0,
    offsetY: 0,
    isDragging: false,
    dragStart: { x: 0, y: 0 }
};

// DOM 元素
let domElements = {};

// =============== 初始化 ===============
document.addEventListener('DOMContentLoaded', function() {
    initializeContacts();
});

function initializeContacts() {
    console.log('初始化人脉管理系统');

    // 缓存 DOM 元素
    cacheElements();

    // 加载数据
    loadContactsData();



    // 添加事件监听器
    attachEventListeners();

    // 初始化视图
    renderCurrentView();

    // 更新统计信息
    updateStatistics();

    // 更新筛选器
    updateTagFilter();
    updateTreeCenterSelect();
}

function cacheElements() {
    domElements = {
        searchInput: document.getElementById('searchInput'),
        importanceFilter: document.getElementById('importanceFilter'),
        tagFilter: document.getElementById('tagFilter'),
        contactsList: document.getElementById('contactsList'),
        listViewContainer: document.getElementById('listViewContainer'),
        networkViewContainer: document.getElementById('networkViewContainer'),
        treeViewContainer: document.getElementById('treeViewContainer'),
        networkCanvas: document.getElementById('networkCanvas'),
        treeContainer: document.getElementById('treeContainer'),
        treeCenterSelect: document.getElementById('treeCenterSelect'),
        contactModal: document.getElementById('contactModal'),
        contactForm: document.getElementById('contactForm'),
        connectionsList: document.getElementById('connectionsList'),
        totalContacts: document.getElementById('totalContacts'),
        recentContacts: document.getElementById('recentContacts'),
        importantContacts: document.getElementById('importantContacts'),
        connectionCount: document.getElementById('connectionCount')
    };

    // 初始化网络画布
    if (domElements.networkCanvas) {
        networkCanvas = domElements.networkCanvas;
        networkCtx = networkCanvas.getContext('2d');
    }
}

function attachEventListeners() {
    // 搜索功能
    domElements.searchInput.addEventListener('input', handleSearch);
    
    // 筛选功能
    domElements.importanceFilter.addEventListener('change', handleFilter);
    domElements.tagFilter.addEventListener('change', handleFilter);
    
    // 视图切换
    document.querySelectorAll('input[name="viewMode"]').forEach(radio => {
        radio.addEventListener('change', handleViewChange);
    });
    
    // 关系树中心选择
    domElements.treeCenterSelect.addEventListener('change', handleTreeCenterChange);
    
    // 网络画布事件
    if (networkCanvas) {
        networkCanvas.addEventListener('mousedown', handleNetworkMouseDown);
        networkCanvas.addEventListener('mousemove', handleNetworkMouseMove);
        networkCanvas.addEventListener('mouseup', handleNetworkMouseUp);
        networkCanvas.addEventListener('wheel', handleNetworkWheel);
    }
    
    // 表单提交
    domElements.contactForm.addEventListener('submit', function(e) {
        e.preventDefault();
        saveContact();
    });
}

// =============== 数据管理 ===============
function loadContactsData() {
    try {
        const saved = localStorage.getItem(ContactsConfig.STORAGE_KEY);
        if (saved) {
            const parsed = JSON.parse(saved);
            contactsData = {
                ...contactsData,
                ...parsed,
                tags: new Set(parsed.tags || [])
            };
        }
        console.log('加载人脉数据:', contactsData);
    } catch (error) {
        console.error('加载人脉数据失败:', error);
        showNotification('加载数据失败', 'error');
    }
}

function saveContactsData() {
    try {
        const dataToSave = {
            ...contactsData,
            tags: Array.from(contactsData.tags)
        };
        localStorage.setItem(ContactsConfig.STORAGE_KEY, JSON.stringify(dataToSave));
        console.log('保存人脉数据成功');
    } catch (error) {
        console.error('保存人脉数据失败:', error);
        showNotification('保存数据失败', 'error');
    }
}

// =============== 人脉管理 ===============
function showAddContactModal() {
    currentEditingId = null;
    document.getElementById('contactModalTitle').textContent = '添加人脉';
    clearContactForm();
    updateConnectionsList();
    
    const modal = new bootstrap.Modal(domElements.contactModal);
    modal.show();
}

function showEditContactModal(contactId) {
    currentEditingId = contactId;
    document.getElementById('contactModalTitle').textContent = '编辑人脉';
    
    const contact = contactsData.contacts.find(c => c.id === contactId);
    if (contact) {
        fillContactForm(contact);
        updateConnectionsList();
        
        const modal = new bootstrap.Modal(domElements.contactModal);
        modal.show();
    }
}

function clearContactForm() {
    domElements.contactForm.reset();
    document.getElementById('contactImportance').value = '3';
    domElements.connectionsList.innerHTML = '';
}

function fillContactForm(contact) {
    document.getElementById('contactName').value = contact.name || '';
    document.getElementById('contactPosition').value = contact.position || '';
    document.getElementById('contactImportance').value = contact.importance || 3;
    document.getElementById('contactTags').value = (contact.tags || []).join(', ');

    // 填充关联信息
    domElements.connectionsList.innerHTML = '';
    const contactConnections = contactsData.connections.filter(conn =>
        conn.fromId === contact.id || conn.toId === contact.id
    );

    contactConnections.forEach(conn => {
        const otherContactId = conn.fromId === contact.id ? conn.toId : conn.fromId;
        addConnectionRow(otherContactId, conn.description);
    });
}

function saveContact() {
    const formData = {
        name: document.getElementById('contactName').value.trim(),
        position: document.getElementById('contactPosition').value.trim(),
        importance: parseInt(document.getElementById('contactImportance').value),
        tags: document.getElementById('contactTags').value.split(',').map(tag => tag.trim()).filter(tag => tag)
    };
    
    // 验证必填字段
    if (!formData.name) {
        showNotification('请输入姓名', 'error');
        return;
    }
    
    try {
        if (currentEditingId) {
            // 编辑现有人脉
            const contactIndex = contactsData.contacts.findIndex(c => c.id === currentEditingId);
            if (contactIndex !== -1) {
                contactsData.contacts[contactIndex] = {
                    ...contactsData.contacts[contactIndex],
                    ...formData,
                    updatedAt: getBeiJingTime()
                };
            }
        } else {
            // 添加新人脉
            const newContact = {
                id: ++contactsData.lastId,
                ...formData,
                createdAt: getBeiJingTime(),
                updatedAt: getBeiJingTime()
            };
            contactsData.contacts.push(newContact);
        }
        
        // 更新标签集合
        formData.tags.forEach(tag => contactsData.tags.add(tag));
        
        // 处理关联关系
        saveConnections();
        
        // 保存数据
        saveContactsData();

        // 优化界面更新：只更新必要的部分
        updateContactsDisplay();
        updateStatistics();
        updateTagFilter();
        updateTreeCenterSelect();
        
        // 关闭模态框
        bootstrap.Modal.getInstance(domElements.contactModal).hide();
        
        if (currentEditingId) {
            showNotification('人脉信息已更新', 'success');
        }
        
    } catch (error) {
        console.error('保存人脉失败:', error);
        showNotification('保存失败', 'error');
    }
}

function deleteContact(contactId) {
    if (!confirm('确定要删除这个人脉吗？相关的关系连接也会被删除。')) {
        return;
    }

    try {
        // 删除人脉
        contactsData.contacts = contactsData.contacts.filter(c => c.id !== contactId);

        // 删除相关连接
        contactsData.connections = contactsData.connections.filter(conn =>
            conn.fromId !== contactId && conn.toId !== contactId
        );

        // 保存数据
        saveContactsData();

        // 优化界面更新：只更新必要的部分，避免整个界面刷新
        updateContactsDisplay();
        updateStatistics();
        updateTreeCenterSelect();

        // 移除删除成功通知

    } catch (error) {
        console.error('删除人脉失败:', error);
        showNotification('删除失败', 'error');
    }
}

// =============== 关系连接管理 ===============
function addConnection() {
    addConnectionRow();
}

function addConnectionRow(selectedContactId = '', description = '') {
    const row = document.createElement('div');
    row.className = 'connection-row';
    
    const contactSelect = document.createElement('select');
    contactSelect.className = 'form-select form-select-sm';
    contactSelect.innerHTML = '<option value="">选择关联人脉</option>';

    contactsData.contacts.forEach(contact => {
        if (contact.id !== currentEditingId) {
            const option = document.createElement('option');
            option.value = contact.id;
            option.textContent = `${contact.name} (${contact.position || '无职位'})`;
            if (contact.id === selectedContactId) {
                option.selected = true;
            }
            contactSelect.appendChild(option);
        }
    });
    
    const descriptionInput = document.createElement('input');
    descriptionInput.type = 'text';
    descriptionInput.className = 'form-control form-control-sm';
    descriptionInput.placeholder = '关系描述 (如: 通过XX介绍)';
    descriptionInput.value = description;
    
    const removeBtn = document.createElement('button');
    removeBtn.type = 'button';
    removeBtn.className = 'btn btn-outline-danger btn-sm';
    removeBtn.innerHTML = '<i class="fas fa-times"></i>';
    removeBtn.onclick = () => row.remove();
    
    row.appendChild(contactSelect);
    row.appendChild(descriptionInput);
    row.appendChild(removeBtn);
    
    domElements.connectionsList.appendChild(row);
}

function saveConnections() {
    if (!currentEditingId) return;
    
    // 删除当前人脉的所有连接
    contactsData.connections = contactsData.connections.filter(conn => 
        conn.fromId !== currentEditingId && conn.toId !== currentEditingId
    );
    
    // 添加新的连接
    const connectionRows = domElements.connectionsList.querySelectorAll('.connection-row');
    connectionRows.forEach(row => {
        const select = row.querySelector('select');
        const input = row.querySelector('input');
        
        if (select.value && input.value.trim()) {
            contactsData.connections.push({
                id: Date.now() + Math.random(),
                fromId: currentEditingId,
                toId: parseInt(select.value),
                description: input.value.trim(),
                createdAt: getBeiJingTime()
            });
        }
    });
}

function updateConnectionsList() {
    const availableContacts = contactsData.contacts.filter(c => c.id !== currentEditingId);

    // 更新所有选择框的选项
    const selects = domElements.connectionsList.querySelectorAll('select');
    selects.forEach(select => {
        const currentValue = select.value;
        select.innerHTML = '<option value="">选择关联人脉</option>';

        availableContacts.forEach(contact => {
            const option = document.createElement('option');
            option.value = contact.id;
            option.textContent = `${contact.name} (${contact.company || contact.position || '无职位'})`;
            if (contact.id == currentValue) {
                option.selected = true;
            }
            select.appendChild(option);
        });
    });
}

// =============== 视图管理 ===============
function handleViewChange(event) {
    currentView = event.target.id.replace('View', '').toLowerCase();
    renderCurrentView();
}

function renderCurrentView() {
    // 隐藏所有视图容器
    domElements.listViewContainer.style.display = 'none';
    domElements.networkViewContainer.style.display = 'none';
    domElements.treeViewContainer.style.display = 'none';

    // 显示当前视图
    switch (currentView) {
        case 'list':
            domElements.listViewContainer.style.display = 'block';
            renderListView();
            break;
        case 'network':
            domElements.networkViewContainer.style.display = 'block';
            renderNetworkView();
            break;
        case 'tree':
            domElements.treeViewContainer.style.display = 'block';
            renderTreeView();
            break;
    }
}

// 优化的界面更新函数，避免整个界面刷新
function updateContactsDisplay() {
    switch (currentView) {
        case 'list':
            renderListView();
            break;
        case 'network':
            renderNetworkView();
            break;
        case 'tree':
            renderTreeView();
            break;
    }
}

function renderListView() {
    const filteredContacts = getFilteredContacts();

    if (filteredContacts.length === 0) {
        domElements.contactsList.innerHTML = `
            <div class="col-12">
                <div class="empty-state">
                    <i class="fas fa-users"></i>
                    <h5>暂无人脉数据</h5>
                    <p>点击"添加人脉"开始建立你的人脉网络</p>
                </div>
            </div>
        `;
        return;
    }

    domElements.contactsList.innerHTML = '';

    filteredContacts.forEach(contact => {
        const contactCard = createContactCard(contact);
        domElements.contactsList.appendChild(contactCard);
    });
}

function createContactCard(contact) {
    const col = document.createElement('div');
    col.className = 'col-md-4 col-lg-3';

    const connections = getContactConnections(contact.id);
    const connectionNames = connections.map(conn => {
        const otherContact = contactsData.contacts.find(c =>
            c.id === (conn.fromId === contact.id ? conn.toId : conn.fromId)
        );
        return otherContact ? otherContact.name : '';
    }).filter(name => name);

    const importanceStars = '⭐'.repeat(contact.importance || 3);

    col.innerHTML = `
        <div class="contact-card fade-in" onclick="showContactDetails(${contact.id})">
            <div class="contact-header">
                <div>
                    <h6 class="contact-name">${highlightText(contact.name || '')}</h6>
                    <p class="contact-position">${highlightText(contact.position || '')}</p>
                </div>
                <div class="contact-importance">${importanceStars}</div>
            </div>

            ${contact.tags && contact.tags.length > 0 ? `
                <div class="contact-tags">
                    ${contact.tags.map(tag => `<span class="contact-tag">${highlightText(tag)}</span>`).join('')}
                </div>
            ` : ''}

            ${connectionNames.length > 0 ? `
                <div class="contact-connections">
                    <small class="text-muted">关联:</small>
                    ${connectionNames.map(name => `<span class="connection-item">${name}</span>`).join('')}
                </div>
            ` : ''}

            <div class="contact-actions" onclick="event.stopPropagation()">
                <button class="btn btn-outline-primary" onclick="showEditContactModal(${contact.id})">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button class="btn btn-outline-danger" onclick="deleteContact(${contact.id})">
                    <i class="fas fa-trash"></i> 删除
                </button>
                <button class="btn btn-outline-info" onclick="focusOnContact(${contact.id})">
                    <i class="fas fa-search"></i> 关系
                </button>
            </div>
        </div>
    `;

    return col;
}

function getContactConnections(contactId) {
    return contactsData.connections.filter(conn =>
        conn.fromId === contactId || conn.toId === contactId
    );
}



function highlightText(text) {
    if (!searchQuery || !text) return text;

    const regex = new RegExp(`(${searchQuery})`, 'gi');
    return text.replace(regex, '<span class="highlight">$1</span>');
}

// =============== 搜索和筛选 ===============
function handleSearch(event) {
    searchQuery = event.target.value.toLowerCase();
    updateContactsDisplay();
}

function handleFilter() {
    activeFilters.importance = domElements.importanceFilter.value;
    activeFilters.tag = domElements.tagFilter.value;
    updateContactsDisplay();
}

function getFilteredContacts() {
    return contactsData.contacts.filter(contact => {
        // 搜索过滤
        if (searchQuery) {
            const searchText = [
                contact.name,
                contact.position,
                ...(contact.tags || [])
            ].join(' ').toLowerCase();

            if (!searchText.includes(searchQuery)) {
                return false;
            }
        }

        // 重要程度过滤
        if (activeFilters.importance && contact.importance != activeFilters.importance) {
            return false;
        }

        // 标签过滤
        if (activeFilters.tag && (!contact.tags || !contact.tags.includes(activeFilters.tag))) {
            return false;
        }

        return true;
    });
}

function clearSearch() {
    domElements.searchInput.value = '';
    searchQuery = '';
    updateContactsDisplay();
}

function resetFilters() {
    domElements.importanceFilter.value = '';
    domElements.tagFilter.value = '';
    activeFilters = { importance: '', tag: '' };
    clearSearch();
}

// =============== 网络视图 ===============
function renderNetworkView() {
    if (!networkCanvas || !networkCtx) return;

    // 准备网络数据
    prepareNetworkData();

    // 绘制网络图
    drawNetwork();
}

function prepareNetworkData() {
    const filteredContacts = getFilteredContacts();

    if (filteredContacts.length === 0) {
        networkData.nodes = [];
        networkData.links = [];
        return;
    }

    // 使用圆形布局算法
    const centerX = networkCanvas.width / 2;
    const centerY = networkCanvas.height / 2;
    const radius = Math.min(centerX, centerY) * 0.6;

    // 创建节点
    networkData.nodes = filteredContacts.map((contact, index) => {
        let x, y;

        if (filteredContacts.length === 1) {
            // 单个节点放在中心
            x = centerX;
            y = centerY;
        } else {
            // 多个节点按圆形分布
            const angle = (index * 2 * Math.PI) / filteredContacts.length;
            x = centerX + radius * Math.cos(angle);
            y = centerY + radius * Math.sin(angle);
        }

        return {
            id: contact.id,
            name: contact.name,
            x: x,
            y: y,
            radius: 20 + (contact.importance || 3) * 3,
            color: getNodeColor(contact.importance || 3),
            contact: contact
        };
    });

    // 创建连接
    networkData.links = [];
    contactsData.connections.forEach(conn => {
        const fromNode = networkData.nodes.find(n => n.id === conn.fromId);
        const toNode = networkData.nodes.find(n => n.id === conn.toId);

        if (fromNode && toNode) {
            networkData.links.push({
                from: fromNode,
                to: toNode,
                description: conn.description
            });
        }
    });
}

function getNodeColor(importance) {
    const colors = {
        5: '#dc3545', // 红色 - 非常重要
        4: '#fd7e14', // 橙色 - 重要
        3: '#0d6efd', // 蓝色 - 一般
        2: '#6c757d', // 灰色 - 较低
        1: '#adb5bd'  // 浅灰 - 很低
    };
    return colors[importance] || '#6c757d';
}

function drawNetwork() {
    // 清空画布
    networkCtx.clearRect(0, 0, networkCanvas.width, networkCanvas.height);

    if (networkData.nodes.length === 0) {
        // 显示空状态
        networkCtx.fillStyle = '#6c757d';
        networkCtx.font = '16px Arial';
        networkCtx.textAlign = 'center';
        networkCtx.textBaseline = 'middle';
        networkCtx.fillText('暂无人脉数据', networkCanvas.width / 2, networkCanvas.height / 2);
        return;
    }

    // 应用变换
    networkCtx.save();
    networkCtx.translate(networkData.offsetX, networkData.offsetY);
    networkCtx.scale(networkData.scale, networkData.scale);

    // 绘制连接线
    networkCtx.strokeStyle = '#dee2e6';
    networkCtx.lineWidth = 2;
    networkData.links.forEach(link => {
        networkCtx.beginPath();
        networkCtx.moveTo(link.from.x, link.from.y);
        networkCtx.lineTo(link.to.x, link.to.y);
        networkCtx.stroke();

        // 绘制连接描述（在连接线中点）
        if (link.description && networkData.scale > 0.5) {
            const midX = (link.from.x + link.to.x) / 2;
            const midY = (link.from.y + link.to.y) / 2;

            networkCtx.fillStyle = '#495057';
            networkCtx.font = '10px Arial';
            networkCtx.textAlign = 'center';
            networkCtx.textBaseline = 'middle';

            // 添加背景
            const textWidth = networkCtx.measureText(link.description).width;
            networkCtx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            networkCtx.fillRect(midX - textWidth/2 - 2, midY - 6, textWidth + 4, 12);

            networkCtx.fillStyle = '#495057';
            networkCtx.fillText(link.description, midX, midY);
        }
    });

    // 绘制节点
    networkData.nodes.forEach(node => {
        // 绘制节点圆圈
        networkCtx.fillStyle = node.color;
        networkCtx.beginPath();
        networkCtx.arc(node.x, node.y, node.radius, 0, 2 * Math.PI);
        networkCtx.fill();

        // 绘制节点边框
        networkCtx.strokeStyle = '#ffffff';
        networkCtx.lineWidth = 3;
        networkCtx.stroke();

        // 绘制节点文字
        networkCtx.fillStyle = '#ffffff';
        networkCtx.font = '12px Arial';
        networkCtx.textAlign = 'center';
        networkCtx.textBaseline = 'middle';

        const text = node.name.length > 6 ? node.name.substring(0, 6) + '...' : node.name;
        networkCtx.fillText(text, node.x, node.y);

        // 绘制节点名称标签
        networkCtx.fillStyle = '#212529';
        networkCtx.font = '11px Arial';
        networkCtx.fillText(node.name, node.x, node.y + node.radius + 15);

        // 绘制职位信息
        if (node.contact.position && networkData.scale > 0.7) {
            networkCtx.fillStyle = '#6c757d';
            networkCtx.font = '9px Arial';
            networkCtx.fillText(node.contact.position, node.x, node.y + node.radius + 28);
        }
    });

    networkCtx.restore();
}

// 网络视图交互
function handleNetworkMouseDown(event) {
    const rect = networkCanvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // 检查是否点击了节点
    const clickedNode = getNodeAtPosition(x, y);
    if (clickedNode) {
        showContactDetails(clickedNode.id);
        return;
    }

    // 开始拖拽画布
    networkData.isDragging = true;
    networkData.dragStart = { x, y };
}

function handleNetworkMouseMove(event) {
    if (!networkData.isDragging) return;

    const rect = networkCanvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    networkData.offsetX += x - networkData.dragStart.x;
    networkData.offsetY += y - networkData.dragStart.y;

    networkData.dragStart = { x, y };
    drawNetwork();
}

function handleNetworkMouseUp() {
    networkData.isDragging = false;
}

function handleNetworkWheel(event) {
    event.preventDefault();

    const scaleFactor = event.deltaY > 0 ? 0.9 : 1.1;
    networkData.scale = Math.max(0.1, Math.min(3, networkData.scale * scaleFactor));

    drawNetwork();
}

function getNodeAtPosition(x, y) {
    // 转换坐标
    const transformedX = (x - networkData.offsetX) / networkData.scale;
    const transformedY = (y - networkData.offsetY) / networkData.scale;

    return networkData.nodes.find(node => {
        const distance = Math.sqrt(
            Math.pow(transformedX - node.x, 2) + Math.pow(transformedY - node.y, 2)
        );
        return distance <= node.radius;
    });
}

function resetNetworkView() {
    networkData.scale = 1;
    networkData.offsetX = 0;
    networkData.offsetY = 0;
    drawNetwork();
}

function centerNetwork() {
    if (networkData.nodes.length === 0) return;

    // 计算所有节点的中心点
    const centerX = networkData.nodes.reduce((sum, node) => sum + node.x, 0) / networkData.nodes.length;
    const centerY = networkData.nodes.reduce((sum, node) => sum + node.y, 0) / networkData.nodes.length;

    // 将中心点移动到画布中心
    networkData.offsetX = networkCanvas.width / 2 - centerX * networkData.scale;
    networkData.offsetY = networkCanvas.height / 2 - centerY * networkData.scale;

    drawNetwork();
}

function zoomIn() {
    networkData.scale = Math.min(3, networkData.scale * 1.2);
    drawNetwork();
}

function zoomOut() {
    networkData.scale = Math.max(0.1, networkData.scale / 1.2);
    drawNetwork();
}

// =============== 关系树视图 ===============
function renderTreeView() {
    const centerContactId = parseInt(domElements.treeCenterSelect.value);

    if (!centerContactId) {
        domElements.treeContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-sitemap"></i>
                <h5>请选择中心人脉</h5>
                <p>选择一个人脉作为关系树的中心节点</p>
            </div>
        `;
        return;
    }

    const centerContact = contactsData.contacts.find(c => c.id === centerContactId);
    if (!centerContact) return;

    const treeData = buildTreeData(centerContactId);
    domElements.treeContainer.innerHTML = '';

    const treeElement = createTreeNode(centerContact, treeData, true);
    domElements.treeContainer.appendChild(treeElement);
}

function buildTreeData(centerContactId, visited = new Set()) {
    if (visited.has(centerContactId)) return [];
    visited.add(centerContactId);

    const connections = contactsData.connections.filter(conn =>
        conn.fromId === centerContactId || conn.toId === centerContactId
    );

    return connections.map(conn => {
        const otherContactId = conn.fromId === centerContactId ? conn.toId : conn.fromId;
        const otherContact = contactsData.contacts.find(c => c.id === otherContactId);

        if (!otherContact) return null;

        return {
            contact: otherContact,
            description: conn.description,
            children: buildTreeData(otherContactId, new Set(visited))
        };
    }).filter(item => item !== null);
}

function createTreeNode(contact, children = [], isCenter = false) {
    const nodeDiv = document.createElement('div');
    nodeDiv.className = `tree-node ${isCenter ? 'center-node' : ''}`;

    const hasChildren = children.length > 0;
    const nodeId = `tree-node-${contact.id}`;

    nodeDiv.innerHTML = `
        <div class="tree-node-header" onclick="toggleTreeNode('${nodeId}')">
            <div>
                <span class="tree-node-name">${contact.name}</span>
                <span class="tree-node-relation">${contact.position || ''}</span>
            </div>
            <div>
                ${hasChildren ? `<button class="tree-toggle" id="toggle-${nodeId}">
                    <i class="fas fa-chevron-down"></i>
                </button>` : ''}
                <button class="btn btn-outline-primary btn-sm ms-2" onclick="event.stopPropagation(); showContactDetails(${contact.id})">
                    <i class="fas fa-eye"></i>
                </button>
            </div>
        </div>
        ${hasChildren ? `<div class="tree-children" id="children-${nodeId}">
            ${children.map(child => `
                <div class="mb-2">
                    <small class="text-muted">关系: ${child.description}</small>
                    ${createTreeNodeHTML(child.contact, child.children)}
                </div>
            `).join('')}
        </div>` : ''}
    `;

    return nodeDiv;
}

function createTreeNodeHTML(contact, children = []) {
    const hasChildren = children.length > 0;
    const nodeId = `tree-node-${contact.id}`;

    return `
        <div class="tree-node">
            <div class="tree-node-header" onclick="toggleTreeNode('${nodeId}')">
                <div>
                    <span class="tree-node-name">${contact.name}</span>
                    <span class="tree-node-relation">${contact.position || ''}</span>
                </div>
                <div>
                    ${hasChildren ? `<button class="tree-toggle" id="toggle-${nodeId}">
                        <i class="fas fa-chevron-down"></i>
                    </button>` : ''}
                    <button class="btn btn-outline-primary btn-sm ms-2" onclick="event.stopPropagation(); showContactDetails(${contact.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
            ${hasChildren ? `<div class="tree-children" id="children-${nodeId}" style="display: none;">
                ${children.map(child => `
                    <div class="mb-2">
                        <small class="text-muted">关系: ${child.description}</small>
                        ${createTreeNodeHTML(child.contact, child.children)}
                    </div>
                `).join('')}
            </div>` : ''}
        </div>
    `;
}

function toggleTreeNode(nodeId) {
    const childrenElement = document.getElementById(`children-${nodeId}`);
    const toggleButton = document.getElementById(`toggle-${nodeId}`);

    if (childrenElement && toggleButton) {
        const isVisible = childrenElement.style.display !== 'none';
        childrenElement.style.display = isVisible ? 'none' : 'block';
        toggleButton.innerHTML = isVisible ?
            '<i class="fas fa-chevron-right"></i>' :
            '<i class="fas fa-chevron-down"></i>';
    }
}

function handleTreeCenterChange() {
    renderTreeView();
}

function expandAllTree() {
    const childrenElements = domElements.treeContainer.querySelectorAll('.tree-children');
    const toggleButtons = domElements.treeContainer.querySelectorAll('.tree-toggle');

    childrenElements.forEach(element => {
        element.style.display = 'block';
    });

    toggleButtons.forEach(button => {
        button.innerHTML = '<i class="fas fa-chevron-down"></i>';
    });
}

function collapseAllTree() {
    const childrenElements = domElements.treeContainer.querySelectorAll('.tree-children');
    const toggleButtons = domElements.treeContainer.querySelectorAll('.tree-toggle');

    childrenElements.forEach(element => {
        element.style.display = 'none';
    });

    toggleButtons.forEach(button => {
        button.innerHTML = '<i class="fas fa-chevron-right"></i>';
    });
}

// =============== 辅助功能 ===============
function showContactDetails(contactId) {
    const contact = contactsData.contacts.find(c => c.id === contactId);
    if (!contact) return;

    // 这里可以显示详细信息模态框
    // 暂时使用编辑模态框
    showEditContactModal(contactId);
}

function focusOnContact(contactId) {
    // 切换到关系树视图并以该人脉为中心
    document.getElementById('treeView').checked = true;
    currentView = 'tree';
    domElements.treeCenterSelect.value = contactId;
    renderCurrentView();
}

function updateStatistics() {
    const now = new Date(getBeiJingTime());
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    contactsData.statistics = {
        total: contactsData.contacts.length,
        recentCount: contactsData.contacts.filter(c =>
            new Date(c.createdAt) >= thisMonth
        ).length,
        importantCount: contactsData.contacts.filter(c =>
            (c.importance || 3) >= 4
        ).length,
        connectionCount: contactsData.connections.length
    };

    // 更新界面显示
    domElements.totalContacts.textContent = contactsData.statistics.total;
    domElements.recentContacts.textContent = contactsData.statistics.recentCount;
    domElements.importantContacts.textContent = contactsData.statistics.importantCount;
    domElements.connectionCount.textContent = contactsData.statistics.connectionCount;
}

function updateTagFilter() {
    const currentValue = domElements.tagFilter.value;
    domElements.tagFilter.innerHTML = '<option value="">所有标签</option>';

    Array.from(contactsData.tags).sort().forEach(tag => {
        const option = document.createElement('option');
        option.value = tag;
        option.textContent = tag;
        if (tag === currentValue) {
            option.selected = true;
        }
        domElements.tagFilter.appendChild(option);
    });
}

function updateTreeCenterSelect() {
    const currentValue = domElements.treeCenterSelect.value;
    domElements.treeCenterSelect.innerHTML = '<option value="">选择中心人脉</option>';

    contactsData.contacts.forEach(contact => {
        const option = document.createElement('option');
        option.value = contact.id;
        option.textContent = `${contact.name} (${contact.position || '无职位'})`;
        if (contact.id == currentValue) {
            option.selected = true;
        }
        domElements.treeCenterSelect.appendChild(option);
    });
}



// =============== 通知系统 ===============
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show`;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';

    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // 自动移除通知
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}


