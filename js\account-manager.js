// 账号管理主逻辑

class AccountManager {
    constructor() {
        this.currentFilters = {
            type: 'all',
            platform: 'all',
            status: 'all',
            search: ''
        };
        this.selectedAccounts = new Set();
        this.passwordVisibility = new Map();
        this.showExpiredAccounts = false; // 默认隐藏过期账号
        this.debugMode = true; // 启用调试模式

        this.init();
    }

    // 调试方法：记录筛选器状态变化
    logFilterChange(source, oldFilters, newFilters) {
        if (this.debugMode) {
            console.log(`[${source}] 筛选器状态变化:`, {
                old: { ...oldFilters },
                new: { ...newFilters }
            });
        }
    }

    // 清除搜索框并重置搜索筛选
    clearSearch() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = '';
            const oldFilters = { ...this.currentFilters };
            this.currentFilters.search = '';
            this.logFilterChange('手动清除搜索', oldFilters, this.currentFilters);
            this.loadAccounts();
        }
    }

    // 初始化
    init() {
        // 确保搜索框是空的
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = '';
            this.currentFilters.search = '';
        }

        // 先更新筛选器，再绑定事件
        this.updateTypeFilters();
        this.updatePlatformFilters();
        this.updatePlatformDatalist();
        this.bindEvents();
        this.loadAccounts();
        this.updateStatistics();

        // 设置显示过期账号开关的初始状态
        const showExpiredSwitch = document.getElementById('showExpiredAccounts');
        if (showExpiredSwitch) {
            showExpiredSwitch.checked = this.showExpiredAccounts;
        }
    }

    // 绑定事件
    bindEvents() {
        // 搜索框事件
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            // 防止自动填充
            searchInput.setAttribute('autocomplete', 'off');
            searchInput.setAttribute('autocorrect', 'off');
            searchInput.setAttribute('autocapitalize', 'off');
            searchInput.setAttribute('spellcheck', 'false');

            searchInput.addEventListener('input', debounce((e) => {
                const oldFilters = { ...this.currentFilters };
                this.currentFilters.search = e.target.value;
                this.logFilterChange('搜索框输入', oldFilters, this.currentFilters);
                this.loadAccounts();
            }, 300));

            // 监听搜索框的值变化（包括自动填充）
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                        const currentValue = searchInput.value;
                        if (currentValue !== this.currentFilters.search) {
                            console.log('检测到搜索框值被外部修改:', currentValue);
                            const oldFilters = { ...this.currentFilters };
                            this.currentFilters.search = currentValue;
                            this.logFilterChange('搜索框外部修改', oldFilters, this.currentFilters);
                            this.loadAccounts();
                        }
                    }
                });
            });

            // 监听搜索框的属性变化
            observer.observe(searchInput, {
                attributes: true,
                attributeFilter: ['value']
            });

            // 定期检查搜索框的值是否被意外修改
            setInterval(() => {
                const currentValue = searchInput.value;
                if (currentValue !== this.currentFilters.search) {
                    console.log('定期检查发现搜索框值被修改:', currentValue);
                    const oldFilters = { ...this.currentFilters };
                    this.currentFilters.search = currentValue;
                    this.logFilterChange('搜索框定期检查', oldFilters, this.currentFilters);
                    this.loadAccounts();
                }
            }, 1000);
        }

        // 筛选按钮事件
        this.bindFilterEvents();

        // 表单提交事件
        const addForm = document.getElementById('addAccountForm');
        if (addForm) {
            addForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.addAccounts();
            });
        }

        // 添加表单状态变化事件
        const addStatus = document.getElementById('status');
        if (addStatus) {
            addStatus.addEventListener('change', (e) => {
                const rentInfo = document.getElementById('addRentInfo');
                rentInfo.style.display = e.target.value === 'rented' ? 'block' : 'none';
            });
        }

        // 全选复选框事件
        const selectAllCheckbox = document.getElementById('selectAllAccounts');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                const checkboxes = document.querySelectorAll('.account-checkbox');
                checkboxes.forEach(cb => cb.checked = e.target.checked);
                this.updateBatchSelection();
            });
        }

        // 编辑模态框关闭事件
        const editModal = document.getElementById('editAccountModal');
        if (editModal) {
            editModal.addEventListener('hidden.bs.modal', () => {
                this.resetEditForm();
            });
        }
    }

    // 绑定筛选事件
    bindFilterEvents() {
        // 状态筛选（这些是静态的HTML按钮）
        document.querySelectorAll('#statusFilters .filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const oldFilters = { ...this.currentFilters };
                // 移除其他按钮的active类
                document.querySelectorAll('#statusFilters .filter-btn').forEach(b => b.classList.remove('active'));
                // 添加当前按钮的active类
                e.target.classList.add('active');
                // 更新筛选条件
                this.currentFilters.status = e.target.dataset.status;
                this.logFilterChange('状态筛选', oldFilters, this.currentFilters);
                this.loadAccounts();
            });
        });

        // 类型筛选 - 绑定"全部"按钮的事件
        const typeAllButton = document.querySelector('#typeFilters [data-type="all"]');
        if (typeAllButton) {
            typeAllButton.addEventListener('click', (e) => {
                const oldFilters = { ...this.currentFilters };
                // 移除其他按钮的active类
                document.querySelectorAll('#typeFilters .filter-btn').forEach(b => b.classList.remove('active'));
                // 添加当前按钮的active类
                e.target.classList.add('active');
                // 更新筛选条件
                this.currentFilters.type = e.target.dataset.type;
                this.logFilterChange('类型全部', oldFilters, this.currentFilters);
                this.loadAccounts();
            });
        }

        // 平台筛选 - 绑定"全部"按钮的事件
        const platformAllButton = document.querySelector('#platformFilters [data-platform="all"]');
        if (platformAllButton) {
            platformAllButton.addEventListener('click', (e) => {
                const oldFilters = { ...this.currentFilters };
                // 移除其他按钮的active类
                document.querySelectorAll('#platformFilters .filter-btn').forEach(b => b.classList.remove('active'));
                // 添加当前按钮的active类
                e.target.classList.add('active');
                // 更新筛选条件
                this.currentFilters.platform = e.target.dataset.platform;
                this.logFilterChange('平台全部', oldFilters, this.currentFilters);
                this.loadAccounts();
            });
        }

        // 显示过期账号开关
        const showExpiredSwitch = document.getElementById('showExpiredAccounts');
        if (showExpiredSwitch) {
            showExpiredSwitch.addEventListener('change', (e) => {
                this.showExpiredAccounts = e.target.checked;
                this.loadAccounts();
                this.updateExpiredStatCard();
            });
        }
    }

    // 加载账号列表
    loadAccounts() {
        const container = document.getElementById('accountsContainer');
        if (!container) return;

        let accounts = accountDataManager.filterAccounts(this.currentFilters);

        // 如果不显示过期账号，则过滤掉过期账号
        if (!this.showExpiredAccounts) {
            accounts = accounts.filter(account => account.status !== 'expired');
        }

        // 调试信息：记录当前筛选条件和结果数量
        console.log('当前筛选条件:', this.currentFilters);
        console.log('筛选后账号数量:', accounts.length);

        if (accounts.length === 0) {
            container.innerHTML = this.getEmptyState();
            return;
        }

        container.innerHTML = accounts.map(account => this.createAccountRow(account)).join('');
    }

    // 格式化价值范围显示
    formatValueRange(valueMin, valueMax) {
        const min = parseFloat(valueMin) || 0;
        const max = parseFloat(valueMax) || 0;

        if (min === 0 && max === 0) {
            return '-';
        } else if (min === max) {
            return `¥${min}`;
        } else if (min === 0) {
            return `≤¥${max}`;
        } else if (max === 0) {
            return `≥¥${min}`;
        } else {
            return `¥${min}-${max}`;
        }
    }

    // 创建账号表格行
    createAccountRow(account) {
        const isPasswordVisible = this.passwordVisibility.get(account.id) || false;
        const displayPassword = isPasswordVisible ? account.password : '••••••••';

        // 为过期账号添加半透明样式
        const fadedClass = account.status === 'expired' && this.showExpiredAccounts ? 'faded' : '';

        return `
            <tr class="account-row ${getStatusClass(account.status)} ${fadedClass}" data-account-id="${account.id}">
                <td>
                    <input type="checkbox" class="form-check-input account-checkbox" value="${account.id}">
                </td>
                <td data-label="类型/平台">
                    <div class="platform-info">
                        <i class="${getPlatformIcon(account.platform, account.type)} platform-icon"></i>
                        <div>
                            <div class="platform-name">${account.platform}</div>
                            <div class="account-type">${account.type}</div>
                        </div>
                    </div>
                </td>
                <td data-label="账号信息">
                    <div class="account-info">
                        <span class="account-username">${account.username}</span>
                        <button class="btn btn-link btn-sm p-0" onclick="copyToClipboard('${account.username}')" title="复制账号">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </td>
                <td data-label="密码">
                    <div class="password-field">
                        <span class="password-text">${displayPassword}</span>
                        <button class="btn btn-link btn-sm p-0" onclick="accountManager.togglePassword('${account.id}')" title="显示/隐藏密码">
                            <i class="fas ${isPasswordVisible ? 'fa-eye-slash' : 'fa-eye'}"></i>
                        </button>
                        <button class="btn btn-link btn-sm p-0" onclick="copyToClipboard('${account.password}')" title="复制密码">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </td>
                <td data-label="状态">
                    <span class="status-badge ${getStatusClass(account.status)}">
                        ${getStatusText(account.status)}
                    </span>
                </td>
                <td data-label="价值">
                    ${this.formatValueRange(account.valueMin, account.valueMax)}
                </td>
                <td data-label="备注">
                    <span class="table-meta" title="${account.notes || ''}">${account.notes ? (account.notes.length > 20 ? account.notes.substring(0, 20) + '...' : account.notes) : '-'}</span>
                </td>
                <td data-label="操作">
                    <div class="action-buttons">
                        <button class="btn btn-primary btn-sm" onclick="accountManager.editAccount('${account.id}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="accountManager.deleteAccount('${account.id}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    // 获取空状态显示
    getEmptyState() {
        return `
            <tr>
                <td colspan="8" class="text-center py-5">
                    <div class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <h4>暂无账号</h4>
                        <p>点击"添加账号"按钮开始管理您的账号</p>
                        <button class="btn btn-primary" onclick="showAddAccountModal()">
                            <i class="fas fa-plus me-1"></i>添加第一个账号
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    // 切换密码显示
    togglePassword(accountId) {
        const isVisible = this.passwordVisibility.get(accountId) || false;
        this.passwordVisibility.set(accountId, !isVisible);

        // 只更新特定行的密码显示，而不是重新渲染整个列表
        const accountRow = document.querySelector(`tr[data-account-id="${accountId}"]`);
        if (accountRow) {
            const passwordField = accountRow.querySelector('.password-field');
            const passwordText = passwordField.querySelector('.password-text');
            const toggleButton = passwordField.querySelector('button[onclick*="togglePassword"]');
            const toggleIcon = toggleButton.querySelector('i');

            const account = accountDataManager.getAccountById(accountId);
            if (account) {
                const newIsVisible = this.passwordVisibility.get(accountId);
                passwordText.textContent = newIsVisible ? account.password : '••••••••';
                toggleIcon.className = `fas ${newIsVisible ? 'fa-eye-slash' : 'fa-eye'}`;
            }
        }
    }

    // 添加账号（支持批量）
    addAccounts() {
        // 获取基本信息
        const baseData = {
            type: document.getElementById('accountType').value.trim(),
            platform: document.getElementById('platform').value.trim()
        };

        // 验证基本信息
        if (!baseData.type || !baseData.platform) {
            showToast('请填写账号类型和平台名称', 'warning');
            return;
        }

        // 获取所有账号行的信息
        const accountRows = document.querySelectorAll('#accountRows .account-row');
        const accounts = [];
        let hasError = false;

        accountRows.forEach((row, index) => {
            const usernameInput = row.querySelector('.username-input');
            const passwordInput = row.querySelector('.password-input');
            const statusInput = row.querySelector('.status-input');
            const valueMinInput = row.querySelector('.value-min-input');
            const valueMaxInput = row.querySelector('.value-max-input');
            const notesInput = row.querySelector('.notes-input');

            const username = usernameInput.value.trim();
            const password = passwordInput.value.trim();
            const status = statusInput.value;
            const valueMin = parseFloat(valueMinInput.value) || 0;
            const valueMax = parseFloat(valueMaxInput.value) || 0;
            const notes = notesInput.value.trim();

            if (!username || !password) {
                showToast(`第${index + 1}行：用户名和密码不能为空`, 'error');
                hasError = true;
                return;
            }

            const accountData = {
                ...baseData,
                username,
                password,
                status,
                valueMin: valueMin,
                valueMax: valueMax,
                notes
            };

            // 添加租赁信息（如果状态为已租）
            if (status === 'rented') {
                const renterInput = document.getElementById('renter');
                const rentPriceInput = document.getElementById('rentPrice');
                const rentStartDateInput = document.getElementById('rentStartDate');
                const rentEndDateInput = document.getElementById('rentEndDate');

                if (renterInput) accountData.renter = renterInput.value;
                if (rentPriceInput) accountData.rentPrice = rentPriceInput.value;
                if (rentStartDateInput) accountData.rentStartDate = rentStartDateInput.value;
                if (rentEndDateInput) accountData.rentEndDate = rentEndDateInput.value;
            }

            accounts.push(accountData);
        });

        if (hasError || accounts.length === 0) {
            return;
        }

        // 批量添加账号
        let successCount = 0;
        accounts.forEach(accountData => {
            try {
                accountDataManager.addAccount(accountData);
                successCount++;
            } catch (error) {
                console.error('添加账号失败:', error);
            }
        });

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('addAccountModal'));
        modal.hide();

        // 刷新界面
        this.loadAccounts();
        this.updateStatistics();
        // 只在添加了新类型或平台时才更新筛选器
        this.updateTypeFilters();
        this.updatePlatformFilters();
        this.updatePlatformDatalist();

        showToast(`成功添加 ${successCount} 个账号`, 'success');
    }

    // 编辑账号
    editAccount(accountId) {
        const account = accountDataManager.getAccountById(accountId);
        if (!account) {
            return;
        }

        // 填充共享的平台信息
        document.getElementById('editAccountType').value = account.type;
        document.getElementById('editPlatform').value = account.platform;

        // 清空账号行容器
        const accountRows = document.getElementById('editAccountRows');
        accountRows.innerHTML = '';

        // 添加第一行并填充现有账号信息
        this.addEditAccountRow(account);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('editAccountModal'));
        modal.show();

        // 延迟处理自动填充问题
        setTimeout(() => {
            const firstRow = accountRows.querySelector('.account-row');
            if (firstRow) {
                firstRow.querySelector('.username-input').value = account.username;
                firstRow.querySelector('.password-input').value = account.password;
            }
        }, 100);
    }

    // 更新账号（保持向后兼容）
    updateAccount() {
        this.updateAccounts();
    }

    // 更新多个账号
    updateAccounts() {
        const accountType = document.getElementById('editAccountType').value.trim();
        const platform = document.getElementById('editPlatform').value.trim();

        if (!accountType || !platform) {
            showToast('请填写账号类型和平台名称', 'error');
            return;
        }

        const accountRows = document.querySelectorAll('#editAccountRows .account-row');
        let hasError = false;
        let updateCount = 0;
        let addCount = 0;

        accountRows.forEach((row, index) => {
            const username = row.querySelector('.username-input').value.trim();
            const password = row.querySelector('.password-input').value.trim();
            const status = row.querySelector('.status-input').value;
            const valueMin = parseFloat(row.querySelector('.value-min-input').value) || 0;
            const valueMax = parseFloat(row.querySelector('.value-max-input').value) || 0;
            const notes = row.querySelector('.notes-input').value.trim();

            if (!username || !password) {
                showToast(`第 ${index + 1} 行：用户名和密码不能为空`, 'error');
                hasError = true;
                return;
            }

            const accountData = {
                type: accountType,
                platform: platform,
                username: username,
                password: password,
                status: status,
                valueMin: valueMin,
                valueMax: valueMax,
                notes: notes
            };

            // 检查是否是现有账号（第一行且有account-id）
            const accountId = row.dataset.accountId;
            if (accountId) {
                // 更新现有账号
                try {
                    accountDataManager.updateAccount(accountId, accountData);
                    updateCount++;
                } catch (error) {
                    console.error('更新账号失败:', error);
                    hasError = true;
                }
            } else {
                // 添加新账号
                try {
                    const newAccount = accountDataManager.addAccount(accountData);
                    if (newAccount) {
                        addCount++;
                    }
                } catch (error) {
                    console.error('添加账号失败:', error);
                    hasError = true;
                }
            }
        });

        if (hasError) {
            return;
        }

        try {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('editAccountModal'));
            modal.hide();

            // 重置表单
            this.resetEditForm();

            // 刷新列表和统计
            this.loadAccounts();
            this.updateStatistics();
            // 只在添加了新类型或平台时才更新筛选器
            this.updateTypeFilters();
            this.updatePlatformFilters();
            this.updatePlatformDatalist();

            // 显示成功消息
            let message = '';
            if (updateCount > 0 && addCount > 0) {
                message = `成功更新 ${updateCount} 个账号，添加 ${addCount} 个账号`;
            } else if (updateCount > 0) {
                message = `成功更新 ${updateCount} 个账号`;
            } else if (addCount > 0) {
                message = `成功添加 ${addCount} 个账号`;
            }

            if (message) {
                showToast(message, 'success');
            }
        } catch (error) {
            console.error('保存账号失败:', error);
            showToast('保存账号失败', 'error');
        }
    }

    // 删除账号
    deleteAccount(accountId) {
        showConfirm('确定要删除这个账号吗？此操作不可恢复。', () => {
            if (accountDataManager.deleteAccount(accountId)) {
                this.loadAccounts();
                this.updateStatistics();
            }
        });
    }



    // 更新统计信息
    updateStatistics() {
        const stats = accountDataManager.getStatistics();

        document.getElementById('totalAccounts').textContent = stats.totalAccounts;
        document.getElementById('activeAccounts').textContent = stats.activeAccounts;
        document.getElementById('pendingAccounts').textContent = stats.pendingAccounts;
        document.getElementById('rentedAccounts').textContent = stats.rentedAccounts;
        document.getElementById('soldAccounts').textContent = stats.soldAccounts;
        document.getElementById('expiredAccounts').textContent = stats.expiredAccounts;

        // 更新过期账号统计卡片状态
        this.updateExpiredStatCard();
    }

    // 更新过期账号统计卡片状态
    updateExpiredStatCard() {
        const expiredStatCard = document.getElementById('expiredStatCard');
        const expiredStatText = expiredStatCard.querySelector('p');

        if (this.showExpiredAccounts) {
            expiredStatCard.classList.remove('disabled');
            expiredStatText.innerHTML = '过期账号';
        } else {
            expiredStatCard.classList.add('disabled');
            expiredStatText.innerHTML = '过期账号 <small class="text-muted">(已隐藏)</small>';
        }
    }

    // 显示批量操作模态框
    showBatchOperations() {
        const accounts = accountDataManager.getAllAccounts();

        // 按平台排序，使同一平台的账号排在一起
        const sortedAccounts = accounts.sort((a, b) => {
            // 首先按平台名称排序
            const platformCompare = a.platform.localeCompare(b.platform);
            if (platformCompare !== 0) {
                return platformCompare;
            }

            // 如果平台相同，再按账号类型排序
            const typeCompare = a.type.localeCompare(b.type);
            if (typeCompare !== 0) {
                return typeCompare;
            }

            // 如果平台和类型都相同，按用户名排序
            return a.username.localeCompare(b.username);
        });

        const container = document.getElementById('batchAccountList');

        container.innerHTML = sortedAccounts.map(account => `
            <div class="form-check mb-2">
                <input class="form-check-input batch-account-checkbox" type="checkbox"
                       value="${account.id}" id="batch_${account.id}">
                <label class="form-check-label d-flex align-items-center" for="batch_${account.id}">
                    <i class="${getPlatformIcon(account.platform, account.type)} me-2"></i>
                    <span class="me-2">${account.platform}</span>
                    <span class="text-muted small">${account.username}</span>
                    <span class="badge ${getStatusClass(account.status)} ms-auto">
                        ${getStatusText(account.status)}
                    </span>
                </label>
            </div>
        `).join('');

        // 绑定选择事件
        container.querySelectorAll('.batch-account-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', this.updateBatchSelection.bind(this));
        });

        // 添加全选/取消全选按钮
        const selectAllBtn = document.createElement('div');
        selectAllBtn.className = 'mb-3 border-bottom pb-2';
        selectAllBtn.innerHTML = `
            <button class="btn btn-sm btn-outline-primary me-2" onclick="accountManager.selectAllBatch()">
                <i class="fas fa-check-square me-1"></i>全选
            </button>
            <button class="btn btn-sm btn-outline-secondary" onclick="accountManager.clearBatchSelection()">
                <i class="fas fa-square me-1"></i>取消全选
            </button>
        `;
        container.insertBefore(selectAllBtn, container.firstChild);

        this.updateBatchSelection();

        const modal = new bootstrap.Modal(document.getElementById('batchOperationModal'));
        modal.show();
    }

    // 更新批量选择计数
    updateBatchSelection() {
        const checkboxes = document.querySelectorAll('.batch-account-checkbox');
        const selectedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
        document.getElementById('selectedCount').textContent = selectedCount;

        this.selectedAccounts.clear();
        checkboxes.forEach(cb => {
            if (cb.checked) {
                this.selectedAccounts.add(cb.value);
            }
        });
    }

    // 全选批量账号
    selectAllBatch() {
        document.querySelectorAll('.batch-account-checkbox').forEach(cb => {
            cb.checked = true;
        });
        this.updateBatchSelection();
    }

    // 清除批量选择
    clearBatchSelection() {
        document.querySelectorAll('.batch-account-checkbox').forEach(cb => {
            cb.checked = false;
        });
        this.updateBatchSelection();
    }

    // 执行批量操作
    executeBatchOperation() {
        const operation = document.getElementById('batchOperation').value;
        const selectedIds = Array.from(this.selectedAccounts);

        if (!operation) {
            return;
        }

        if (selectedIds.length === 0) {
            return;
        }

        const operationText = {
            'status-active': '设为活跃',
            'status-pending': '设为待处理',
            'status-rented': '设为已租',
            'status-sold': '设为已售',
            'status-expired': '设为过期',
            'delete': '删除'
        }[operation];

        showConfirm(`确定要对 ${selectedIds.length} 个账号执行"${operationText}"操作吗？`, () => {
            try {
                let result = 0;

                if (operation === 'delete') {
                    result = accountDataManager.batchDeleteAccounts(selectedIds);
                } else if (operation.startsWith('status-')) {
                    const newStatus = operation.replace('status-', '');
                    result = accountDataManager.batchUpdateStatus(selectedIds, newStatus);
                }

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('batchOperationModal'));
                modal.hide();

                // 刷新列表和统计
                this.loadAccounts();
                this.updateStatistics();
                this.selectedAccounts.clear();

            } catch (error) {
                console.error('批量操作失败:', error);
            }
        });
    }

    // 更新类型筛选器
    updateTypeFilters() {
        const categories = accountDataManager.data.categories;
        const typeFilters = document.getElementById('typeFilters');

        // 获取现有的动态按钮（排除"全部"按钮）
        const existingButtons = Array.from(typeFilters.querySelectorAll('.filter-btn:not([data-type="all"])'));
        const existingTypes = existingButtons.map(btn => btn.dataset.type);

        // 移除不再存在的类型按钮
        existingButtons.forEach(btn => {
            if (!categories.includes(btn.dataset.type)) {
                btn.remove();
            }
        });

        // 添加新的类型按钮
        categories.forEach(type => {
            if (!existingTypes.includes(type)) {
                const button = document.createElement('button');
                button.className = 'filter-btn';
                button.setAttribute('data-type', type);
                button.textContent = type;
                button.addEventListener('click', (e) => {
                    const oldFilters = { ...this.currentFilters };
                    // 移除其他按钮的active类
                    document.querySelectorAll('#typeFilters .filter-btn').forEach(b => b.classList.remove('active'));
                    // 添加当前按钮的active类
                    e.target.classList.add('active');
                    // 更新筛选条件
                    this.currentFilters.type = e.target.dataset.type;
                    this.logFilterChange('类型筛选', oldFilters, this.currentFilters);
                    this.loadAccounts();
                });
                typeFilters.appendChild(button);
            }
        });

        // 更新所有按钮的active状态
        typeFilters.querySelectorAll('.filter-btn').forEach(btn => {
            if (btn.dataset.type === this.currentFilters.type) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });

        // 调试信息：记录当前类型筛选状态
        console.log('类型筛选器更新完成，当前筛选:', this.currentFilters.type);

        // 更新datalist选项
        this.updateTypeDatalist();
    }

    // 更新平台筛选器
    updatePlatformFilters() {
        const accounts = accountDataManager.getAllAccounts();
        const platforms = [...new Set(accounts.map(acc => acc.platform))].sort();
        const platformFilters = document.getElementById('platformFilters');

        // 获取现有的动态按钮（排除"全部"按钮）
        const existingButtons = Array.from(platformFilters.querySelectorAll('.filter-btn:not([data-platform="all"])'));
        const existingPlatforms = existingButtons.map(btn => btn.dataset.platform);

        // 移除不再存在的平台按钮
        existingButtons.forEach(btn => {
            if (!platforms.includes(btn.dataset.platform)) {
                btn.remove();
            }
        });

        // 添加新的平台按钮
        platforms.forEach(platform => {
            if (!existingPlatforms.includes(platform)) {
                const button = document.createElement('button');
                button.className = 'filter-btn';
                button.setAttribute('data-platform', platform);
                button.textContent = platform;
                button.addEventListener('click', (e) => {
                    const oldFilters = { ...this.currentFilters };
                    // 移除其他按钮的active类
                    document.querySelectorAll('#platformFilters .filter-btn').forEach(b => b.classList.remove('active'));
                    // 添加当前按钮的active类
                    e.target.classList.add('active');
                    // 更新筛选条件
                    this.currentFilters.platform = e.target.dataset.platform;
                    this.logFilterChange('平台筛选', oldFilters, this.currentFilters);
                    this.loadAccounts();
                });
                platformFilters.appendChild(button);
            }
        });

        // 更新所有按钮的active状态
        platformFilters.querySelectorAll('.filter-btn').forEach(btn => {
            if (btn.dataset.platform === this.currentFilters.platform) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });

        // 调试信息：记录当前平台筛选状态
        console.log('平台筛选器更新完成，当前筛选:', this.currentFilters.platform);
    }

    // 更新类型datalist
    updateTypeDatalist() {
        const categories = accountDataManager.data.categories;
        const addDatalist = document.getElementById('typeDatalist');
        const editDatalist = document.getElementById('editTypeDatalist');

        [addDatalist, editDatalist].forEach(datalist => {
            if (datalist) {
                datalist.innerHTML = categories.map(type =>
                    `<option value="${type}">`
                ).join('');
            }
        });
    }

    // 更新平台datalist
    updatePlatformDatalist() {
        const platforms = accountDataManager.getAllPlatforms();
        const addDatalist = document.getElementById('platformDatalist');
        const editDatalist = document.getElementById('editPlatformDatalist');

        [addDatalist, editDatalist].forEach(datalist => {
            if (datalist) {
                datalist.innerHTML = platforms.map(platform =>
                    `<option value="${platform}">`
                ).join('');
            }
        });
    }

    // 显示类型管理模态框
    showTypeManagerModal() {
        this.loadExistingTypes();
        const modal = new bootstrap.Modal(document.getElementById('typeManagerModal'));
        modal.show();

        // 绑定回车键事件
        const newTypeInput = document.getElementById('newTypeName');
        newTypeInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.addAccountType();
            }
        });

        // 聚焦到输入框
        setTimeout(() => newTypeInput.focus(), 300);
    }

    // 加载现有类型
    loadExistingTypes() {
        const categories = accountDataManager.data.categories;
        const container = document.getElementById('existingTypes');

        if (categories.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-3">
                    <i class="fas fa-inbox"></i>
                    <p class="mb-0">暂无自定义类型</p>
                </div>
            `;
            return;
        }

        container.innerHTML = categories.map(type => `
            <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                <span>
                    <i class="fas fa-user-circle me-2"></i>
                    ${type}
                </span>
                <button class="btn btn-danger btn-sm" onclick="accountManager.deleteAccountType('${type}')" title="删除类型">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `).join('');
    }

    // 添加账号类型
    addAccountType() {
        const newTypeName = document.getElementById('newTypeName').value.trim();

        if (!newTypeName) {
            return;
        }

        if (accountDataManager.data.categories.includes(newTypeName)) {
            return;
        }

        accountDataManager.data.categories.push(newTypeName);
        accountDataManager.saveData();

        // 清空输入框
        document.getElementById('newTypeName').value = '';

        // 刷新显示
        this.loadExistingTypes();
        this.updateTypeFilters();
        this.updatePlatformFilters();
    }

    // 删除账号类型
    deleteAccountType(typeName) {
        showConfirm(`确定要删除类型"${typeName}"吗？`, () => {
            const index = accountDataManager.data.categories.indexOf(typeName);
            if (index > -1) {
                accountDataManager.data.categories.splice(index, 1);
                accountDataManager.saveData();

                // 刷新显示
                this.loadExistingTypes();
                this.updateTypeFilters();
                this.updatePlatformFilters();
                this.updatePlatformDatalist();
                this.updatePlatformDatalist();
            }
        });
    }

    // 显示平台管理模态框
    showPlatformManagerModal() {
        this.loadExistingPlatforms();
        const modal = new bootstrap.Modal(document.getElementById('platformManagerModal'));
        modal.show();

        // 绑定回车键事件
        const newPlatformInput = document.getElementById('newPlatformName');
        newPlatformInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.addPlatform();
            }
        });

        // 聚焦到输入框
        setTimeout(() => newPlatformInput.focus(), 300);
    }

    // 加载现有平台
    loadExistingPlatforms() {
        const platforms = accountDataManager.getAllPlatforms();
        const container = document.getElementById('existingPlatforms');

        if (platforms.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-3">
                    <i class="fas fa-inbox"></i>
                    <p class="mb-0">暂无平台记录</p>
                </div>
            `;
            return;
        }

        container.innerHTML = platforms.map(platform => `
            <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                <span>
                    <i class="fas fa-server me-2"></i>
                    ${platform}
                </span>
                <button class="btn btn-danger btn-sm" onclick="accountManager.deletePlatform('${platform}')" title="删除平台">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `).join('');
    }

    // 添加平台
    addPlatform() {
        const newPlatformName = document.getElementById('newPlatformName').value.trim();

        if (!newPlatformName) {
            return;
        }

        if (accountDataManager.addPlatform(newPlatformName)) {
            // 清空输入框
            document.getElementById('newPlatformName').value = '';

            // 刷新显示
            this.loadExistingPlatforms();
            this.updatePlatformFilters();
            this.updatePlatformDatalist();
        }
    }

    // 删除平台
    deletePlatform(platformName) {
        showConfirm(`确定要删除平台"${platformName}"吗？`, () => {
            if (accountDataManager.deletePlatform(platformName)) {
                // 刷新显示
                this.loadExistingPlatforms();
                this.updatePlatformFilters();
                this.updatePlatformDatalist();
            }
        });
    }

    // 重置账号行
    resetAccountRows() {
        const container = document.getElementById('accountRows');
        container.innerHTML = `
            <div class="account-row mb-3" data-row="0">
                <div class="row align-items-end mb-2">
                    <div class="col-3">
                        <label class="form-label">用户名/邮箱</label>
                        <input type="text" class="form-control form-control-sm username-input" required autocomplete="new-username" name="new-username-0">
                    </div>
                    <div class="col-3">
                        <label class="form-label">密码</label>
                        <div class="input-group input-group-sm">
                            <input type="password" class="form-control password-input" required autocomplete="new-password" name="new-password-0">
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordInRow(this)">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-2">
                        <label class="form-label">状态</label>
                        <select class="form-select form-select-sm status-input">
                            <option value="active">活跃</option>
                            <option value="pending">待处理</option>
                            <option value="rented">已租</option>
                            <option value="sold">已售</option>
                            <option value="expired">过期</option>
                        </select>
                    </div>
                    <div class="col-2">
                        <label class="form-label">价值（元）</label>
                        <div class="value-range-inputs">
                            <input type="number" class="form-control form-control-sm value-min-input" min="0" step="0.01" placeholder="最低价">
                            <span class="value-separator">-</span>
                            <input type="number" class="form-control form-control-sm value-max-input" min="0" step="0.01" placeholder="最高价">
                        </div>
                    </div>
                    <div class="col-2">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="button" class="btn btn-outline-danger" onclick="accountManager.removeAccountRow(this)" style="display: none;">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <label class="form-label">备注</label>
                        <input type="text" class="form-control form-control-sm notes-input" placeholder="可选的备注信息">
                    </div>
                </div>
            </div>
        `;
        this.updateAddButtonText();
    }

    // 添加账号行
    addAccountRow() {
        const container = document.getElementById('accountRows');
        const rowIndex = container.children.length;

        const rowHtml = `
            <div class="account-row mb-3" data-row="${rowIndex}">
                <div class="row align-items-end mb-2">
                    <div class="col-3">
                        <label class="form-label">用户名/邮箱</label>
                        <input type="text" class="form-control form-control-sm username-input" required autocomplete="new-username" name="new-username-${rowIndex}">
                    </div>
                    <div class="col-3">
                        <label class="form-label">密码</label>
                        <div class="input-group input-group-sm">
                            <input type="password" class="form-control password-input" required autocomplete="new-password" name="new-password-${rowIndex}">
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordInRow(this)">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-2">
                        <label class="form-label">状态</label>
                        <select class="form-select form-select-sm status-input">
                            <option value="active">活跃</option>
                            <option value="pending">待处理</option>
                            <option value="rented">已租</option>
                            <option value="sold">已售</option>
                            <option value="expired">过期</option>
                        </select>
                    </div>
                    <div class="col-2">
                        <label class="form-label">价值（元）</label>
                        <div class="value-range-inputs">
                            <input type="number" class="form-control form-control-sm value-min-input" min="0" step="0.01" placeholder="最低价">
                            <span class="value-separator">-</span>
                            <input type="number" class="form-control form-control-sm value-max-input" min="0" step="0.01" placeholder="最高价">
                        </div>
                    </div>
                    <div class="col-2">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="button" class="btn btn-outline-danger" onclick="accountManager.removeAccountRow(this)">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <label class="form-label">备注</label>
                        <input type="text" class="form-control form-control-sm notes-input" placeholder="可选的备注信息">
                    </div>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', rowHtml);
        this.updateRemoveButtons();
        this.updateAddButtonText();
    }

    // 移除账号行
    removeAccountRow(button) {
        const container = document.getElementById('accountRows');
        if (container.children.length > 1) {
            button.closest('.account-row').remove();
            this.updateRemoveButtons();
            this.updateAddButtonText();
        }
    }

    // 更新移除按钮的显示状态
    updateRemoveButtons() {
        const container = document.getElementById('accountRows');
        const removeButtons = container.querySelectorAll('.btn-outline-danger');

        if (container.children.length === 1) {
            // 只有一行时隐藏删除按钮
            removeButtons.forEach(btn => btn.style.display = 'none');
        } else {
            // 多行时显示删除按钮
            removeButtons.forEach(btn => btn.style.display = 'inline-block');
        }
    }

    // 更新添加按钮文本
    updateAddButtonText() {
        const container = document.getElementById('accountRows');
        const buttonText = document.getElementById('addButtonText');
        const count = container.children.length;

        if (count === 1) {
            buttonText.textContent = '添加账号';
        } else {
            buttonText.textContent = `添加 ${count} 个账号`;
        }
    }






    // 清空表单自动填充
    clearFormAutoFill() {
        // 清空基本信息字段
        document.getElementById('accountType').value = '';
        document.getElementById('platform').value = '';

        // 清空所有账号行的用户名和密码
        const accountRows = document.querySelectorAll('#accountRows .account-row');
        accountRows.forEach(row => {
            const usernameInput = row.querySelector('.username-input');
            const passwordInput = row.querySelector('.password-input');
            if (usernameInput) usernameInput.value = '';
            if (passwordInput) passwordInput.value = '';
        });

        // 清空租赁信息
        const rentInputs = ['renter', 'rentPrice', 'rentStartDate', 'rentEndDate'];
        rentInputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) element.value = '';
        });
    }



    // 添加编辑账号行
    addEditAccountRow(existingAccount = null) {
        const accountRows = document.getElementById('editAccountRows');
        const rowIndex = accountRows.children.length;
        const isFirstRow = rowIndex === 0;

        const rowHtml = `
            <div class="account-row mb-3" data-row="${rowIndex}" ${existingAccount ? `data-account-id="${existingAccount.id}"` : ''}>
                <div class="row align-items-end mb-2">
                    <div class="col-3">
                        <label class="form-label">用户名/邮箱</label>
                        <input type="text" class="form-control form-control-sm username-input" required autocomplete="new-username" name="edit-new-username-${rowIndex}" value="${existingAccount ? existingAccount.username : ''}">
                    </div>
                    <div class="col-3">
                        <label class="form-label">密码</label>
                        <div class="input-group input-group-sm">
                            <input type="password" class="form-control password-input" required autocomplete="new-password" name="edit-new-password-${rowIndex}" value="${existingAccount ? existingAccount.password : ''}">
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordInRow(this)">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-2">
                        <label class="form-label">状态</label>
                        <select class="form-select form-select-sm status-input">
                            <option value="active" ${existingAccount && existingAccount.status === 'active' ? 'selected' : ''}>活跃</option>
                            <option value="pending" ${existingAccount && existingAccount.status === 'pending' ? 'selected' : ''}>待处理</option>
                            <option value="rented" ${existingAccount && existingAccount.status === 'rented' ? 'selected' : ''}>已租</option>
                            <option value="sold" ${existingAccount && existingAccount.status === 'sold' ? 'selected' : ''}>已售</option>
                            <option value="expired" ${existingAccount && existingAccount.status === 'expired' ? 'selected' : ''}>过期</option>
                        </select>
                    </div>
                    <div class="col-2">
                        <label class="form-label">价值（元）</label>
                        <div class="value-range-inputs">
                            <input type="number" class="form-control form-control-sm value-min-input" min="0" step="0.01" placeholder="最低价" value="${existingAccount && existingAccount.valueMin ? existingAccount.valueMin : ''}">
                            <span class="value-separator">-</span>
                            <input type="number" class="form-control form-control-sm value-max-input" min="0" step="0.01" placeholder="最高价" value="${existingAccount && existingAccount.valueMax ? existingAccount.valueMax : ''}">
                        </div>
                    </div>
                    <div class="col-2">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="accountManager.removeEditAccountRow(this)" ${isFirstRow ? 'style="display: none;"' : ''}>
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <label class="form-label">备注</label>
                        <input type="text" class="form-control form-control-sm notes-input" placeholder="可选的备注信息" value="${existingAccount && existingAccount.notes ? existingAccount.notes : ''}">
                    </div>
                </div>
            </div>
        `;

        accountRows.insertAdjacentHTML('beforeend', rowHtml);

        // 更新删除按钮显示状态
        this.updateEditRemoveButtons();
    }

    // 删除编辑账号行
    removeEditAccountRow(button) {
        const row = button.closest('.account-row');
        row.remove();
        this.updateEditRemoveButtons();
    }

    // 更新编辑删除按钮显示状态
    updateEditRemoveButtons() {
        const rows = document.querySelectorAll('#editAccountRows .account-row');
        rows.forEach((row, index) => {
            const removeBtn = row.querySelector('.btn-outline-danger');
            if (removeBtn) {
                removeBtn.style.display = rows.length > 1 ? 'inline-block' : 'none';
            }
        });
    }

    // 重置编辑表单
    resetEditForm() {
        // 清空平台信息
        document.getElementById('editAccountType').value = '';
        document.getElementById('editPlatform').value = '';

        // 重置账号行
        const accountRows = document.getElementById('editAccountRows');
        if (accountRows) {
            accountRows.innerHTML = '';
        }
    }
}

// 全局函数
function showAddAccountModal() {
    // 清除表单中的基本信息
    document.getElementById('accountType').value = '';
    document.getElementById('platform').value = '';

    // 隐藏租赁信息区域
    document.getElementById('addRentInfo').style.display = 'none';

    // 重置账号行
    accountManager.resetAccountRows();

    const modal = new bootstrap.Modal(document.getElementById('addAccountModal'));
    modal.show();

    // 延迟清空表单以防止浏览器自动填充
    setTimeout(() => {
        accountManager.clearFormAutoFill();
    }, 100);
}

function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;
    const icon = button.querySelector('i');

    if (field.type === 'password') {
        field.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        field.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

function togglePasswordInRow(button) {
    const field = button.previousElementSibling;
    const icon = button.querySelector('i');

    if (field.type === 'password') {
        field.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        field.type = 'password';
        icon.className = 'fas fa-eye';
    }
}



function showBatchOperations() {
    accountManager.showBatchOperations();
}



function showTypeManagerModal() {
    accountManager.showTypeManagerModal();
}

function showPlatformManagerModal() {
    accountManager.showPlatformManagerModal();
}



// 初始化应用
let accountManager;
document.addEventListener('DOMContentLoaded', () => {
    accountManager = new AccountManager();
});
