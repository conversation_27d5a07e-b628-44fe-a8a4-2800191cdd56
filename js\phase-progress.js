// 阶段进度管理系统
// 数据结构
let phases = [];
let phaseTasks = {}; // 存储每个阶段的任务
let isBatchMode = false; // 批量模式状态

// 存储键名 - 与现有系统兼容
const PHASES_STORAGE_KEY = 'st_phases';
const PHASE_TASKS_STORAGE_KEY = 'st_phase_tasks';

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟加载，确保主系统数据已完全加载
    setTimeout(() => {
        loadData();
        renderPhases();
        initializeEventListeners();
    }, 500);
});

// 初始化事件监听器
function initializeEventListeners() {
    // 添加阶段表单回车提交
    const addPhaseForm = document.getElementById('addPhaseForm');
    if (addPhaseForm) {
        addPhaseForm.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && e.target.tagName !== 'TEXTAREA') {
                e.preventDefault();
                createPhase();
            }
        });
    }

    // 编辑阶段表单回车提交
    const editPhaseForm = document.getElementById('editPhaseForm');
    if (editPhaseForm) {
        editPhaseForm.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && e.target.tagName !== 'TEXTAREA') {
                e.preventDefault();
                updatePhase();
            }
        });
    }

    // 模态框显示时自动聚焦到输入框
    const addPhaseModal = document.getElementById('addPhaseModal');
    if (addPhaseModal) {
        addPhaseModal.addEventListener('shown.bs.modal', function() {
            document.getElementById('phaseName').focus();
        });
    }

    const editPhaseModal = document.getElementById('editPhaseModal');
    if (editPhaseModal) {
        editPhaseModal.addEventListener('shown.bs.modal', function() {
            document.getElementById('editPhaseName').focus();
        });
    }

    // 批量模式切换事件
    const batchModeToggle = document.getElementById('phaseBatchModeToggle');
    if (batchModeToggle) {
        batchModeToggle.addEventListener('click', function() {
            isBatchMode = !isBatchMode;

            if (isBatchMode) {
                // 切换到批量模式
                this.innerHTML = '<i class="fas fa-times me-1"></i>关闭批量';
                this.className = 'btn btn-outline-secondary btn-sm';
                showBatchModeInstructions();
            } else {
                // 切换回普通模式
                this.innerHTML = '<i class="fas fa-list me-1"></i>批量模式';
                this.className = 'btn btn-outline-info btn-sm';
                hideBatchModeInstructions();
            }

            // 重新渲染阶段以更新按钮状态
            renderPhases();
        });
    }
}

// 显示批量模式说明
function showBatchModeInstructions() {
    // 在页面顶部显示批量模式说明
    const container = document.querySelector('.container');
    const existingInstructions = document.getElementById('batchModeInstructions');

    if (!existingInstructions) {
        const instructionsDiv = document.createElement('div');
        instructionsDiv.id = 'batchModeInstructions';
        instructionsDiv.className = 'alert alert-info alert-dismissible fade show mb-3';
        instructionsDiv.innerHTML = `
            <i class="fas fa-info-circle me-2"></i>
            <strong>批量模式已启用</strong> - 点击任何阶段的"添加任务"按钮可以批量添加多个任务
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // 插入到第一个row之前
        const firstRow = container.querySelector('.row');
        container.insertBefore(instructionsDiv, firstRow);
    }
}

// 隐藏批量模式说明
function hideBatchModeInstructions() {
    const instructions = document.getElementById('batchModeInstructions');
    if (instructions) {
        instructions.remove();
    }
}

// 数据加载和保存 - 与现有任务系统兼容
function loadData() {
    try {
        // 优先从独立存储加载阶段数据
        const savedPhases = localStorage.getItem(PHASES_STORAGE_KEY);
        const savedPhaseTasks = localStorage.getItem(PHASE_TASKS_STORAGE_KEY);

        if (savedPhases) {
            phases = JSON.parse(savedPhases);
        }

        if (savedPhaseTasks) {
            phaseTasks = JSON.parse(savedPhaseTasks);
        }

        // 不再从主系统加载数据，保持独立

        console.log('阶段数据加载完成:', { phases: phases.length, phaseTasks: Object.keys(phaseTasks).length });
    } catch (error) {
        console.error('加载阶段数据失败:', error);
        phases = [];
        phaseTasks = {};
    }
}

function saveData() {
    try {
        // 保存到独立存储
        localStorage.setItem(PHASES_STORAGE_KEY, JSON.stringify(phases));
        localStorage.setItem(PHASE_TASKS_STORAGE_KEY, JSON.stringify(phaseTasks));

        // 不再更新主系统数据，避免触发云同步

        console.log('阶段数据保存成功');
    } catch (error) {
        console.error('保存阶段数据失败:', error);
    }
}

// 生成UUID
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// 创建阶段
function createPhase() {
    const name = document.getElementById('phaseName').value.trim();
    const description = document.getElementById('phaseDescription').value.trim();

    // 验证输入
    if (!name) {
        showAlert('请输入阶段名称', 'warning');
        document.getElementById('phaseName').focus();
        return;
    }

    if (name.length > 50) {
        showAlert('阶段名称不能超过50个字符', 'warning');
        document.getElementById('phaseName').focus();
        return;
    }

    // 检查是否有重名阶段
    if (phases.some(phase => phase.name === name)) {
        showAlert('已存在同名阶段，请使用不同的名称', 'warning');
        document.getElementById('phaseName').focus();
        return;
    }

    try {
        const newPhase = {
            id: generateUUID(),
            name: name,
            description: description,
            status: 'not-started', // not-started, in-progress, completed
            createdAt: new Date().toISOString(),
            order: phases.length
        };

        phases.push(newPhase);
        phaseTasks[newPhase.id] = [];

        saveData();
        renderPhases();

        // 关闭模态框并重置表单
        const modal = bootstrap.Modal.getInstance(document.getElementById('addPhaseModal'));
        modal.hide();
        document.getElementById('addPhaseForm').reset();
    } catch (error) {
        console.error('创建阶段失败:', error);
        showAlert('创建阶段失败，请重试', 'danger');
    }
}

// 显示提示消息
function showAlert(message, type = 'info') {
    // 创建提示元素
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// 编辑阶段
function editPhase(phaseId) {
    const phase = phases.find(p => p.id === phaseId);
    if (!phase) return;
    
    document.getElementById('editPhaseId').value = phase.id;
    document.getElementById('editPhaseName').value = phase.name;
    document.getElementById('editPhaseDescription').value = phase.description || '';
    
    const modal = new bootstrap.Modal(document.getElementById('editPhaseModal'));
    modal.show();
}

function updatePhase() {
    const phaseId = document.getElementById('editPhaseId').value;
    const name = document.getElementById('editPhaseName').value.trim();
    const description = document.getElementById('editPhaseDescription').value.trim();

    // 验证输入
    if (!name) {
        showAlert('请输入阶段名称', 'warning');
        document.getElementById('editPhaseName').focus();
        return;
    }

    if (name.length > 50) {
        showAlert('阶段名称不能超过50个字符', 'warning');
        document.getElementById('editPhaseName').focus();
        return;
    }

    // 检查是否有重名阶段（排除当前阶段）
    if (phases.some(phase => phase.name === name && phase.id !== phaseId)) {
        showAlert('已存在同名阶段，请使用不同的名称', 'warning');
        document.getElementById('editPhaseName').focus();
        return;
    }

    const phaseIndex = phases.findIndex(p => p.id === phaseId);
    if (phaseIndex === -1) {
        showAlert('找不到要编辑的阶段', 'danger');
        return;
    }

    try {
        const oldName = phases[phaseIndex].name;
        phases[phaseIndex].name = name;
        phases[phaseIndex].description = description;

        saveData();
        renderPhases();

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('editPhaseModal'));
        modal.hide();

        showAlert(`阶段"${oldName}"已更新`, 'success');
    } catch (error) {
        console.error('更新阶段失败:', error);
        showAlert('更新阶段失败，请重试', 'danger');
    }
}

// 删除阶段
function deletePhase(phaseId) {
    const phase = phases.find(p => p.id === phaseId);
    if (!phase) return;
    
    if (confirm(`确定要删除阶段"${phase.name}"吗？这将同时删除该阶段下的所有任务。`)) {
        phases = phases.filter(p => p.id !== phaseId);
        delete phaseTasks[phaseId];
        
        saveData();
        renderPhases();
    }
}

// 更改阶段状态
function changePhaseStatus(phaseId, newStatus) {
    const phaseIndex = phases.findIndex(p => p.id === phaseId);
    if (phaseIndex === -1) return;
    
    phases[phaseIndex].status = newStatus;
    
    // 如果标记为完成，将该阶段所有任务标记为完成
    if (newStatus === 'completed') {
        const tasks = phaseTasks[phaseId] || [];
        tasks.forEach(task => {
            task.completed = true;
        });
    }
    
    saveData();
    renderPhases();
}

// 添加任务到阶段
function addTaskToPhase(phaseId, parentTaskId = null) {
    // 如果是批量模式，显示批量添加模态框
    if (isBatchMode) {
        showBatchAddTaskModal(phaseId, parentTaskId);
        return;
    }

    // 普通模式：创建一个新的空任务
    const newTask = {
        id: generateUUID(),
        name: '',
        completed: false,
        createdAt: new Date().toISOString(),
        parentId: parentTaskId,
        level: 0,
        isEditing: true // 标记为编辑状态
    };

    // 计算任务层级
    if (parentTaskId) {
        const parentTask = phaseTasks[phaseId]?.find(task => task.id === parentTaskId);
        if (parentTask) {
            newTask.level = (parentTask.level || 0) + 1;
            // 限制最多两层嵌套（主任务 + 子任务）
            if (newTask.level > 1) {
                showAlert('最多支持两层任务嵌套！', 'warning');
                return;
            }
        }
    }

    if (!phaseTasks[phaseId]) {
        phaseTasks[phaseId] = [];
    }

    phaseTasks[phaseId].push(newTask);

    // 重新渲染，不保存数据（等用户输入完成后再保存）
    renderPhases();

    // 聚焦到新创建的输入框
    setTimeout(() => {
        const input = document.querySelector(`input[data-task-id="${newTask.id}"]`);
        if (input) {
            input.focus();
        }
    }, 100);
}

// 保存任务名称
function saveTaskName(phaseId, taskId, name) {
    if (!name || !name.trim()) {
        // 如果名称为空，删除这个任务
        cancelTaskEdit(phaseId, taskId);
        return;
    }

    const tasks = phaseTasks[phaseId] || [];
    const task = tasks.find(t => t.id === taskId);
    if (task) {
        task.name = name.trim();
        task.isEditing = false;

        // 更新阶段状态
        updatePhaseStatus(phaseId);

        saveData();
        renderPhases();
    }
}

// 取消任务编辑
function cancelTaskEdit(phaseId, taskId) {
    if (!phaseTasks[phaseId]) return;

    const task = phaseTasks[phaseId].find(t => t.id === taskId);
    if (task && task.isEditing && !task.name) {
        // 如果是新建的空任务，直接删除
        phaseTasks[phaseId] = phaseTasks[phaseId].filter(t => t.id !== taskId);
    } else if (task) {
        // 如果是编辑现有任务，恢复编辑状态
        task.isEditing = false;
    }

    renderPhases();
}

// 添加子任务
function addSubTask(phaseId, parentTaskId) {
    addTaskToPhase(phaseId, parentTaskId);
}

// 显示批量添加任务模态框
function showBatchAddTaskModal(phaseId, parentTaskId = null) {
    document.getElementById('batchPhaseId').value = phaseId;
    // 添加隐藏字段来存储父任务ID
    let parentIdInput = document.getElementById('batchParentTaskId');
    if (!parentIdInput) {
        parentIdInput = document.createElement('input');
        parentIdInput.type = 'hidden';
        parentIdInput.id = 'batchParentTaskId';
        document.getElementById('batchAddTaskForm').appendChild(parentIdInput);
    }
    parentIdInput.value = parentTaskId || '';

    // 根据是否有父任务ID来更新模态框标题和提示
    const modalTitle = document.querySelector('#batchAddTaskModal .modal-title');
    const textarea = document.getElementById('batchTaskNames');
    const helpText = document.querySelector('#batchAddTaskModal .text-muted');

    if (parentTaskId) {
        modalTitle.textContent = '批量添加子任务';
        textarea.placeholder = '输入多个子任务名称，每行一个任务，空行会被忽略\n例如：\n准备材料\n开始制作\n完成检查';
        helpText.textContent = '每行一个子任务，空行会被忽略';
    } else {
        modalTitle.textContent = '批量添加任务';
        textarea.placeholder = '输入多个任务名称，每行一个任务，空行会被忽略\n例如：\n吃饭\n睡觉\n洗漱';
        helpText.textContent = '每行一个任务，空行会被忽略';
    }

    document.getElementById('batchTaskNames').value = '';
    const modal = new bootstrap.Modal(document.getElementById('batchAddTaskModal'));
    modal.show();
}

// 确认批量添加任务
function confirmBatchAddTasks() {
    const phaseId = document.getElementById('batchPhaseId').value;
    const parentTaskId = document.getElementById('batchParentTaskId')?.value || null;
    const taskNamesText = document.getElementById('batchTaskNames').value.trim();

    if (!taskNamesText) {
        alert('请输入任务名称');
        return;
    }

    // 如果是添加子任务，检查层级限制
    let taskLevel = 0;
    if (parentTaskId) {
        const parentTask = phaseTasks[phaseId]?.find(task => task.id === parentTaskId);
        if (parentTask) {
            taskLevel = (parentTask.level || 0) + 1;
            if (taskLevel > 1) {
                showAlert('最多支持两层任务嵌套！', 'warning');
                return;
            }
        }
    }

    const lines = taskNamesText.split('\n');
    let addedCount = 0;

    lines.forEach(line => {
        const taskName = line.trim();
        if (taskName) { // 忽略空行
            const newTask = {
                id: generateUUID(),
                name: taskName,
                completed: false,
                createdAt: new Date().toISOString(),
                parentId: parentTaskId,
                level: taskLevel
            };

            if (!phaseTasks[phaseId]) {
                phaseTasks[phaseId] = [];
            }

            phaseTasks[phaseId].push(newTask);
            addedCount++;
        }
    });

    if (addedCount > 0) {
        // 更新阶段状态
        updatePhaseStatus(phaseId);

        // 保存数据
        saveData();

        // 重新渲染
        renderPhases();

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('batchAddTaskModal'));
        modal.hide();

        // 显示成功消息
        const taskType = parentTaskId ? '子任务' : '任务';
        showAlert(`成功添加了 ${addedCount} 个${taskType}`, 'success');
    }
}

// 编辑任务名称
function editTaskName(phaseId, taskId) {
    const tasks = phaseTasks[phaseId] || [];
    const task = tasks.find(t => t.id === taskId);
    if (task) {
        task.isEditing = true;
        renderPhases();

        // 聚焦到输入框并选中文本
        setTimeout(() => {
            const input = document.querySelector(`input[data-task-id="${taskId}"]`);
            if (input) {
                input.focus();
                input.select();
            }
        }, 100);
    }
}

// 切换任务完成状态
function toggleTaskCompletion(phaseId, taskId) {
    const tasks = phaseTasks[phaseId] || [];
    const taskIndex = tasks.findIndex(t => t.id === taskId);

    if (taskIndex === -1) return;

    const task = tasks[taskIndex];
    const newCompletedStatus = !task.completed;
    task.completed = newCompletedStatus;

    // 如果标记为完成，则所有子任务也标记为完成
    if (newCompletedStatus) {
        markChildTasksAsCompleted(phaseId, taskId, true);
    }

    // 检查并更新父任务状态
    updateParentTaskStatus(phaseId, task.parentId);

    // 不再同步到原任务系统

    // 更新阶段状态
    updatePhaseStatus(phaseId);

    saveData();
    renderPhases();
}

// 标记子任务为完成状态
function markChildTasksAsCompleted(phaseId, parentTaskId, completed) {
    const tasks = phaseTasks[phaseId] || [];
    const childTasks = tasks.filter(t => t.parentId === parentTaskId);

    childTasks.forEach(child => {
        child.completed = completed;
        // 递归处理子任务的子任务（虽然目前只支持两层）
        markChildTasksAsCompleted(phaseId, child.id, completed);
    });
}

// 更新父任务状态
function updateParentTaskStatus(phaseId, parentTaskId) {
    if (!parentTaskId) return;

    const tasks = phaseTasks[phaseId] || [];
    const parentTask = tasks.find(t => t.id === parentTaskId);
    if (!parentTask) return;

    const childTasks = tasks.filter(t => t.parentId === parentTaskId);
    if (childTasks.length === 0) return;

    // 如果所有子任务都完成了，父任务也标记为完成
    const allChildrenCompleted = childTasks.every(child => child.completed);
    if (allChildrenCompleted && !parentTask.completed) {
        parentTask.completed = true;

        // 不再同步到原任务系统

        // 递归检查父任务的父任务
        updateParentTaskStatus(phaseId, parentTask.parentId);
    }
}

// 同步任务状态到原任务系统功能已删除

// 删除任务（包括子任务）
function deleteTask(phaseId, taskId) {
    if (!phaseTasks[phaseId]) return;

    const task = phaseTasks[phaseId].find(t => t.id === taskId);
    if (!task) return;

    // 直接删除，不需要确认弹窗
    // 递归删除任务及其子任务
    function deleteTaskAndChildren(taskIdToDelete) {
        // 先删除所有子任务
        const childTasks = phaseTasks[phaseId].filter(t => t.parentId === taskIdToDelete);
        childTasks.forEach(child => {
            deleteTaskAndChildren(child.id);
        });

        // 删除任务本身
        phaseTasks[phaseId] = phaseTasks[phaseId].filter(t => t.id !== taskIdToDelete);
    }

    deleteTaskAndChildren(taskId);

    // 更新阶段状态
    updatePhaseStatus(phaseId);

    saveData();
    renderPhases();
}

// 更新阶段状态（根据任务完成情况）
function updatePhaseStatus(phaseId) {
    const tasks = phaseTasks[phaseId] || [];
    const phaseIndex = phases.findIndex(p => p.id === phaseId);
    
    if (phaseIndex === -1) return;
    
    if (tasks.length === 0) {
        phases[phaseIndex].status = 'not-started';
    } else {
        const completedTasks = tasks.filter(t => t.completed);
        if (completedTasks.length === tasks.length) {
            phases[phaseIndex].status = 'completed';
        } else if (completedTasks.length > 0) {
            phases[phaseIndex].status = 'in-progress';
        } else {
            phases[phaseIndex].status = 'not-started';
        }
    }
}

// 计算阶段进度（只计算主任务，子任务不单独计算）
function calculatePhaseProgress(phaseId) {
    const allTasks = phaseTasks[phaseId] || [];

    // 只计算主任务（没有父任务的任务）
    const mainTasks = allTasks.filter(task => !task.parentId);

    if (mainTasks.length === 0) return { completed: 0, total: 0, percentage: 0 };

    // 计算完成的主任务数量
    const completedMainTasks = mainTasks.filter(task => task.completed);
    const percentage = Math.round((completedMainTasks.length / mainTasks.length) * 100);

    return {
        completed: completedMainTasks.length,
        total: mainTasks.length,
        percentage: percentage
    };
}

// 获取状态显示文本
function getStatusText(status) {
    const statusMap = {
        'not-started': '未开始',
        'in-progress': '进行中',
        'completed': '已完成'
    };
    return statusMap[status] || '未知';
}

// 从现有任务系统导入任务功能已删除

// 任务导入模态框功能已删除

// 任务选择辅助函数已删除

// 确认导入任务功能已删除

// 更新统计信息
function updateStatistics() {
    const totalPhases = phases.length;
    const inProgressPhases = phases.filter(p => p.status === 'in-progress').length;
    const completedPhases = phases.filter(p => p.status === 'completed').length;

    // 更新统计显示
    const statisticsRow = document.getElementById('statisticsRow');
    if (totalPhases > 0) {
        statisticsRow.style.display = 'block';
        document.getElementById('totalPhases').textContent = totalPhases;
        document.getElementById('inProgressPhases').textContent = inProgressPhases;
        document.getElementById('completedPhases').textContent = completedPhases;
    } else {
        statisticsRow.style.display = 'none';
    }
}

// 渲染阶段列表
function renderPhases() {
    const container = document.getElementById('phaseContainer');
    const emptyState = document.getElementById('emptyState');

    // 更新统计信息
    updateStatistics();

    if (phases.length === 0) {
        container.innerHTML = '';
        emptyState.style.display = 'block';
        return;
    }

    emptyState.style.display = 'none';

    // 按order排序
    const sortedPhases = [...phases].sort((a, b) => a.order - b.order);

    container.innerHTML = sortedPhases.map(phase => {
        const progress = calculatePhaseProgress(phase.id);
        const tasks = phaseTasks[phase.id] || [];
        const totalTasks = tasks.length;
        const completedTasks = tasks.filter(task => task.completed).length;
        const progressPercentage = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

        // 计算父任务数量
        const mainTasks = tasks.filter(task => !task.parentId);
        const mainTaskCount = mainTasks.length;

        return `
            <div class="phase-card">
                <div class="phase-header" onclick="toggleTaskList('${phase.id}')" style="cursor: pointer;">
                    <div class="d-flex align-items-center flex-grow-1">
                        <i class="fas fa-chevron-down task-list-toggle me-2" id="toggle-${phase.id}"></i>
                        <h6 class="phase-title mb-0 me-2">${phase.name}</h6>
                        <span class="badge bg-secondary me-3" style="font-size: 0.75rem;">${mainTaskCount}</span>
                        ${phase.description ? `<span class="text-muted" style="font-size: 0.9rem;">${phase.description}</span>` : ''}
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <span class="phase-status ${phase.status}">${getStatusText(phase.status)}</span>
                        <div class="phase-actions">
                            <button class="btn btn-sm btn-outline-primary btn-icon" onclick="event.stopPropagation(); editPhase('${phase.id}')" title="编辑阶段">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger btn-icon" onclick="event.stopPropagation(); deletePhase('${phase.id}')" title="删除阶段">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="phase-body">
                    <!-- 进度条 -->
                    <div class="phase-progress mb-3">
                        <div class="progress">
                            <div class="progress-bar" style="width: ${progressPercentage}%"></div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end align-items-center mb-2">
                        <button class="btn btn-sm btn-outline-success" onclick="addTaskToPhase('${phase.id}')">
                            <i class="fas fa-plus me-1"></i>添加任务
                        </button>
                    </div>

                    <div class="task-list" id="task-list-${phase.id}">
                        ${tasks.length === 0 ?
                            '<p class="text-muted text-center py-3 mb-0" style="font-size: 0.85rem;">暂无任务</p>' :
                            renderTasksHierarchy(tasks, phase.id)
                        }
                    </div>
                </div>
            </div>
        `;
    }).join('');

    // 恢复任务列表的收起/展开状态
    setTimeout(() => {
        restoreTaskListStates();
    }, 100);
}

// 渲染任务层级结构
function renderTasksHierarchy(tasks, phaseId) {
    // 分离主任务和子任务
    const mainTasks = tasks.filter(task => !task.parentId);

    // 递归渲染任务及其子任务
    function renderTaskWithChildren(task, level = 0) {
        const childTasks = tasks.filter(t => t.parentId === task.id);
        const hasChildren = childTasks.length > 0;
        const childCount = childTasks.length;
        const indentStyle = level > 0 ? `margin-left: ${level * 20}px;` : '';

        let html = `
            <div class="task-item" style="${indentStyle}">
                <input type="checkbox" class="task-checkbox" ${task.completed ? 'checked' : ''}
                       onchange="toggleTaskCompletion('${phaseId}', '${task.id}')" ${task.isEditing ? 'disabled' : ''}>
                <div class="flex-grow-1">
                    ${task.isEditing ? `
                        <input type="text" class="form-control form-control-sm"
                               data-task-id="${task.id}"
                               value="${task.name}"
                               placeholder="输入任务名称..."
                               onblur="saveTaskName('${phaseId}', '${task.id}', this.value)"
                               onkeypress="if(event.key==='Enter') this.blur(); if(event.key==='Escape') cancelTaskEdit('${phaseId}', '${task.id}')">
                    ` : `
                        <div class="d-flex align-items-center">
                            <p class="task-name ${task.completed ? 'task-completed' : ''} mb-0 me-2"
                               ondblclick="editTaskName('${phaseId}', '${task.id}')">
                                ${level > 0 ? '<i class="fas fa-level-up-alt fa-rotate-90 me-1 text-muted"></i>' : ''}
                                ${task.name}
                            </p>
                            ${level === 0 && childCount > 0 ? `<span class="badge bg-info" style="font-size: 0.7rem;">${childCount}</span>` : ''}
                        </div>

                    `}
                </div>
                <div class="task-actions d-flex gap-1">
                    ${!task.isEditing && level === 0 ? `
                        <button class="btn btn-sm btn-outline-success btn-icon" onclick="addSubTask('${phaseId}', '${task.id}')" title="添加子任务">
                            <i class="fas fa-plus"></i>
                        </button>
                    ` : ''}
                    ${!task.isEditing ? `
                        <button class="btn btn-sm btn-outline-danger btn-icon" onclick="deleteTask('${phaseId}', '${task.id}')" title="删除任务">
                            <i class="fas fa-times"></i>
                        </button>
                    ` : `
                        <button class="btn btn-sm btn-outline-secondary btn-icon" onclick="cancelTaskEdit('${phaseId}', '${task.id}')" title="取消">
                            <i class="fas fa-times"></i>
                        </button>
                    `}
                </div>
            </div>
        `;

        // 渲染子任务
        if (hasChildren) {
            childTasks.forEach(child => {
                html += renderTaskWithChildren(child, level + 1);
            });
        }

        return html;
    }

    // 渲染所有主任务及其子任务
    return mainTasks.map(task => renderTaskWithChildren(task)).join('');
}

// 切换任务列表显示状态
function toggleTaskList(phaseId) {
    const taskList = document.getElementById(`task-list-${phaseId}`);
    const toggleIcon = document.getElementById(`toggle-${phaseId}`);

    if (!taskList || !toggleIcon) return;

    const isCollapsed = taskList.style.display === 'none';

    if (isCollapsed) {
        taskList.style.display = 'block';
        toggleIcon.className = 'fas fa-chevron-down task-list-toggle';
    } else {
        taskList.style.display = 'none';
        toggleIcon.className = 'fas fa-chevron-right task-list-toggle';
    }

    // 保存状态到localStorage
    const collapsedStates = JSON.parse(localStorage.getItem('taskListCollapsedStates') || '{}');
    collapsedStates[phaseId] = !isCollapsed; // true表示收起，false表示展开
    localStorage.setItem('taskListCollapsedStates', JSON.stringify(collapsedStates));
}

// 恢复任务列表显示状态
function restoreTaskListStates() {
    const collapsedStates = JSON.parse(localStorage.getItem('taskListCollapsedStates') || '{}');

    Object.keys(collapsedStates).forEach(phaseId => {
        const taskList = document.getElementById(`task-list-${phaseId}`);
        const toggleIcon = document.getElementById(`toggle-${phaseId}`);

        if (taskList && toggleIcon && collapsedStates[phaseId]) {
            taskList.style.display = 'none';
            toggleIcon.className = 'fas fa-chevron-right task-list-toggle';
        }
    });
}
