<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标测试</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/shopping-list.css">
</head>
<body>
    <div class="container mt-5">
        <h2>图标测试页面</h2>
        
        <div class="row mt-4">
            <div class="col-12">
                <h4>Font Awesome 图标测试</h4>
                <div class="d-flex flex-wrap gap-3 mt-3">
                    <div class="p-3 border rounded">
                        <i class="fas fa-shopping-cart"></i> 购物车
                    </div>
                    <div class="p-3 border rounded">
                        <i class="fas fa-plus"></i> 加号
                    </div>
                    <div class="p-3 border rounded">
                        <i class="fas fa-edit"></i> 编辑
                    </div>
                    <div class="p-3 border rounded">
                        <i class="fas fa-trash"></i> 删除
                    </div>
                    <div class="p-3 border rounded">
                        <i class="fas fa-check"></i> 完成
                    </div>
                    <div class="p-3 border rounded">
                        <i class="fas fa-arrow-left"></i> 返回
                    </div>
                    <div class="p-3 border rounded">
                        <i class="fas fa-tags"></i> 标签
                    </div>
                    <div class="p-3 border rounded">
                        <i class="fas fa-list"></i> 列表
                    </div>
                    <div class="p-3 border rounded">
                        <i class="fas fa-chart-bar"></i> 图表
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h4>按钮中的图标测试</h4>
                <div class="d-flex flex-wrap gap-2 mt-3">
                    <button class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>添加项目
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-check me-2"></i>完成
                    </button>
                    <button class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>编辑
                    </button>
                    <button class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>删除
                    </button>
                    <button class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>返回
                    </button>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h4>卡片中的图标测试</h4>
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-shopping-cart me-2"></i>购物清单测试
                    </div>
                    <div class="card-body">
                        <p><i class="fas fa-info-circle me-2"></i>这是一个测试卡片</p>
                        <p><i class="fas fa-star me-2"></i>如果您能看到这些图标，说明修复成功</p>
                        <p><i class="fas fa-exclamation-triangle me-2"></i>如果看到的是长方形，说明还有问题</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <a href="shopping-list.html" class="btn btn-primary">
                    <i class="fas fa-arrow-right me-2"></i>返回购物清单
                </a>
            </div>
        </div>
    </div>
</body>
</html>
