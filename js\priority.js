// 优先级管理系统
class PriorityManager {
    constructor() {
        this.lists = [];
        this.currentListId = null;
        this.storageKey = 'savingsData'; // 使用主系统的存储键
        this.draggedItem = null;
        this.init();
    }

    // 初始化
    init() {
        this.loadData();
        this.bindEvents();
        this.render();
    }

    // 获取当前列表
    getCurrentList() {
        if (!this.currentListId) return null;
        return this.lists.find(list => list.id === this.currentListId);
    }

    // 获取当前列表的项目
    getCurrentItems() {
        const currentList = this.getCurrentList();
        return currentList ? currentList.items : [];
    }

    // 设置当前列表的项目
    setCurrentItems(items) {
        const currentList = this.getCurrentList();
        if (currentList) {
            currentList.items = items;
        }
    }

    // 绑定事件
    bindEvents() {
        // 添加表单提交
        document.getElementById('addPriorityForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addItem();
        });

        // 回车键快速添加
        document.getElementById('priorityItemText').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.addItem();
            }
        });

        // 输入时实时预览批量导入
        document.getElementById('priorityItemText').addEventListener('input', (e) => {
            this.previewBatchImport(e.target.value);
        });
    }

    // 添加新列表
    addNewList() {
        const listName = prompt('请输入新列表名称：');
        if (!listName || !listName.trim()) return;

        const newList = {
            id: this.generateId(),
            name: listName.trim(),
            items: [],
            collapsed: false, // 新列表默认展开
            createdAt: new Date().toISOString()
        };

        this.lists.push(newList);
        this.currentListId = newList.id;
        this.saveData();
        this.render();
    }

    // 删除列表
    deleteList(listId) {
        if (this.lists.length <= 1) {
            alert('至少需要保留一个列表');
            return;
        }

        const list = this.lists.find(l => l.id === listId);
        if (!list) return;

        if (confirm(`确定要删除列表"${list.name}"吗？此操作不可恢复！`)) {
            this.lists = this.lists.filter(l => l.id !== listId);

            // 如果删除的是当前列表，切换到第一个列表
            if (this.currentListId === listId) {
                this.currentListId = this.lists.length > 0 ? this.lists[0].id : null;
            }

            this.saveData();
            this.render();
        }
    }

    // 重命名列表
    renameList(listId) {
        const list = this.lists.find(l => l.id === listId);
        if (!list) return;

        const newName = prompt('请输入新的列表名称：', list.name);
        if (!newName || !newName.trim() || newName.trim() === list.name) return;

        list.name = newName.trim();
        this.saveData();
        this.render();
    }

    // 切换列表
    switchToList(listId) {
        this.currentListId = listId;
        this.saveData();
        this.render();
    }

    // 切换列表折叠状态
    toggleListCollapse(listId) {
        const list = this.lists.find(l => l.id === listId);
        if (list) {
            list.collapsed = !list.collapsed;
            this.saveData();
            this.render();
        }
    }

    // 添加新项目（支持批量导入）
    addItem(text = null) {
        if (!this.currentListId) {
            alert('请先创建一个列表');
            return;
        }

        const input = document.getElementById('priorityItemText');
        const itemText = text || input.value.trim();

        if (!itemText) return;

        // 通过空格分割文本，支持批量添加
        const items = itemText.split(/\s+/).filter(item => item.trim().length > 0);

        if (items.length === 0) return;

        const currentItems = this.getCurrentItems();
        const addedItems = [];

        items.forEach(itemContent => {
            const newItem = {
                id: this.generateId(),
                text: itemContent.trim(),
                completed: false,
                createdAt: new Date().toISOString(),
                priority: currentItems.length + 1
            };

            currentItems.push(newItem);
            addedItems.push(newItem);
        });

        this.updatePriorities();
        this.saveData();
        this.render();

        if (!text) {
            input.value = '';
            input.focus();
            // 清除批量导入预览
            this.clearBatchPreview();
        }
    }

    // 快速添加预设项目
    addQuickItem(text) {
        this.addItem(text);
    }

    // 预览批量导入
    previewBatchImport(inputText) {
        const previewElement = document.getElementById('batchPreview');
        if (!previewElement) return;

        const text = inputText.trim();
        if (!text) {
            previewElement.style.display = 'none';
            return;
        }

        // 分割文本
        const items = text.split(/\s+/).filter(item => item.trim().length > 0);

        if (items.length <= 1) {
            previewElement.style.display = 'none';
            return;
        }

        // 显示预览
        previewElement.style.display = 'block';
        previewElement.innerHTML = `
            <div class="alert alert-info mb-0">
                <i class="fas fa-eye me-2"></i>
                <strong>批量导入预览（${items.length} 个事项）：</strong>
                <div class="mt-2">
                    ${items.map((item, index) => `
                        <span class="badge bg-secondary me-1 mb-1">${index + 1}. ${this.escapeHtml(item)}</span>
                    `).join('')}
                </div>
            </div>
        `;
    }

    // 清除批量导入预览
    clearBatchPreview() {
        const previewElement = document.getElementById('batchPreview');
        if (previewElement) {
            previewElement.style.display = 'none';
        }
    }

    // 删除项目
    deleteItem(id) {
        if (confirm('确定要删除这个事项吗？')) {
            const currentItems = this.getCurrentItems();
            const filteredItems = currentItems.filter(item => item.id !== id);
            this.setCurrentItems(filteredItems);
            this.updatePriorities();
            this.saveData();
            this.render();
        }
    }

    // 切换完成状态
    toggleComplete(id) {
        const currentItems = this.getCurrentItems();
        const item = currentItems.find(item => item.id === id);
        if (item) {
            item.completed = !item.completed;
            this.saveData();
            this.render();
        }
    }

    // 开始编辑事项
    startEdit(id) {
        const currentItems = this.getCurrentItems();
        const item = currentItems.find(item => item.id === id);
        if (!item) return;

        const itemElement = document.querySelector(`[data-id="${id}"]`);
        const textElement = itemElement.querySelector('.priority-text');

        // 创建输入框
        const input = document.createElement('input');
        input.type = 'text';
        input.className = 'form-control priority-edit-input';
        input.value = item.text;
        input.style.cssText = 'margin-right: 1rem; font-size: 1rem;';

        // 替换文本元素
        textElement.replaceWith(input);
        input.focus();
        input.select();

        // 保存原始文本，用于取消编辑
        const originalText = item.text;

        // 完成编辑
        const finishEdit = () => {
            const newText = input.value.trim();
            if (newText && newText !== originalText) {
                item.text = newText;
                this.saveData();
            }
            this.render();
        };

        // 取消编辑
        const cancelEdit = () => {
            this.render();
        };

        // 绑定事件
        input.addEventListener('blur', finishEdit);
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                finishEdit();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });
    }

    // 更新优先级序号
    updatePriorities() {
        const currentItems = this.getCurrentItems();
        currentItems.forEach((item, index) => {
            item.priority = index + 1;
        });
    }

    // 拖拽开始
    handleDragStart(e, id) {
        const currentItems = this.getCurrentItems();
        this.draggedItem = currentItems.find(item => item.id === id);
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/html', e.target.outerHTML);
        e.target.classList.add('dragging');
    }

    // 拖拽结束
    handleDragEnd(e) {
        e.target.classList.remove('dragging');
        this.clearDragIndicators();
        this.draggedItem = null;
    }

    // 拖拽经过
    handleDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
        
        const targetItem = e.target.closest('.priority-item');
        if (targetItem && this.draggedItem) {
            this.clearDragIndicators();
            
            const rect = targetItem.getBoundingClientRect();
            const midY = rect.top + rect.height / 2;
            
            if (e.clientY < midY) {
                targetItem.classList.add('drag-over-top');
            } else {
                targetItem.classList.add('drag-over-bottom');
            }
        }
    }

    // 拖拽放置
    handleDrop(e, targetId) {
        e.preventDefault();

        if (!this.draggedItem || this.draggedItem.id === targetId) {
            this.clearDragIndicators();
            return;
        }

        const currentItems = this.getCurrentItems();
        const targetIndex = currentItems.findIndex(item => item.id === targetId);
        const draggedIndex = currentItems.findIndex(item => item.id === this.draggedItem.id);

        if (targetIndex === -1 || draggedIndex === -1) return;

        // 移除拖拽的项目
        const [draggedItem] = currentItems.splice(draggedIndex, 1);

        // 确定插入位置
        const rect = e.target.closest('.priority-item').getBoundingClientRect();
        const midY = rect.top + rect.height / 2;
        let insertIndex = targetIndex;

        if (e.clientY > midY) {
            insertIndex = targetIndex + 1;
        }

        // 如果原位置在目标位置之前，需要调整插入位置
        if (draggedIndex < targetIndex) {
            insertIndex--;
        }

        // 插入到新位置
        currentItems.splice(insertIndex, 0, draggedItem);

        this.updatePriorities();
        this.saveData();
        this.render();
        this.clearDragIndicators();
    }

    // 清除拖拽指示器
    clearDragIndicators() {
        document.querySelectorAll('.drag-over-top, .drag-over-bottom').forEach(el => {
            el.classList.remove('drag-over-top', 'drag-over-bottom');
        });
    }

    // 获取优先级颜色类
    getPriorityClass(index) {
        const currentItems = this.getCurrentItems();
        const total = currentItems.length;
        if (total <= 1) return 'priority-default';

        const ratio = index / (total - 1);

        if (ratio <= 0.2) return 'priority-1';      // 红色 - 最高优先级
        if (ratio <= 0.4) return 'priority-2';      // 橙色
        if (ratio <= 0.6) return 'priority-3';      // 黄色
        if (ratio <= 0.8) return 'priority-4';      // 青色
        return 'priority-5';                         // 绿色 - 最低优先级
    }

    // 渲染列表
    render() {
        this.renderListTabs();
        this.renderPriorityLists();
    }

    // 渲染列表标签
    renderListTabs() {
        const container = document.getElementById('listTabs');
        if (!container) return;

        if (this.lists.length === 0) {
            container.innerHTML = '<p class="text-muted mb-0">暂无列表，请创建第一个列表</p>';
            return;
        }

        container.innerHTML = this.lists.map(list => `
            <div class="list-tab ${list.id === this.currentListId ? 'active' : ''}" data-list-id="${list.id}">
                <div class="list-tab-header" onclick="window.priorityManager.switchToList('${list.id}')">
                    <span class="list-name">${this.escapeHtml(list.name)}</span>
                    <span class="list-count">${list.items.length}</span>
                </div>
                <div class="list-tab-actions">
                    <button class="btn btn-sm btn-outline-secondary" onclick="window.priorityManager.renameList('${list.id}')" title="重命名">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="window.priorityManager.deleteList('${list.id}')" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    // 渲染优先级列表
    renderPriorityLists() {
        const container = document.getElementById('priorityListsContainer');
        if (!container) return;

        if (this.lists.length === 0) {
            container.innerHTML = `
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无列表</h5>
                        <p class="text-muted">创建第一个列表开始管理您的优先级</p>
                        <button class="btn btn-primary" onclick="window.priorityManager.addNewList()">
                            <i class="fas fa-plus me-1"></i>创建列表
                        </button>
                    </div>
                </div>
            `;
            return;
        }

        // 只显示当前选中的列表
        const currentList = this.getCurrentList();
        if (currentList) {
            container.innerHTML = this.renderSingleList(currentList);
        } else {
            container.innerHTML = '';
        }
        this.updateStats();
    }

    // 渲染单个列表
    renderSingleList(list) {
        const isCollapsed = list.collapsed;
        const items = list.items || [];

        return `
            <div class="card mb-3 priority-list-card active-list" data-list-id="${list.id}">
                <div class="card-header d-flex justify-content-between align-items-center list-header"
                     onclick="window.priorityManager.toggleListCollapse('${list.id}')">
                    <h5 class="card-title mb-0 d-flex align-items-center">
                        <i class="fas fa-chevron-${isCollapsed ? 'right' : 'down'} me-2 collapse-icon"></i>
                        <i class="fas fa-list-ol me-2"></i>
                        ${this.escapeHtml(list.name)}
                    </h5>
                    <div class="priority-stats">
                        <span class="badge bg-primary">${items.length} 项</span>
                        <span class="badge bg-success">${items.filter(item => item.completed).length} 已完成</span>
                    </div>
                </div>
                <div class="card-body list-content" style="display: ${isCollapsed ? 'none' : 'block'}">
                    ${items.length === 0 ? this.renderEmptyState(list.id) : this.renderItemsList(items, list.id)}
                </div>
            </div>
        `;
    }

    // 渲染空状态
    renderEmptyState(listId) {
        return `
            <div class="empty-state text-center">
                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无待办事项</h5>
                <p class="text-muted">添加第一个事项开始管理优先级</p>
            </div>
        `;
    }

    // 渲染项目列表
    renderItemsList(items, listId) {
        return `
            <div class="priority-list">
                ${items.map((item, index) => this.renderItem(item, index)).join('')}
            </div>
        `;
    }

    // 渲染单个项目
    renderItem(item, index) {
        const priorityClass = this.getPriorityClass(index);
        const completedClass = item.completed ? 'completed' : '';

        return `
            <div class="priority-item ${priorityClass} ${completedClass}"
                 draggable="true"
                 data-id="${item.id}"
                 ondragstart="window.priorityManager.handleDragStart(event, '${item.id}')"
                 ondragend="window.priorityManager.handleDragEnd(event)"
                 ondragover="window.priorityManager.handleDragOver(event)"
                 ondrop="window.priorityManager.handleDrop(event, '${item.id}')">

                <div class="priority-number">${index + 1}</div>

                <input type="checkbox" class="priority-checkbox form-check-input"
                       ${item.completed ? 'checked' : ''}
                       onchange="window.priorityManager.toggleComplete('${item.id}')">

                <div class="priority-text" ondblclick="window.priorityManager.startEdit('${item.id}')" title="双击编辑">${this.escapeHtml(item.text)}</div>

                <div class="priority-actions">
                    <button class="btn btn-outline-danger btn-sm"
                            onclick="window.priorityManager.deleteItem('${item.id}')"
                            title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }

    // 更新统计信息
    updateStats() {
        // 统计信息现在在每个列表的头部显示，这里不需要更新全局统计
    }

    // 全部标记为完成
    markAllCompleted() {
        const currentItems = this.getCurrentItems();
        if (currentItems.length === 0) {
            return;
        }

        currentItems.forEach(item => item.completed = true);
        this.saveData();
        this.render();
    }

    // 清除已完成项目
    clearCompleted() {
        const currentItems = this.getCurrentItems();
        const completedCount = currentItems.filter(item => item.completed).length;

        if (completedCount === 0) {
            return;
        }

        if (confirm(`确定要删除 ${completedCount} 个已完成的事项吗？`)) {
            const filteredItems = currentItems.filter(item => !item.completed);
            this.setCurrentItems(filteredItems);
            this.updatePriorities();
            this.saveData();
            this.render();
        }
    }

    // 清空全部
    clearAll() {
        const currentItems = this.getCurrentItems();
        if (currentItems.length === 0) {
            return;
        }

        if (confirm('确定要清空当前列表的所有事项吗？此操作不可恢复！')) {
            this.setCurrentItems([]);
            this.saveData();
            this.render();
        }
    }



    // 生成唯一ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // HTML转义
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }



    // 保存数据到主系统
    saveData() {
        try {
            // 获取主系统数据
            const savedData = localStorage.getItem(this.storageKey);
            let appData = savedData ? JSON.parse(savedData) : {};

            // 将优先级数据集成到主系统
            appData.priorityLists = this.lists;
            appData.currentPriorityListId = this.currentListId;

            // 保存回主系统
            localStorage.setItem(this.storageKey, JSON.stringify(appData));

            // 触发数据更改事件，通知其他模块
            const dataChangeEvent = new CustomEvent('appDataChanged', {
                detail: { source: 'priority' }
            });
            window.dispatchEvent(dataChangeEvent);
        } catch (error) {
            console.error('保存数据失败:', error);
        }
    }

    // 从主系统加载数据
    loadData() {
        try {
            const savedData = localStorage.getItem(this.storageKey);
            if (savedData) {
                const appData = JSON.parse(savedData);

                // 加载列表数据
                this.lists = appData.priorityLists || [];
                this.currentListId = appData.currentPriorityListId || null;

                // 如果没有列表，创建默认列表
                if (this.lists.length === 0) {
                    const defaultList = {
                        id: this.generateId(),
                        name: '默认列表',
                        items: appData.priorityItems || [], // 兼容旧数据
                        collapsed: false, // 默认展开
                        createdAt: new Date().toISOString()
                    };
                    this.lists.push(defaultList);
                    this.currentListId = defaultList.id;
                }

                // 确保当前列表ID有效
                if (!this.currentListId || !this.lists.find(l => l.id === this.currentListId)) {
                    this.currentListId = this.lists[0].id;
                }

                // 确保数据完整性
                this.lists.forEach(list => {
                    if (!list.id) list.id = this.generateId();
                    if (!list.name) list.name = '未命名列表';
                    if (!Array.isArray(list.items)) list.items = [];
                    if (typeof list.collapsed !== 'boolean') list.collapsed = false;
                    if (!list.createdAt) list.createdAt = new Date().toISOString();

                    list.items.forEach((item, index) => {
                        if (!item.id) item.id = this.generateId();
                        if (typeof item.completed !== 'boolean') item.completed = false;
                        if (!item.createdAt) item.createdAt = new Date().toISOString();
                        item.priority = index + 1;
                    });
                });
            } else {
                // 创建默认列表
                const defaultList = {
                    id: this.generateId(),
                    name: '默认列表',
                    items: [],
                    collapsed: false, // 默认展开
                    createdAt: new Date().toISOString()
                };
                this.lists = [defaultList];
                this.currentListId = defaultList.id;
            }
        } catch (error) {
            console.error('加载数据失败:', error);
            // 创建默认列表
            const defaultList = {
                id: this.generateId(),
                name: '默认列表',
                items: [],
                collapsed: false, // 默认展开
                createdAt: new Date().toISOString()
            };
            this.lists = [defaultList];
            this.currentListId = defaultList.id;
        }
    }
}

// 全局函数，供HTML调用
let priorityManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    priorityManager = new PriorityManager();
    window.priorityManager = priorityManager;
});

// 导出全局函数
window.markAllCompleted = () => priorityManager.markAllCompleted();
window.clearCompleted = () => priorityManager.clearCompleted();
window.clearAll = () => priorityManager.clearAll();

// 确保所有方法都可以从全局访问
window.priorityManager = null;
