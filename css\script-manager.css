/* 脚本管理样式 */

/* 脚本列表样式 */
.scripts-list {
    max-height: 500px;
    overflow-y: auto;
}

.script-item {
    padding: 10px 16px;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.script-item:hover {
    background-color: #f8f9fa;
}

.script-item.active {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.script-item:last-child {
    border-bottom: none;
}

.script-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.script-name {
    font-weight: 600;
    color: #333;
    margin: 0;
    font-size: 14px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.script-meta-inline {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

.script-language {
    background-color: #6c757d;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    text-transform: uppercase;
}

.script-size {
    font-size: 11px;
    color: #666;
    font-weight: 500;
}

.script-language.javascript {
    background-color: #f7df1e;
    color: #000;
}

.script-language.python {
    background-color: #3776ab;
}

.script-language.bash {
    background-color: #4eaa25;
}

.script-language.powershell {
    background-color: #012456;
}

.script-language.sql {
    background-color: #336791;
}

.script-language.html {
    background-color: #e34f26;
}

.script-language.css {
    background-color: #1572b6;
}

/* 移除不再使用的样式 */

/* 脚本详情样式 */
.script-details {
    min-height: 400px;
}

.script-info {
    background-color: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.script-info h4 {
    margin: 0 0 8px 0;
    color: #333;
}

.script-info .meta-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.script-info .meta-label {
    font-weight: 600;
    color: #666;
}

.script-info .meta-value {
    color: #333;
}

.script-code-container {
    position: relative;
}

.script-code {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 16px;
    font-family: 'Courier New', Consolas, monospace;
    font-size: 13px;
    line-height: 1.5;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 400px;
    overflow-y: auto;
    margin: 0;
}

.code-editor {
    font-family: 'Courier New', Consolas, monospace;
    font-size: 13px;
    line-height: 1.5;
    resize: vertical;
}

/* 统计信息样式 */
.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #2196f3;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h5 {
    margin-bottom: 8px;
    color: #666;
}

.empty-state p {
    margin-bottom: 20px;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .script-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .script-meta-inline {
        align-self: flex-end;
    }

    .script-name {
        width: 100%;
    }

    .script-code {
        font-size: 12px;
        padding: 12px;
    }

    .code-editor {
        font-size: 12px;
    }
}

/* 滚动条样式 */
.scripts-list::-webkit-scrollbar,
.script-code::-webkit-scrollbar {
    width: 6px;
}

.scripts-list::-webkit-scrollbar-track,
.script-code::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.scripts-list::-webkit-scrollbar-thumb,
.script-code::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.scripts-list::-webkit-scrollbar-thumb:hover,
.script-code::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 20px;
    color: #666;
}

.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 搜索框样式 */
.search-container {
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

.search-input {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 13px;
    width: 100%;
}

.search-input:focus {
    outline: none;
    border-color: #2196f3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

/* 代码高亮提示 */
.code-hint {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
    font-style: italic;
}

/* 模态框样式调整 */
.modal-lg {
    max-width: 900px;
}

.modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

/* 按钮组样式 */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* 工具提示样式 */
.tooltip-inner {
    max-width: 200px;
    text-align: left;
}

/* 复制成功提示 */
.copy-success {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #28a745;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.copy-success.show {
    opacity: 1;
}

/* 脚本项目动画 */
.script-item {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 删除确认样式 */
.delete-confirm {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 8px 12px;
    margin-top: 8px;
    font-size: 13px;
}

.delete-confirm .btn {
    font-size: 12px;
    padding: 2px 8px;
    margin: 0 2px;
}



/* 压缩率显示 */
.script-size small {
    font-size: 10px;
    font-weight: bold;
    margin-left: 4px;
}

/* 统计信息增强 */
.stat-item .compression-stats {
    font-size: 10px;
    color: #28a745;
    margin-top: 2px;
}

/* 脚本项目压缩指示器 */
.script-item .compression-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: #28a745;
    color: white;
    font-size: 9px;
    padding: 1px 4px;
    border-radius: 2px;
    opacity: 0.8;
}

/* 压缩率工具提示 */
.compression-tooltip {
    position: relative;
    cursor: help;
}

.compression-tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1000;
    opacity: 0.9;
}

/* 压缩状态图标 */
.compression-icon {
    color: #28a745;
    margin-right: 4px;
    font-size: 12px;
}



/* 批量操作样式 */
.header-actions {
    display: flex;
    align-items: center;
}

.batch-toolbar {
    border-top: 1px solid #dee2e6;
}

.batch-selection {
    display: flex;
    align-items: center;
}

.batch-actions {
    display: flex;
    align-items: center;
}

.selected-count {
    font-size: 13px;
    margin-left: 8px;
}

/* 脚本项目批量选择样式 */
.script-item.batch-mode {
    padding-left: 40px;
    position: relative;
}

.script-item.batch-mode::before {
    content: '';
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid #dee2e6;
    border-radius: 3px;
    background-color: white;
    cursor: pointer;
}

.script-item.batch-mode.selected::before {
    background-color: #007bff;
    border-color: #007bff;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
    background-size: 12px;
    background-position: center;
    background-repeat: no-repeat;
}

.script-item.batch-mode.selected {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}

/* 批量模式下禁用点击选择 */
.script-item.batch-mode {
    cursor: default;
}

.script-item.batch-mode:hover {
    background-color: #f8f9fa;
}

.script-item.batch-mode.selected:hover {
    background-color: #e3f2fd;
}

/* 批量操作按钮状态 */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 批量模式动画 */
.batch-toolbar {
    transition: all 0.3s ease;
}

.script-item.batch-mode {
    transition: all 0.2s ease;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .batch-toolbar .d-flex {
        flex-direction: column;
        gap: 8px;
    }

    .batch-actions {
        justify-content: center;
        width: 100%;
    }

    .batch-selection {
        justify-content: center;
        width: 100%;
    }

    .header-actions {
        flex-direction: column;
        gap: 4px;
    }
}
