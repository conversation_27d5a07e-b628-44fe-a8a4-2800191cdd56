// 脚本管理器 - 节省内存的实现
const ScriptManager = {
    // 配置
    config: {
        STORAGE_KEY: 'scriptManagerData',

        AUTO_SAVE_DELAY: 1000, // 自动保存延迟
        COMPRESSION_THRESHOLD: 200, // 超过200字符才压缩
        ENABLE_COMPRESSION: true // 是否启用压缩
    },

    // LZ字符串压缩算法
    compression: {
        // 压缩字符串
        compress(str) {
            if (!str || str.length < ScriptManager.config.COMPRESSION_THRESHOLD) {
                return { compressed: str, isCompressed: false, originalSize: str.length, compressedSize: str.length };
            }

            try {
                const compressed = ScriptManager.compression.lzCompress(str);
                const compressionRatio = compressed.length / str.length;

                // 如果压缩后反而更大，就不压缩
                if (compressionRatio >= 0.95) {
                    return { compressed: str, isCompressed: false, originalSize: str.length, compressedSize: str.length };
                }

                return {
                    compressed: compressed,
                    isCompressed: true,
                    originalSize: str.length,
                    compressedSize: compressed.length,
                    compressionRatio: Math.round((1 - compressionRatio) * 100)
                };
            } catch (error) {
                return { compressed: str, isCompressed: false, originalSize: str.length, compressedSize: str.length };
            }
        },

        // 解压缩字符串
        decompress(data) {
            if (!data || typeof data === 'string') {
                return data || '';
            }

            try {
                if (data.isCompressed && data.compressed) {
                    return ScriptManager.compression.lzDecompress(data.compressed);
                }
                return data.compressed || '';
            } catch (error) {
                console.error('解压缩失败:', error);
                return data.compressed || '';
            }
        },

        // 简单但有效的压缩实现
        lzCompress(str) {
            try {
                // 使用浏览器内置的压缩API（如果可用）
                if (typeof CompressionStream !== 'undefined') {
                    // 现代浏览器支持的压缩流
                    return this.compressWithStream(str);
                }

                // 降级到简单的重复字符串替换压缩
                return this.simpleCompress(str);
            } catch (error) {
                console.error('压缩过程出错:', error);
                return str;
            }
        },

        // 改进的压缩算法 - 专门针对JavaScript代码
        simpleCompress(str) {
            let compressed = str;
            const patterns = new Map();
            let patternId = 0;
            let totalSaved = 0;

            // 1. 压缩常见的JavaScript关键字和模式
            const commonPatterns = [
                'function', 'document', 'getElementById', 'addEventListener',
                'style.', 'innerHTML', 'createElement', 'appendChild',
                'console.log', 'return', 'const ', 'let ', 'var ',
                'if (', 'for (', 'while (', ') {', '} else {',
                'true', 'false', 'null', 'undefined'
            ];

            // 替换常见模式
            commonPatterns.forEach(pattern => {
                if (pattern.length > 3) {
                    const regex = new RegExp(this.escapeRegex(pattern), 'g');
                    const matches = compressed.match(regex);
                    if (matches && matches.length > 2) {
                        const replacement = `§${patternId}§`;
                        const saved = (pattern.length - replacement.length) * matches.length;
                        if (saved > 0) {
                            patterns.set(replacement, pattern);
                            compressed = compressed.replace(regex, replacement);
                            totalSaved += saved;
                            patternId++;
                        }
                    }
                }
            });

            // 2. 查找重复的字符串字面量
            const stringLiterals = compressed.match(/'[^']{4,}'/g) || [];
            const quotedStrings = compressed.match(/"[^"]{4,}"/g) || [];
            const allStrings = [...stringLiterals, ...quotedStrings];

            allStrings.forEach(str => {
                const regex = new RegExp(this.escapeRegex(str), 'g');
                const matches = compressed.match(regex);
                if (matches && matches.length > 1) {
                    const replacement = `§${patternId}§`;
                    const saved = (str.length - replacement.length) * matches.length;
                    if (saved > 10) { // 至少节省10个字符才值得
                        patterns.set(replacement, str);
                        compressed = compressed.replace(regex, replacement);
                        totalSaved += saved;
                        patternId++;
                    }
                }
            });

            // 3. 查找重复的代码块
            for (let len = 50; len >= 10; len--) {
                const blocks = new Map();
                for (let i = 0; i <= compressed.length - len; i++) {
                    const block = compressed.substr(i, len);
                    if (block.includes('\n') || block.includes(';')) { // 确保是有意义的代码块
                        blocks.set(block, (blocks.get(block) || 0) + 1);
                    }
                }

                for (const [block, count] of blocks) {
                    if (count > 1) {
                        const replacement = `§${patternId}§`;
                        const saved = (block.length - replacement.length) * count;
                        if (saved > 20) {
                            patterns.set(replacement, block);
                            const regex = new RegExp(this.escapeRegex(block), 'g');
                            compressed = compressed.replace(regex, replacement);
                            totalSaved += saved;
                            patternId++;
                            break; // 只处理最长的重复块
                        }
                    }
                }
            }

            // 如果有足够的压缩效果，添加字典
            if (patterns.size > 0 && totalSaved > 100) {
                const dict = JSON.stringify(Object.fromEntries(patterns));
                const result = `DICT:${dict}:DATA:${compressed}`;
                return result;
            }

            return str; // 返回原始字符串
        },

        // 转义正则表达式特殊字符
        escapeRegex(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        },

        // 简单解压缩实现
        lzDecompress(str) {
            try {
                // 检查是否是字典压缩格式
                if (str.startsWith('DICT:')) {
                    const dictEnd = str.indexOf(':DATA:');
                    if (dictEnd !== -1) {
                        const dictStr = str.substring(5, dictEnd);
                        const data = str.substring(dictEnd + 6);
                        const dict = JSON.parse(dictStr);

                        let decompressed = data;
                        for (const [replacement, original] of Object.entries(dict)) {
                            const regex = new RegExp(this.escapeRegex(replacement), 'g');
                            decompressed = decompressed.replace(regex, original);
                        }
                        return decompressed;
                    }
                }

                // 如果不是压缩格式，直接返回
                return str;
            } catch (error) {
                console.error('解压缩失败:', error);
                return str;
            }
        }
    },

    // 数据结构 - 只存储基本信息，代码按需加载
    data: {
        scripts: [], // 脚本基本信息列表
        lastId: 0,
        statistics: {
            total: 0,
            totalSize: 0
        }
    },

    // 当前选中的脚本
    currentScript: null,
    currentScriptId: null,
    editingScript: null,

    // 批量操作相关
    batchMode: false,
    selectedScripts: new Set(),

    // 初始化
    init() {
        this.loadData();
        this.bindEvents();
        this.renderScriptsList();
        this.updateStatistics();
        console.log('脚本管理器初始化完成');
    },

    // 加载数据
    loadData() {
        try {
            const savedData = localStorage.getItem(this.config.STORAGE_KEY);
            if (savedData) {
                const parsedData = JSON.parse(savedData);
                this.data = {
                    ...this.data,
                    ...parsedData
                };
            }
        } catch (error) {
            console.error('加载脚本数据失败:', error);
            this.data = {
                scripts: [],
                lastId: 0,
                statistics: { total: 0, totalSize: 0 }
            };
        }
    },

    // 保存数据 - 只保存基本信息
    saveData() {
        try {
            localStorage.setItem(this.config.STORAGE_KEY, JSON.stringify(this.data));
            this.updateStatistics();
        } catch (error) {
            console.error('保存脚本数据失败:', error);
        }
    },

    // 绑定事件
    bindEvents() {
        // 模态框事件
        const modal = document.getElementById('scriptModal');
        if (modal) {
            modal.addEventListener('hidden.bs.modal', () => {
                this.resetForm();
            });
        }

        // 表单提交事件
        const form = document.getElementById('scriptForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveScript();
            });
        }
    },

    // 渲染脚本列表
    renderScriptsList() {
        const container = document.getElementById('scripts-list');
        if (!container) return;

        if (this.data.scripts.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-code"></i>
                    <h5>暂无脚本</h5>
                    <p>点击"新建"按钮创建你的第一个脚本</p>
                    <button class="btn btn-primary btn-sm" onclick="ScriptManager.showAddScriptModal()">
                        <i class="fas fa-plus me-1"></i>新建脚本
                    </button>
                </div>
            `;
            return;
        }

        const scriptsHtml = this.data.scripts.map(script => {
            const actualSize = this.formatSize(script.actualSize || script.size || 0);

            // 批量模式相关
            const batchModeClass = this.batchMode ? 'batch-mode' : '';
            const selectedClass = this.selectedScripts.has(script.id) ? 'selected' : '';
            const activeClass = this.currentScriptId === script.id && !this.batchMode ? 'active' : '';
            const clickHandler = this.batchMode ?
                `ScriptManager.toggleScriptSelection(${script.id})` :
                `ScriptManager.selectScript(${script.id})`;

            return `
                <div class="script-item ${batchModeClass} ${selectedClass} ${activeClass}"
                     onclick="${clickHandler}" data-script-id="${script.id}">
                    <div class="script-item-header">
                        <h6 class="script-name">${this.escapeHtml(script.name)}</h6>
                        <div class="script-meta-inline">
                            <span class="script-language ${script.language}">${script.language}</span>
                            <span class="script-size">${actualSize}</span>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = scriptsHtml;
    },

    // 选择脚本
    async selectScript(scriptId) {
        // 批量模式下不允许选择脚本查看详情
        if (this.batchMode) {
            this.toggleScriptSelection(scriptId);
            return;
        }

        try {
            // 更新选中状态
            this.currentScriptId = scriptId;
            this.renderScriptsList();

            // 加载完整脚本数据
            const script = await this.loadFullScript(scriptId);
            if (!script) {
                this.showError('脚本不存在');
                return;
            }

            this.currentScript = script;
            this.renderScriptDetails(script);

            // 显示操作按钮
            const actionsDiv = document.getElementById('script-actions');
            if (actionsDiv) {
                actionsDiv.style.display = 'block';
            }

        } catch (error) {
            console.error('选择脚本失败:', error);
            this.showError('加载脚本失败');
        }
    },

    // 加载完整脚本数据 - 按需加载代码内容
    async loadFullScript(scriptId) {
        try {
            // 从基本信息中找到脚本
            const scriptInfo = this.data.scripts.find(s => s.id === scriptId);
            if (!scriptInfo) return null;

            // 从localStorage加载完整代码
            const fullScriptKey = `script_${scriptId}`;
            const fullScriptData = localStorage.getItem(fullScriptKey);

            if (fullScriptData) {
                const parsedScript = JSON.parse(fullScriptData);

                // 解压缩代码
                const decompressedCode = this.compression.decompress(parsedScript.codeData || parsedScript.code);

                return {
                    ...scriptInfo,
                    code: decompressedCode,
                    // 添加压缩信息用于显示
                    compressionInfo: parsedScript.compressionInfo
                };
            }

            return scriptInfo;
        } catch (error) {
            console.error('加载完整脚本失败:', error);
            return null;
        }
    },

    // 渲染脚本详情
    renderScriptDetails(script) {
        const container = document.getElementById('script-details');
        const titleElement = document.getElementById('script-title');
        
        if (!container || !titleElement) return;

        // 更新标题
        titleElement.innerHTML = `<i class="fas fa-file-code me-2"></i>${this.escapeHtml(script.name)}`;

        container.innerHTML = `
            <div class="script-code-container">
                <h5>代码内容:</h5>
                <pre class="script-code">${this.escapeHtml(script.code || '// 代码加载中...')}</pre>
            </div>
        `;
    },

    // 显示添加脚本模态框
    showAddScriptModal() {
        this.editingScript = null;
        this.resetForm();
        
        const modal = new bootstrap.Modal(document.getElementById('scriptModal'));
        const title = document.getElementById('scriptModalTitle');
        
        if (title) {
            title.innerHTML = '<i class="fas fa-plus me-2"></i>新建脚本';
        }
        
        modal.show();
    },

    // 编辑当前脚本
    editCurrentScript() {
        if (!this.currentScript) {
            this.showError('请先选择一个脚本');
            return;
        }

        this.editingScript = this.currentScript;
        this.fillForm(this.currentScript);
        
        const modal = new bootstrap.Modal(document.getElementById('scriptModal'));
        const title = document.getElementById('scriptModalTitle');
        
        if (title) {
            title.innerHTML = '<i class="fas fa-edit me-2"></i>编辑脚本';
        }
        
        modal.show();
    },

    // 填充表单
    fillForm(script) {
        const nameInput = document.getElementById('scriptName');
        const langSelect = document.getElementById('scriptLanguage');
        const codeTextarea = document.getElementById('scriptCode');

        if (nameInput) nameInput.value = script.name || '';
        if (langSelect) langSelect.value = script.language || 'javascript';
        if (codeTextarea) codeTextarea.value = script.code || '';
    },

    // 重置表单
    resetForm() {
        const form = document.getElementById('scriptForm');
        if (form) {
            form.reset();
        }
    },

    // 保存脚本
    async saveScript() {
        try {
            const nameInput = document.getElementById('scriptName');
            const langSelect = document.getElementById('scriptLanguage');
            const codeTextarea = document.getElementById('scriptCode');

            if (!nameInput || !codeTextarea) {
                this.showError('表单元素未找到');
                return;
            }

            const name = nameInput.value.trim();
            const language = langSelect ? langSelect.value : 'javascript';
            const code = codeTextarea.value;

            if (!name) {
                this.showError('请输入脚本名称');
                return;
            }

            if (!code.trim()) {
                this.showError('请输入脚本代码');
                return;
            }

            const now = Date.now();
            const originalSize = new Blob([code]).size;

            // 压缩代码
            const compressionResult = this.compression.compress(code);
            const actualStorageSize = new Blob([compressionResult.compressed]).size;



            let script;
            if (this.editingScript) {
                // 编辑现有脚本
                script = {
                    ...this.editingScript,
                    name,
                    language,
                    size: originalSize, // 显示原始大小
                    actualSize: actualStorageSize, // 实际存储大小
                    updatedAt: now,
                    compressionInfo: compressionResult.isCompressed ? {
                        isCompressed: true,
                        compressionRatio: compressionResult.compressionRatio,
                        originalSize: compressionResult.originalSize,
                        compressedSize: compressionResult.compressedSize
                    } : null
                };

                // 更新基本信息
                const index = this.data.scripts.findIndex(s => s.id === script.id);
                if (index !== -1) {
                    this.data.scripts[index] = { ...script };
                    delete this.data.scripts[index].code; // 不在基本信息中存储代码
                }
            } else {
                // 新建脚本
                script = {
                    id: ++this.data.lastId,
                    name,
                    language,
                    size: originalSize, // 显示原始大小
                    actualSize: actualStorageSize, // 实际存储大小
                    createdAt: now,
                    updatedAt: now,
                    compressionInfo: compressionResult.isCompressed ? {
                        isCompressed: true,
                        compressionRatio: compressionResult.compressionRatio,
                        originalSize: compressionResult.originalSize,
                        compressedSize: compressionResult.compressedSize
                    } : null
                };

                // 添加到基本信息列表
                const scriptInfo = { ...script };
                delete scriptInfo.code; // 不在基本信息中存储代码
                this.data.scripts.push(scriptInfo);
            }

            // 单独存储压缩后的代码
            const fullScriptKey = `script_${script.id}`;
            const dataToStore = {
                codeData: compressionResult,
                compressionInfo: script.compressionInfo
            };
            localStorage.setItem(fullScriptKey, JSON.stringify(dataToStore));

            // 保存基本信息
            this.saveData();

            // 更新界面
            this.renderScriptsList();
            this.selectScript(script.id);

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('scriptModal'));
            if (modal) {
                modal.hide();
            }

        } catch (error) {
            console.error('保存脚本失败:', error);
            this.showError('保存脚本失败');
        }
    },

    // 删除当前脚本
    deleteCurrentScript() {
        if (!this.currentScript) {
            this.showError('请先选择一个脚本');
            return;
        }

        if (confirm(`确定要删除脚本"${this.currentScript.name}"吗？此操作不可撤销。`)) {
            try {
                // 删除完整代码存储
                const fullScriptKey = `script_${this.currentScript.id}`;
                localStorage.removeItem(fullScriptKey);

                // 从基本信息列表中删除
                this.data.scripts = this.data.scripts.filter(s => s.id !== this.currentScript.id);

                // 保存数据
                this.saveData();

                // 清空详情显示
                this.currentScript = null;
                this.currentScriptId = null;
                this.clearScriptDetails();

                // 更新界面
                this.renderScriptsList();

            } catch (error) {
                console.error('删除脚本失败:', error);
                this.showError('删除脚本失败');
            }
        }
    },

    // 复制脚本代码
    async copyScriptCode() {
        if (!this.currentScript || !this.currentScript.code) {
            this.showError('没有可复制的代码');
            return;
        }

        try {
            await navigator.clipboard.writeText(this.currentScript.code);
            this.showSuccess('代码已复制到剪贴板');
        } catch (error) {
            console.error('复制失败:', error);
            // 降级方案
            this.fallbackCopyToClipboard(this.currentScript.code);
        }
    },

    // 降级复制方案
    fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            this.showSuccess('代码已复制到剪贴板');
        } catch (error) {
            this.showError('复制失败，请手动复制');
        }

        document.body.removeChild(textArea);
    },

    // 清空脚本详情
    clearScriptDetails() {
        const container = document.getElementById('script-details');
        const titleElement = document.getElementById('script-title');
        const actionsDiv = document.getElementById('script-actions');

        if (titleElement) {
            titleElement.innerHTML = '<i class="fas fa-file-code me-2"></i>选择一个脚本查看详情';
        }

        if (container) {
            container.innerHTML = `
                <div class="text-center text-muted py-5">
                    <i class="fas fa-code fa-3x mb-3"></i>
                    <p>请从左侧选择一个脚本查看详情</p>
                </div>
            `;
        }

        if (actionsDiv) {
            actionsDiv.style.display = 'none';
        }
    },



    // 更新统计信息
    updateStatistics() {
        const totalScripts = this.data.scripts.length;
        const totalOriginalSize = this.data.scripts.reduce((sum, script) => sum + (script.size || 0), 0);
        const totalActualSize = this.data.scripts.reduce((sum, script) => sum + (script.actualSize || script.size || 0), 0);
        const compressionRatio = totalOriginalSize > 0 ? Math.round((1 - totalActualSize / totalOriginalSize) * 100) : 0;

        this.data.statistics = {
            total: totalScripts,
            totalSize: totalOriginalSize,
            totalActualSize: totalActualSize,
            compressionRatio: compressionRatio
        };

        // 更新界面显示
        const totalElement = document.getElementById('total-scripts');
        const sizeElement = document.getElementById('total-size');

        if (totalElement) {
            totalElement.textContent = totalScripts;
        }

        if (sizeElement) {
            const sizeText = compressionRatio > 0
                ? `${this.formatSize(totalActualSize)}`
                : this.formatSize(totalOriginalSize);
            sizeElement.textContent = sizeText;
            sizeElement.title = `原始大小: ${this.formatSize(totalOriginalSize)}, 实际存储: ${this.formatSize(totalActualSize)}`;
        }


    },



    // 格式化文件大小
    formatSize(bytes) {
        if (bytes === 0) return '0B';

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + sizes[i];
    },

    // HTML转义
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    // 显示成功消息
    showSuccess(message) {
        this.showMessage(message, 'success');
    },

    // 显示错误消息
    showError(message) {
        this.showMessage(message, 'error');
    },

    // 显示消息
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageDiv = document.createElement('div');
        messageDiv.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
        messageDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        messageDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(messageDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 3000);
    },

    // 获取数据用于同步
    getDataForSync() {
        return {
            scripts: this.data.scripts,
            lastId: this.data.lastId,
            statistics: this.data.statistics
        };
    },

    // 从同步数据恢复
    restoreFromSync(syncData) {
        if (!syncData) return;

        try {
            this.data = {
                ...this.data,
                ...syncData
            };

            this.saveData();
            this.renderScriptsList();
            this.updateStatistics();
            this.clearScriptDetails();

            console.log('脚本管理数据同步完成');
        } catch (error) {
            console.error('恢复脚本管理数据失败:', error);
        }
    },

    // 批量操作相关方法

    // 切换批量模式
    toggleBatchMode() {
        this.batchMode = !this.batchMode;
        this.selectedScripts.clear();

        const toolbar = document.getElementById('batch-toolbar');
        const batchBtn = document.getElementById('batch-delete-btn');

        if (this.batchMode) {
            toolbar.style.display = 'block';
            batchBtn.innerHTML = '<i class="fas fa-times me-1"></i>取消批量';
            batchBtn.className = 'btn btn-secondary btn-sm me-2';
            this.clearScriptDetails();
        } else {
            toolbar.style.display = 'none';
            batchBtn.innerHTML = '<i class="fas fa-trash-alt me-1"></i>批量删除';
            batchBtn.className = 'btn btn-outline-danger btn-sm me-2';
        }

        this.renderScriptsList();
        this.updateBatchUI();
    },

    // 退出批量模式
    exitBatchMode() {
        this.batchMode = false;
        this.selectedScripts.clear();

        const toolbar = document.getElementById('batch-toolbar');
        const batchBtn = document.getElementById('batch-delete-btn');

        toolbar.style.display = 'none';
        batchBtn.innerHTML = '<i class="fas fa-trash-alt me-1"></i>批量删除';
        batchBtn.className = 'btn btn-outline-danger btn-sm me-2';

        this.renderScriptsList();
    },

    // 切换脚本选择状态
    toggleScriptSelection(scriptId) {
        if (!this.batchMode) return;

        if (this.selectedScripts.has(scriptId)) {
            this.selectedScripts.delete(scriptId);
        } else {
            this.selectedScripts.add(scriptId);
        }

        this.renderScriptsList();
        this.updateBatchUI();
    },

    // 全选/取消全选
    toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('select-all-checkbox');

        if (selectAllCheckbox.checked) {
            // 全选
            this.data.scripts.forEach(script => {
                this.selectedScripts.add(script.id);
            });
        } else {
            // 取消全选
            this.selectedScripts.clear();
        }

        this.renderScriptsList();
        this.updateBatchUI();
    },

    // 反选
    selectInverse() {
        const newSelection = new Set();

        this.data.scripts.forEach(script => {
            if (!this.selectedScripts.has(script.id)) {
                newSelection.add(script.id);
            }
        });

        this.selectedScripts = newSelection;
        this.renderScriptsList();
        this.updateBatchUI();
    },

    // 更新批量操作UI
    updateBatchUI() {
        const selectedCount = this.selectedScripts.size;
        const totalCount = this.data.scripts.length;

        // 更新选中计数
        const countElement = document.getElementById('selected-count');
        if (countElement) {
            countElement.textContent = `已选择 ${selectedCount} 个脚本`;
        }

        // 更新全选复选框状态
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        if (selectAllCheckbox) {
            if (selectedCount === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (selectedCount === totalCount) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }

        // 更新删除按钮状态
        const deleteBtn = document.getElementById('batch-delete-confirm-btn');
        if (deleteBtn) {
            deleteBtn.disabled = selectedCount === 0;
        }
    },

    // 批量删除选中的脚本
    batchDeleteSelected() {
        const selectedCount = this.selectedScripts.size;

        if (selectedCount === 0) {
            this.showError('请先选择要删除的脚本');
            return;
        }

        const confirmMessage = `确定要删除选中的 ${selectedCount} 个脚本吗？此操作不可撤销。`;

        if (confirm(confirmMessage)) {
            try {
                // 删除选中的脚本
                this.selectedScripts.forEach(scriptId => {
                    // 删除完整代码存储
                    const fullScriptKey = `script_${scriptId}`;
                    localStorage.removeItem(fullScriptKey);

                    // 从基本信息列表中删除
                    this.data.scripts = this.data.scripts.filter(s => s.id !== scriptId);
                });

                // 清空选择
                this.selectedScripts.clear();

                // 保存数据
                this.saveData();

                // 退出批量模式
                this.exitBatchMode();

                // 清空详情显示
                this.currentScript = null;
                this.currentScriptId = null;
                this.clearScriptDetails();

                // 更新界面
                this.renderScriptsList();

            } catch (error) {
                console.error('批量删除脚本失败:', error);
                this.showError('批量删除脚本失败');
            }
        }
    }
};

// 全局函数，供HTML调用
function showAddScriptModal() {
    ScriptManager.showAddScriptModal();
}

function editCurrentScript() {
    ScriptManager.editCurrentScript();
}

function deleteCurrentScript() {
    ScriptManager.deleteCurrentScript();
}

function copyScriptCode() {
    ScriptManager.copyScriptCode();
}

function saveScript() {
    ScriptManager.saveScript();
}

// 批量操作全局函数
function toggleBatchMode() {
    ScriptManager.toggleBatchMode();
}

function exitBatchMode() {
    ScriptManager.exitBatchMode();
}

function toggleSelectAll() {
    ScriptManager.toggleSelectAll();
}

function selectInverse() {
    ScriptManager.selectInverse();
}

function batchDeleteSelected() {
    ScriptManager.batchDeleteSelected();
}

// 密码验证相关函数（复用主系统的逻辑）
function checkPasswordAccess() {
    // 使用与主系统相同的存储键
    const isAuthenticated = localStorage.getItem('st_authenticated') === 'true';

    if (isAuthenticated) {
        document.getElementById('password-overlay').style.display = 'none';
        document.getElementById('main-content').style.display = 'block';
        return;
    }

    document.getElementById('password-overlay').style.display = 'flex';

    const passwordInput = document.getElementById('access-password');
    const submitBtn = document.getElementById('password-submit');
    const errorDiv = document.getElementById('password-error');
    const rememberCheckbox = document.getElementById('remember-password');

    function handleSubmit() {
        const password = passwordInput.value;
        const correctPassword = 'st6666'; // 使用与主系统相同的密码

        if (password === correctPassword) {
            // 密码正确，隐藏验证层
            document.getElementById('password-overlay').style.display = 'none';
            document.getElementById('main-content').style.display = 'block';

            // 如果选择了记住密码，将验证状态保存到localStorage
            if (rememberCheckbox.checked) {
                localStorage.setItem('st_authenticated', 'true');
            }
        } else {
            // 密码错误
            errorDiv.style.display = 'block';
            passwordInput.value = '';
            passwordInput.focus();
        }
    }

    // 避免重复绑定事件
    if (!submitBtn.hasAttribute('data-bound')) {
        submitBtn.addEventListener('click', handleSubmit);
        submitBtn.setAttribute('data-bound', 'true');
    }

    if (!passwordInput.hasAttribute('data-bound')) {
        passwordInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleSubmit();
            }
        });
        passwordInput.setAttribute('data-bound', 'true');
    }

    passwordInput.focus();
}
