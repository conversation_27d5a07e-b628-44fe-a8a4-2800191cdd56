// 人生路径规划 - JavaScript 功能实现

// 全局变量
let treeData = [];
let flowData = [];
let storyData = {};
let networkData = { nodes: [], links: [] };
let currentStoryNode = 'start';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeAllDemos();
});

// 初始化所有演示
function initializeAllDemos() {
    initTreeDemo();
    initFlowDemo();
    initStoryDemo();
    initNetworkDemo();
}

// ==================== 方案一：树状分支图 ====================

function initTreeDemo() {
    treeData = [
        {
            id: 'root',
            text: '大学毕业',
            x: 50,
            y: 175,
            children: [
                {
                    id: 'work',
                    text: '直接工作',
                    x: 200,
                    y: 100,
                    children: [
                        { id: 'startup', text: '加入创业公司', x: 350, y: 50, children: [] },
                        { id: 'bigtech', text: '进入大厂', x: 350, y: 150, children: [] }
                    ]
                },
                {
                    id: 'study',
                    text: '继续深造',
                    x: 200,
                    y: 250,
                    children: [
                        { id: 'master', text: '读研究生', x: 350, y: 200, children: [] },
                        { id: 'abroad', text: '出国留学', x: 350, y: 300, children: [] }
                    ]
                }
            ]
        }
    ];
    renderTree();
}

function renderTree() {
    const svg = document.getElementById('treeSvg');
    svg.innerHTML = '';
    
    // 渲染连线
    renderTreeLinks(treeData[0], svg);
    
    // 渲染节点
    renderTreeNodes(treeData[0], svg);
}

function renderTreeLinks(node, svg) {
    if (node.children && node.children.length > 0) {
        node.children.forEach(child => {
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line.setAttribute('x1', node.x + 60);
            line.setAttribute('y1', node.y + 15);
            line.setAttribute('x2', child.x);
            line.setAttribute('y2', child.y + 15);
            line.setAttribute('stroke', '#667eea');
            line.setAttribute('stroke-width', '2');
            svg.appendChild(line);
            
            renderTreeLinks(child, svg);
        });
    }
}

function renderTreeNodes(node, svg) {
    // 创建节点组
    const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    g.setAttribute('transform', `translate(${node.x}, ${node.y})`);
    g.style.cursor = 'pointer';
    
    // 节点背景
    const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    rect.setAttribute('width', '120');
    rect.setAttribute('height', '30');
    rect.setAttribute('rx', '15');
    rect.setAttribute('fill', node.id === 'root' ? '#667eea' : '#ffffff');
    rect.setAttribute('stroke', '#667eea');
    rect.setAttribute('stroke-width', '2');
    g.appendChild(rect);
    
    // 节点文本
    const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    text.setAttribute('x', '60');
    text.setAttribute('y', '20');
    text.setAttribute('text-anchor', 'middle');
    text.setAttribute('fill', node.id === 'root' ? '#ffffff' : '#667eea');
    text.setAttribute('font-size', '12');
    text.setAttribute('font-weight', '500');
    text.textContent = node.text;
    g.appendChild(text);
    
    // 添加点击事件
    g.addEventListener('click', () => selectTreeNode(node));
    
    svg.appendChild(g);
    
    // 递归渲染子节点
    if (node.children && node.children.length > 0) {
        node.children.forEach(child => renderTreeNodes(child, svg));
    }
}

function selectTreeNode(node) {
    alert(`选择了节点: ${node.text}\n\n可以在这里添加:\n- 新的分支选择\n- 备注信息\n- 关闭此路径`);
}

function addTreeNode() {
    alert('在实际应用中，这里会弹出对话框让您:\n1. 选择父节点\n2. 输入新节点名称\n3. 设置节点位置');
}

function resetTreeDemo() {
    initTreeDemo();
}

// ==================== 方案二：流程图式 ====================

function initFlowDemo() {
    flowData = [
        {
            id: 1,
            title: '大学毕业',
            description: '完成了四年的大学学习，现在面临人生的重要选择',
            choices: [
                { text: '直接进入职场', next: 2 },
                { text: '继续深造学习', next: 3 }
            ],
            active: true
        },
        {
            id: 2,
            title: '进入职场',
            description: '选择直接工作，开始职业生涯',
            choices: [
                { text: '加入大型企业', next: 4 },
                { text: '选择创业公司', next: 5 }
            ],
            active: false
        },
        {
            id: 3,
            title: '继续深造',
            description: '选择继续学习提升自己',
            choices: [
                { text: '国内读研', next: 6 },
                { text: '出国留学', next: 7 }
            ],
            active: false
        }
    ];
    renderFlow();
}

function renderFlow() {
    const container = document.getElementById('flowContainer');
    container.innerHTML = '';
    
    flowData.forEach((step, index) => {
        const stepDiv = document.createElement('div');
        stepDiv.className = `flow-step ${step.active ? 'active' : ''}`;
        stepDiv.innerHTML = `
            <div class="step-card">
                <div class="step-header">
                    <span class="step-number">${index + 1}</span>
                    <h6 class="step-title">${step.title}</h6>
                </div>
                <p class="step-description">${step.description}</p>
                <div class="step-choices">
                    ${step.choices.map(choice => 
                        `<button class="btn btn-outline-primary btn-sm choice-btn" onclick="selectFlowChoice(${step.id}, '${choice.text}')">
                            ${choice.text}
                        </button>`
                    ).join('')}
                </div>
            </div>
            ${index < flowData.length - 1 ? '<div class="flow-arrow"><i class="fas fa-arrow-down"></i></div>' : ''}
        `;
        container.appendChild(stepDiv);
    });
    
    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .flow-step {
            margin-bottom: 20px;
        }
        .step-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            transition: all 0.3s ease;
        }
        .flow-step.active .step-card {
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }
        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .step-number {
            background: #667eea;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }
        .step-title {
            margin: 0;
            color: #667eea;
        }
        .step-description {
            color: #6c757d;
            margin-bottom: 15px;
            font-size: 14px;
        }
        .choice-btn {
            margin-right: 10px;
            margin-bottom: 5px;
        }
        .flow-arrow {
            text-align: center;
            color: #667eea;
            font-size: 20px;
            margin: 10px 0;
        }
    `;
    document.head.appendChild(style);
}

function selectFlowChoice(stepId, choice) {
    alert(`选择了: ${choice}\n\n在实际应用中，这会:\n1. 激活下一个步骤\n2. 记录选择路径\n3. 更新进度状态`);
}

function addFlowStep() {
    alert('在实际应用中，这里会添加新的流程步骤');
}

function resetFlowDemo() {
    initFlowDemo();
}

// ==================== 方案三：交互式故事树 ====================

function initStoryDemo() {
    storyData = {
        start: {
            title: '人生的十字路口',
            content: '你刚刚大学毕业，站在人生的十字路口。阳光透过窗户洒在你的毕业证书上，你感到既兴奋又迷茫。这是一个重要的时刻，你的选择将影响未来的道路...',
            choices: [
                { text: '我要立即开始工作，积累经验', next: 'work_path' },
                { text: '我想继续深造，提升自己', next: 'study_path' },
                { text: '我需要时间思考，先休息一段时间', next: 'gap_year' }
            ],
            image: '🎓'
        },
        work_path: {
            title: '踏入职场',
            content: '你决定直接进入职场。经过几轮面试，你收到了两个工作机会。一个是知名大公司的稳定职位，另一个是充满挑战的创业公司...',
            choices: [
                { text: '选择大公司，追求稳定发展', next: 'big_company' },
                { text: '加入创业公司，追求快速成长', next: 'startup' }
            ],
            image: '💼'
        },
        study_path: {
            title: '学术之路',
            content: '你选择继续深造。图书馆里的书香和实验室的忙碌让你感到充实。现在你需要决定专业方向和学习方式...',
            choices: [
                { text: '专注学术研究，追求深度', next: 'research' },
                { text: '选择应用方向，注重实践', next: 'applied' }
            ],
            image: '📚'
        },
        gap_year: {
            title: '间隔年的思考',
            content: '你决定给自己一年的时间来思考和体验。这段时间里，你可以旅行、做志愿工作，或者尝试不同的事情来发现真正的兴趣...',
            choices: [
                { text: '环游世界，开阔视野', next: 'travel' },
                { text: '做志愿工作，帮助他人', next: 'volunteer' }
            ],
            image: '🌍'
        }
    };
    renderStory();
}

function renderStory() {
    const container = document.getElementById('storyContainer');
    const currentNode = storyData[currentStoryNode];

    container.innerHTML = `
        <div class="story-scene">
            <div class="scene-header">
                <div class="scene-icon">${currentNode.image}</div>
                <h4 class="scene-title">${currentNode.title}</h4>
            </div>
            <div class="scene-content">
                <p class="scene-text">${currentNode.content}</p>
            </div>
            <div class="scene-choices">
                <h6>你的选择：</h6>
                ${currentNode.choices.map((choice, index) =>
                    `<button class="btn btn-outline-primary choice-button" onclick="makeStoryChoice('${choice.next}')">
                        ${choice.text}
                    </button>`
                ).join('')}
            </div>
        </div>
    `;

    // 添加故事样式
    if (!document.getElementById('storyStyles')) {
        const style = document.createElement('style');
        style.id = 'storyStyles';
        style.textContent = `
            .story-scene {
                background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                border-radius: 15px;
                padding: 25px;
                height: 100%;
                display: flex;
                flex-direction: column;
            }
            .scene-header {
                text-align: center;
                margin-bottom: 20px;
            }
            .scene-icon {
                font-size: 3rem;
                margin-bottom: 10px;
            }
            .scene-title {
                color: #667eea;
                font-weight: bold;
                margin: 0;
            }
            .scene-content {
                flex: 1;
                margin-bottom: 20px;
            }
            .scene-text {
                font-size: 16px;
                line-height: 1.6;
                color: #495057;
                text-align: justify;
            }
            .scene-choices h6 {
                color: #667eea;
                font-weight: bold;
                margin-bottom: 15px;
            }
            .choice-button {
                display: block;
                width: 100%;
                margin-bottom: 10px;
                text-align: left;
                padding: 12px 20px;
                border-radius: 10px;
                transition: all 0.3s ease;
            }
            .choice-button:hover {
                transform: translateX(5px);
                box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
            }
        `;
        document.head.appendChild(style);
    }
}

function makeStoryChoice(nextNode) {
    if (storyData[nextNode]) {
        currentStoryNode = nextNode;
        renderStory();
    } else {
        alert(`故事分支 "${nextNode}" 还未开发完成。\n\n在实际应用中，这里会:\n1. 继续故事情节\n2. 记录选择历史\n3. 解锁新的故事线`);
    }
}

function restartStory() {
    currentStoryNode = 'start';
    renderStory();
}

function showStoryHistory() {
    alert('在实际应用中，这里会显示:\n1. 你走过的故事路径\n2. 做过的关键选择\n3. 解锁的不同结局\n4. 可以回到之前的节点重新选择');
}

// ==================== 方案四：网络图谱 ====================

function initNetworkDemo() {
    networkData = {
        nodes: [
            { id: 'graduation', text: '大学毕业', x: 300, y: 175, type: 'start' },
            { id: 'work', text: '工作', x: 200, y: 100, type: 'choice' },
            { id: 'study', text: '深造', x: 200, y: 250, type: 'choice' },
            { id: 'bigtech', text: '大厂', x: 100, y: 50, type: 'outcome' },
            { id: 'startup', text: '创业', x: 100, y: 150, type: 'outcome' },
            { id: 'master', text: '研究生', x: 100, y: 200, type: 'outcome' },
            { id: 'phd', text: '博士', x: 100, y: 300, type: 'outcome' },
            { id: 'success', text: '事业成功', x: 50, y: 125, type: 'result' }
        ],
        links: [
            { source: 'graduation', target: 'work' },
            { source: 'graduation', target: 'study' },
            { source: 'work', target: 'bigtech' },
            { source: 'work', target: 'startup' },
            { source: 'study', target: 'master' },
            { source: 'study', target: 'phd' },
            { source: 'bigtech', target: 'success' },
            { source: 'startup', target: 'success' },
            { source: 'master', target: 'success' }
        ]
    };
    renderNetwork();
}

function renderNetwork() {
    const svg = document.getElementById('networkSvg');
    svg.innerHTML = '';

    // 渲染连线
    networkData.links.forEach(link => {
        const sourceNode = networkData.nodes.find(n => n.id === link.source);
        const targetNode = networkData.nodes.find(n => n.id === link.target);

        if (sourceNode && targetNode) {
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line.setAttribute('x1', sourceNode.x);
            line.setAttribute('y1', sourceNode.y);
            line.setAttribute('x2', targetNode.x);
            line.setAttribute('y2', targetNode.y);
            line.setAttribute('stroke', '#667eea');
            line.setAttribute('stroke-width', '2');
            line.setAttribute('opacity', '0.6');
            svg.appendChild(line);
        }
    });

    // 渲染节点
    networkData.nodes.forEach(node => {
        const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        g.setAttribute('transform', `translate(${node.x - 40}, ${node.y - 15})`);
        g.style.cursor = 'pointer';

        // 节点背景
        const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        rect.setAttribute('width', '80');
        rect.setAttribute('height', '30');
        rect.setAttribute('rx', '15');

        // 根据节点类型设置颜色
        const colors = {
            start: '#667eea',
            choice: '#28a745',
            outcome: '#ffc107',
            result: '#dc3545'
        };
        rect.setAttribute('fill', colors[node.type] || '#6c757d');
        rect.setAttribute('stroke', '#ffffff');
        rect.setAttribute('stroke-width', '2');
        g.appendChild(rect);

        // 节点文本
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('x', '40');
        text.setAttribute('y', '20');
        text.setAttribute('text-anchor', 'middle');
        text.setAttribute('fill', '#ffffff');
        text.setAttribute('font-size', '11');
        text.setAttribute('font-weight', '500');
        text.textContent = node.text;
        g.appendChild(text);

        // 添加点击事件
        g.addEventListener('click', () => selectNetworkNode(node));

        // 添加拖拽功能
        let isDragging = false;
        let startX, startY;

        g.addEventListener('mousedown', (e) => {
            isDragging = true;
            startX = e.clientX - node.x;
            startY = e.clientY - node.y;
            g.style.cursor = 'grabbing';
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                node.x = e.clientX - startX;
                node.y = e.clientY - startY;
                renderNetwork();
            }
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                g.style.cursor = 'pointer';
            }
        });

        svg.appendChild(g);
    });
}

function selectNetworkNode(node) {
    alert(`选择了节点: ${node.text}\n类型: ${node.type}\n\n在实际应用中，可以:\n1. 编辑节点信息\n2. 添加新的连接\n3. 删除节点\n4. 修改节点类型`);
}

function addNetworkNode() {
    const newNode = {
        id: 'new_' + Date.now(),
        text: '新节点',
        x: 250 + Math.random() * 100,
        y: 150 + Math.random() * 100,
        type: 'choice'
    };
    networkData.nodes.push(newNode);
    renderNetwork();
    alert('已添加新节点！\n\n在实际应用中，您可以:\n1. 编辑节点名称\n2. 设置节点类型\n3. 连接到其他节点');
}

function resetNetworkDemo() {
    initNetworkDemo();
}
