<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标修复测试</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/shopping-list.css">
    
    <style>
        /* 额外的图标修复样式 */
        .fas, .far, .fab, .fa {
            font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands" !important;
            font-weight: 900 !important;
            display: inline-block !important;
        }
        
        .fas::before, .far::before, .fab::before, .fa::before {
            display: inline-block !important;
            content: inherit !important;
            font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands" !important;
            font-weight: 900 !important;
        }
        
        /* 测试区域样式 */
        .test-section {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background-color: #f8f9fa;
        }
        
        .icon-test {
            display: inline-block;
            padding: 10px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: white;
            min-width: 100px;
            text-align: center;
        }
        
        .status-good {
            border-color: #28a745;
            background-color: #d4edda;
        }
        
        .status-bad {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1><i class="fas fa-bug"></i> 图标修复测试页面</h1>
                <p class="lead">这个页面用于测试Font Awesome图标是否正确显示</p>
                
                <div class="test-section">
                    <h3>基础图标测试</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <h5>常用图标</h5>
                            <div class="icon-test">
                                <i class="fas fa-shopping-cart"></i><br>购物车
                            </div>
                            <div class="icon-test">
                                <i class="fas fa-plus"></i><br>加号
                            </div>
                            <div class="icon-test">
                                <i class="fas fa-edit"></i><br>编辑
                            </div>
                            <div class="icon-test">
                                <i class="fas fa-trash"></i><br>删除
                            </div>
                            <div class="icon-test">
                                <i class="fas fa-check"></i><br>完成
                            </div>
                            <div class="icon-test">
                                <i class="fas fa-times"></i><br>关闭
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>导航图标</h5>
                            <div class="icon-test">
                                <i class="fas fa-arrow-left"></i><br>返回
                            </div>
                            <div class="icon-test">
                                <i class="fas fa-arrow-right"></i><br>前进
                            </div>
                            <div class="icon-test">
                                <i class="fas fa-home"></i><br>首页
                            </div>
                            <div class="icon-test">
                                <i class="fas fa-list"></i><br>列表
                            </div>
                            <div class="icon-test">
                                <i class="fas fa-tags"></i><br>标签
                            </div>
                            <div class="icon-test">
                                <i class="fas fa-chart-bar"></i><br>图表
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="test-section">
                    <h3>按钮中的图标测试</h3>
                    <div class="d-flex flex-wrap gap-2">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> 添加
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-check"></i> 完成
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button class="btn btn-danger">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                        <button class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </button>
                    </div>
                </div>
                
                <div class="test-section">
                    <h3>卡片中的图标测试</h3>
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-shopping-cart"></i> 购物清单测试卡片
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-star"></i> 测试标题
                            </h5>
                            <p class="card-text">
                                <i class="fas fa-info-circle"></i> 如果您能看到正确的图标（而不是长方形），说明修复成功！
                            </p>
                            <p class="card-text">
                                <i class="fas fa-exclamation-triangle"></i> 如果仍然看到长方形，请检查浏览器控制台是否有错误。
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="test-section">
                    <h3>导航链接</h3>
                    <div class="d-flex gap-3">
                        <a href="shopping-list.html" class="btn btn-primary">
                            <i class="fas fa-shopping-cart"></i> 购物清单
                        </a>
                        <a href="icon-test.html" class="btn btn-secondary">
                            <i class="fas fa-vial"></i> 图标测试
                        </a>
                        <a href="index.html" class="btn btn-outline-primary">
                            <i class="fas fa-home"></i> 返回首页
                        </a>
                    </div>
                </div>
                
                <div class="alert alert-info mt-4">
                    <i class="fas fa-lightbulb"></i>
                    <strong>说明：</strong> 如果图标显示正常，您应该能看到各种图标而不是长方形。如果仍有问题，请清除浏览器缓存后重新加载页面。
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载后检查图标是否正确显示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，检查图标显示状态...');
            
            // 检查Font Awesome是否正确加载
            const testIcon = document.querySelector('.fas');
            if (testIcon) {
                const computedStyle = window.getComputedStyle(testIcon, '::before');
                console.log('图标字体族:', computedStyle.fontFamily);
                console.log('图标显示状态:', computedStyle.display);
            }
        });
    </script>
</body>
</html>
