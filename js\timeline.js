/**
 * 时间线应用 - 管理日、周、月视图的时间任务
 */

// 数据存储
const timelineData = {
    day: {}, // 以日期为键存储每日任务
    week: {}, // 以周号为键存储每周任务
    month: {} // 以月份为键存储每月任务
};

// 当前视图状态
let currentView = 'day'; // 'day', 'week', 'month'
let currentDate = new Date();

// DOM元素引用
const timelineContainer = document.getElementById('timelineContainer');
const calendarContainer = document.getElementById('calendarContainer');
const currentDateElement = document.getElementById('currentDate');
const prevDateBtn = document.getElementById('prevDateBtn');
const nextDateBtn = document.getElementById('nextDateBtn');
const dayViewBtn = document.getElementById('dayViewBtn');
const monthViewBtn = document.getElementById('monthViewBtn');
const addTimeSlotBtn = document.getElementById('addTimeSlotBtn');

// 模态框引用
const addTaskModal = new bootstrap.Modal(document.getElementById('addTaskModal'));
const addTimeSlotModal = new bootstrap.Modal(document.getElementById('addTimeSlotModal'));

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    // 加载保存的数据
    loadData();
    
    // 设置当前日期显示
    updateDateDisplay();
    
    // 渲染初始视图
    renderCurrentView();
    
    // 绑定事件监听器
    bindEventListeners();
    
    // 优化添加时间段模态框
    enhanceTimeSlotModal();
});

// 优化添加时间段模态框
function enhanceTimeSlotModal() {
    // 获取模态框元素
    const modalBody = document.querySelector('#addTimeSlotModal .modal-body form');
    
    // 添加时间段标题字段
    const titleFieldHTML = `
        <div class="mb-3">
            <label for="timeSlotTitle" class="form-label">时间段标题 (可选)</label>
            <input type="text" class="form-control" id="timeSlotTitle" placeholder="如：晨间学习、会议时间">
        </div>
    `;
    
    // 插入到表单开头
    modalBody.insertAdjacentHTML('afterbegin', titleFieldHTML);
}

// 加载保存的数据
function loadData() {
    const savedDayData = localStorage.getItem('timelineDay');
    const savedWeekData = localStorage.getItem('timelineWeek');
    const savedMonthData = localStorage.getItem('timelineMonth');
    
    if (savedDayData) timelineData.day = JSON.parse(savedDayData);
    if (savedWeekData) timelineData.week = JSON.parse(savedWeekData);
    if (savedMonthData) timelineData.month = JSON.parse(savedMonthData);
}

// 保存数据
function saveData() {
    localStorage.setItem('timelineDay', JSON.stringify(timelineData.day));
    localStorage.setItem('timelineWeek', JSON.stringify(timelineData.week));
    localStorage.setItem('timelineMonth', JSON.stringify(timelineData.month));
}

// 绑定事件监听器
function bindEventListeners() {
    // 视图切换
    dayViewBtn.addEventListener('click', () => switchView('day'));
    monthViewBtn.addEventListener('click', () => switchView('month'));
    
    // 日期导航
    prevDateBtn.addEventListener('click', navigateToPrevious);
    nextDateBtn.addEventListener('click', navigateToNext);
    
    // 添加时间段按钮 - 只在日视图时使用
    addTimeSlotBtn.addEventListener('click', showAddTimeSlotModal);
    document.getElementById('saveTimeSlotBtn').addEventListener('click', saveNewTimeSlot);
    
    // 保存任务
    document.getElementById('saveTaskBtn').addEventListener('click', saveNewTask);
    
    // 委托事件处理
    timelineContainer.addEventListener('click', handleTimelineContainerClick);
    
    // 当模态框隐藏时重置事件
    const addTimeSlotModalEl = document.getElementById('addTimeSlotModal');
    addTimeSlotModalEl.addEventListener('hidden.bs.modal', () => {
        // 重置表单
        document.getElementById('addTimeSlotForm').reset();
        
        // 重置保存按钮逻辑
        const saveBtn = document.getElementById('saveTimeSlotBtn');
        saveBtn.removeEventListener('click', saveEditTimeSlot);
        saveBtn.addEventListener('click', saveNewTimeSlot);
        
        // 删除删除按钮
        const deleteBtn = document.getElementById('deleteTimeSlotBtn');
        if (deleteBtn) {
            deleteBtn.remove();
        }
        
        // 删除编辑字段
        const editField = document.getElementById('editTimeSlotId');
        if (editField) {
            editField.remove();
        }
        
        // 删除日期索引字段
        const dayIndexField = document.getElementById('dayIndexField');
        if (dayIndexField) {
            dayIndexField.remove();
        }
        
        // 重置模态框标题
        document.getElementById('addTimeSlotModalLabel').textContent = '添加时间段';
    });
}

// 处理时间线容器的点击事件（事件委托）
function handleTimelineContainerClick(e) {
    const target = e.target;
    
    // 添加任务按钮
    if (target.classList.contains('add-task-btn') || target.closest('.add-task-btn')) {
        const timeSlotElement = target.closest('.timeline-unit');
        const timeSlotId = timeSlotElement.dataset.hour || timeSlotElement.dataset.id;
        showAddTaskModal(timeSlotId);
    }
    
    // 添加任务在某任务之后按钮
    if (target.classList.contains('add-task-below-btn') || target.closest('.add-task-below-btn')) {
        const taskCard = target.closest('.task-card');
        const timeSlotElement = target.closest('.timeline-unit');
        const timeSlotId = timeSlotElement.dataset.hour || timeSlotElement.dataset.id;
        const taskIndex = Array.from(taskCard.parentNode.children).indexOf(taskCard);
        showAddTaskModal(timeSlotId, taskIndex + 1);
    }
    
    // 完成任务按钮
    if (target.classList.contains('complete-task-btn') || target.closest('.complete-task-btn')) {
        const taskCard = target.closest('.task-card');
        toggleTaskCompletion(taskCard, true);
    }
    
    // 撤销完成任务按钮
    if (target.classList.contains('uncomplete-task-btn') || target.closest('.uncomplete-task-btn')) {
        const taskCard = target.closest('.task-card');
        toggleTaskCompletion(taskCard, false);
    }
    
    // 删除任务按钮
    if (target.classList.contains('delete-task-btn') || target.closest('.delete-task-btn')) {
        const taskCard = target.closest('.task-card');
        deleteTask(taskCard);
    }
}

// 切换任务完成状态
function toggleTaskCompletion(taskCard, isCompleted) {
    const timeSlotElement = taskCard.closest('.timeline-unit');
    const timeSlotId = timeSlotElement.dataset.hour || timeSlotElement.dataset.id;
    const taskIndex = Array.from(taskCard.parentNode.children).indexOf(taskCard);
    
    // 获取当前日期键
    const dateKey = formatDateKey(currentDate);
    
    // 更新数据
    if (!timelineData[currentView][dateKey]) {
        timelineData[currentView][dateKey] = {};
    }
    
    if (!timelineData[currentView][dateKey][timeSlotId]) {
        timelineData[currentView][dateKey][timeSlotId] = {
            tasks: []
        };
    }
    
    if (timelineData[currentView][dateKey][timeSlotId].tasks[taskIndex]) {
        timelineData[currentView][dateKey][timeSlotId].tasks[taskIndex].completed = isCompleted;
    }
    
    // 保存数据
    saveData();
    
    // 更新UI
    if (isCompleted) {
        taskCard.classList.add('task-completed');
        const completeBtn = taskCard.querySelector('.complete-task-btn');
        if (completeBtn) {
            completeBtn.outerHTML = `
                <button class="btn btn-sm btn-warning uncomplete-task-btn">
                    <i class="fas fa-undo"></i>
                </button>
            `;
        }
    } else {
        taskCard.classList.remove('task-completed');
        const uncompleteBtn = taskCard.querySelector('.uncomplete-task-btn');
        if (uncompleteBtn) {
            uncompleteBtn.outerHTML = `
                <button class="btn btn-sm btn-success complete-task-btn">
                    <i class="fas fa-check"></i>
                </button>
            `;
        }
    }
}

// 删除任务
function deleteTask(taskCard) {
    if (!confirm('确定要删除这个任务吗？')) return;
    
    const timeSlotElement = taskCard.closest('.timeline-unit');
    const timeSlotId = timeSlotElement.dataset.hour || timeSlotElement.dataset.id;
    const taskIndex = Array.from(taskCard.parentNode.children).indexOf(taskCard);
    
    // 获取当前日期键
    const dateKey = formatDateKey(currentDate);
    
    // 更新数据
    if (timelineData[currentView][dateKey] && 
        timelineData[currentView][dateKey][timeSlotId] && 
        timelineData[currentView][dateKey][timeSlotId].tasks) {
        
        // 移除任务
        timelineData[currentView][dateKey][timeSlotId].tasks.splice(taskIndex, 1);
        
        // 保存数据
        saveData();
        
        // 更新UI
        taskCard.remove();
    }
}

// 格式化日期键
function formatDateKey(date) {
    if (currentView === 'day') {
        return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
    } else if (currentView === 'week') {
        // 获取周号
        const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
        const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
        const weekNumber = Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
        return `${date.getFullYear()}-W${weekNumber}`;
    } else if (currentView === 'month') {
        return `${date.getFullYear()}-${date.getMonth() + 1}`;
    }
}

// 更新日期显示
function updateDateDisplay() {
    if (currentView === 'day') {
        currentDateElement.textContent = currentDate.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    } else if (currentView === 'week') {
        const weekStart = new Date(currentDate);
        const day = currentDate.getDay();
        const diff = currentDate.getDate() - day + (day === 0 ? -6 : 1);
        weekStart.setDate(diff);
        
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        
        currentDateElement.textContent = `${weekStart.toLocaleDateString('zh-CN', {month: 'short', day: 'numeric'})} - ${weekEnd.toLocaleDateString('zh-CN', {year: 'numeric', month: 'short', day: 'numeric'})}`;
    } else if (currentView === 'month') {
        currentDateElement.textContent = currentDate.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long'
        });
    }
}

// 切换视图
function switchView(view) {
    // 移除所有按钮的active类
    dayViewBtn.classList.remove('active');
    monthViewBtn.classList.remove('active');
    
    // 添加active类到选中的按钮
    if (view === 'day') dayViewBtn.classList.add('active');
    else if (view === 'month') monthViewBtn.classList.add('active');
    
    // 更新当前视图
    currentView = view;
    
    // 更新日期显示
    updateDateDisplay();
    
    // 更新悬浮添加按钮的显示状态
    if (view === 'day') {
        addTimeSlotBtn.style.display = 'flex';
    } else {
        addTimeSlotBtn.style.display = 'none';
    }
    
    // 渲染视图
    renderCurrentView();
}

// 导航到上一个日期/月
function navigateToPrevious() {
    if (currentView === 'day') {
        currentDate.setDate(currentDate.getDate() - 1);
    } else if (currentView === 'month') {
        currentDate.setMonth(currentDate.getMonth() - 1);
    }
    
    updateDateDisplay();
    renderCurrentView();
}

// 导航到下一个日期/月
function navigateToNext() {
    if (currentView === 'day') {
        currentDate.setDate(currentDate.getDate() + 1);
    } else if (currentView === 'month') {
        currentDate.setMonth(currentDate.getMonth() + 1);
    }
    
    updateDateDisplay();
    renderCurrentView();
}

// 渲染当前视图
function renderCurrentView() {
    // 清空容器
    timelineContainer.innerHTML = '<div class="timeline-vertical-line"></div><div class="timeline-connector"></div>';
    
    // 根据视图类型渲染不同的内容
    if (currentView === 'day') {
        renderDayView();
        calendarContainer.style.display = 'none';
    } else if (currentView === 'month') {
        renderMonthView();
        calendarContainer.style.display = 'grid';
    }
}

// 渲染日视图
function renderDayView() {
    const dateKey = formatDateKey(currentDate);
    const dayData = timelineData.day[dateKey] || {};
    
    // 检查是否有自定义时间段
    const timeSlots = dayData.timeSlots || [];
    
    // 如果没有自定义时间段，显示提示信息
    if (timeSlots.length === 0) {
        timelineContainer.innerHTML += `
            <div class="text-center mt-4 mb-4">
                <p class="text-muted">今天还没有安排时间段</p>
                <button class="btn btn-primary" id="addFirstTimeSlotBtn">
                    <i class="fas fa-plus"></i> 添加第一个时间段
                </button>
            </div>
        `;
        
        // 添加事件监听器
        setTimeout(() => {
            const addFirstTimeSlotBtn = document.getElementById('addFirstTimeSlotBtn');
            if (addFirstTimeSlotBtn) {
                addFirstTimeSlotBtn.addEventListener('click', showAddTimeSlotModal);
            }
        }, 0);
        
        return;
    }
    
    // 按开始时间排序时间段
    const sortedTimeSlots = [...timeSlots].sort((a, b) => {
        return a.startTime.localeCompare(b.startTime);
    });
    
    // 生成自定义时间段块
    sortedTimeSlots.forEach((timeSlot, index) => {
        const timeSlotId = timeSlot.id;
        const startTime = timeSlot.startTime;
        const endTime = timeSlot.endTime;
        const tasks = timeSlot.tasks || [];
        
        // 提取小时和分钟用于显示
        const startHour = parseInt(startTime.split(':')[0]);
        const startMinute = parseInt(startTime.split(':')[1]);
        const startDisplay = `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')}`;
        
        const endHour = parseInt(endTime.split(':')[0]);
        const endMinute = parseInt(endTime.split(':')[1]);
        const endDisplay = `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`;
        
        const timeUnitHTML = `
            <div class="timeline-unit" data-id="${timeSlotId}">
                <div class="timeline-time">${startDisplay}</div>
                <div class="timeline-dot"></div>
                <div class="timeline-item">
                    <div class="timeline-item-header d-flex justify-content-between align-items-center flex-wrap">
                        <h5 class="mb-0">${startDisplay} - ${endDisplay}</h5>
                        <div>
                            <button class="btn btn-sm btn-outline-secondary edit-time-slot-btn me-1" data-id="${timeSlotId}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary add-task-btn">
                                <i class="fas fa-plus"></i> 添加任务
                            </button>
                        </div>
                    </div>
                    <div class="task-list mt-3">
                        ${renderTasks(tasks)}
                    </div>
                </div>
            </div>
        `;
        
        timelineContainer.innerHTML += timeUnitHTML;
    });
    
    // 绑定事件监听器
    setTimeout(() => {
        document.querySelectorAll('.edit-time-slot-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const timeSlotId = e.currentTarget.dataset.id;
                showEditTimeSlotModal(timeSlotId);
            });
        });
    }, 0);
}

// 渲染月视图
function renderMonthView() {
    // 获取当前月的第一天和最后一天
    const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
    
    // 渲染日历
    renderCalendar(firstDayOfMonth, lastDayOfMonth.getDate());
    
    // 获取月键
    const monthKey = formatDateKey(currentDate);
    const monthData = timelineData.month[monthKey] || {};
    
    // 创建整月的时间块
    const daysInMonth = lastDayOfMonth.getDate();
    
    for (let day = 1; day <= daysInMonth; day++) {
        const currentDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
        const dayName = currentDayOfMonth.toLocaleDateString('zh-CN', { weekday: 'short' });
        const dayKey = `day-${day}`;
        const dayData = monthData[dayKey] || {};
        
        // 获取该天的时间段
        const timeSlots = dayData.timeSlots || [];
        // 旧数据兼容，如果没有timeSlots但有tasks，创建默认时间段
        const hasTasks = dayData.tasks && dayData.tasks.length > 0;
        const hasTimeSlots = timeSlots.length > 0;
        
        // 创建日时间块的容器
        const dayContainerHTML = `
            <div class="timeline-unit" data-day="${day}">
                <div class="timeline-time">${day}</div>
                <div class="timeline-dot"></div>
                <div class="timeline-item">
                    <div class="timeline-item-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">${day}日 (${dayName})</h5>
                        <div>
                            <button class="btn btn-sm btn-outline-secondary add-month-time-slot-btn" data-day="${day}">
                                <i class="fas fa-clock"></i> 添加时间段
                            </button>
                        </div>
                    </div>
                    <div class="day-time-slots mt-3" id="month-day-time-slots-${day}">
                        ${hasTimeSlots ? '' : (hasTasks ? 
                            `<div class="timeline-sub-item">
                                <h6 class="text-muted mb-2">全天</h6>
                                <div class="task-list">
                                    ${renderTasks(dayData.tasks || [])}
                                </div>
                             </div>` : 
                            `<div class="text-center text-muted py-3">
                                <p>未添加时间段</p>
                             </div>`)}
                    </div>
                </div>
            </div>
        `;
        
        timelineContainer.innerHTML += dayContainerHTML;
        
        // 如果有自定义时间段，渲染它们
        if (hasTimeSlots) {
            const dayTimeSlotsContainer = document.getElementById(`month-day-time-slots-${day}`);
            
            // 按时间排序
            const sortedTimeSlots = [...timeSlots].sort((a, b) => {
                return a.startTime.localeCompare(b.startTime);
            });
            
            sortedTimeSlots.forEach(timeSlot => {
                const timeSlotHTML = `
                    <div class="timeline-sub-item mb-3" data-id="${timeSlot.id}">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-1">
                                ${timeSlot.startTime} - ${timeSlot.endTime}
                                ${timeSlot.title ? `<span class="badge bg-light text-dark ms-2">${timeSlot.title}</span>` : ''}
                            </h6>
                            <div>
                                <button class="btn btn-sm btn-outline-secondary edit-month-time-slot-btn" data-id="${timeSlot.id}" data-day="${day}">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-primary add-task-btn" data-id="${timeSlot.id}">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="task-list mt-2">
                            ${renderTasks(timeSlot.tasks || [])}
                        </div>
                    </div>
                `;
                
                dayTimeSlotsContainer.innerHTML += timeSlotHTML;
            });
        }
    }
    
    // 绑定事件监听器
    setTimeout(() => {
        // 添加时间段按钮
        document.querySelectorAll('.add-month-time-slot-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const dayIndex = e.currentTarget.dataset.day;
                showAddMonthTimeSlotModal(dayIndex);
            });
        });
        
        // 编辑时间段按钮
        document.querySelectorAll('.edit-month-time-slot-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const timeSlotId = e.currentTarget.dataset.id;
                const dayIndex = e.currentTarget.dataset.day;
                showEditMonthTimeSlotModal(timeSlotId, dayIndex);
            });
        });
        
        // 添加任务按钮
        document.querySelectorAll('.timeline-sub-item .add-task-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const timeSlotId = e.currentTarget.dataset.id;
                showAddTaskModal(timeSlotId);
            });
        });
    }, 0);
}

// 渲染日历
function renderCalendar(startDate, numDays) {
    calendarContainer.innerHTML = '';
    
    // 渲染月日历
    const dayNames = ['日', '一', '二', '三', '四', '五', '六'];
    
    // 添加星期名
    for (const dayName of dayNames) {
        calendarContainer.innerHTML += `<div class="calendar-day day-name">${dayName}</div>`;
    }
    
    // 添加上个月的最后几天（填充第一周的空白）
    const firstDayOfWeek = startDate.getDay();
    if (firstDayOfWeek > 0) {
        const prevMonthLastDay = new Date(startDate);
        prevMonthLastDay.setDate(0);
        const prevMonthDays = prevMonthLastDay.getDate();
        
        for (let i = firstDayOfWeek - 1; i >= 0; i--) {
            const dayNumber = prevMonthDays - i;
            calendarContainer.innerHTML += `<div class="calendar-day text-muted">${dayNumber}</div>`;
        }
    }
    
    // 添加本月的日期
    for (let i = 1; i <= numDays; i++) {
        const day = new Date(startDate.getFullYear(), startDate.getMonth(), i);
        const isCurrentDay = day.toDateString() === new Date().toDateString();
        const className = isCurrentDay ? 'calendar-day active' : 'calendar-day';
        
        calendarContainer.innerHTML += `<div class="${className}">${i}</div>`;
    }
    
    // 添加下个月的前几天（填充最后一周的空白）
    const lastDayOfWeek = new Date(startDate.getFullYear(), startDate.getMonth(), numDays).getDay();
    if (lastDayOfWeek < 6) {
        for (let i = 1; i <= 6 - lastDayOfWeek; i++) {
            calendarContainer.innerHTML += `<div class="calendar-day text-muted">${i}</div>`;
        }
    }
}

// 渲染任务列表
function renderTasks(tasks = []) {
    if (!tasks.length) {
        return '<div class="no-tasks-message">无任务</div>';
    }
    
    return tasks.map(task => {
        const priorityClass = `task-priority-${task.priority || 'medium'}`;
        const completedClass = task.completed ? 'task-completed' : '';
        const completeButton = task.completed ? 
            `<button class="btn btn-sm btn-warning uncomplete-task-btn"><i class="fas fa-undo"></i></button>` : 
            `<button class="btn btn-sm btn-success complete-task-btn"><i class="fas fa-check"></i></button>`;
        
        return `
            <div class="task-card ${priorityClass} ${completedClass}">
                <div class="task-title">${task.title}</div>
                <div class="task-description">${task.description || ''}</div>
                <div class="task-actions">
                    ${completeButton}
                    <button class="btn btn-sm btn-info add-task-below-btn">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="btn btn-sm btn-danger delete-task-btn">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }).join('');
}

// 显示添加任务模态框
function showAddTaskModal(timeSlotId, taskPositionIndex = null) {
    // 重置表单
    document.getElementById('addTaskForm').reset();
    
    // 设置时间段ID和任务位置索引
    document.getElementById('timeSlotId').value = timeSlotId;
    document.getElementById('taskPositionIndex').value = taskPositionIndex !== null ? taskPositionIndex : '';
    
    // 显示模态框
    addTaskModal.show();
}

// 保存新任务
function saveNewTask() {
    const taskTitle = document.getElementById('taskTitle').value.trim();
    if (!taskTitle) {
        alert('请输入任务名称');
        return;
    }
    
    const timeSlotId = document.getElementById('timeSlotId').value;
    const taskPositionIndexInput = document.getElementById('taskPositionIndex').value;
    const taskPositionIndex = taskPositionIndexInput ? parseInt(taskPositionIndexInput) : null;
    const taskDescription = document.getElementById('taskDescription').value.trim();
    const taskPriority = document.getElementById('taskPriority').value;
    
    // 获取日期键
    const dateKey = formatDateKey(currentDate);
    
    // 创建新任务
    const newTask = {
        id: 'task-' + Date.now().toString(),
        title: taskTitle,
        description: taskDescription,
        priority: taskPriority,
        completed: false,
        createdAt: new Date().toISOString()
    };
    
    // 更新数据存储
    if (currentView === 'day') {
        // 确保有日期数据
        if (!timelineData[currentView][dateKey]) {
            timelineData[currentView][dateKey] = {
                timeSlots: []
            };
        }
        
        // 查找时间段
        const timeSlotIndex = timelineData[currentView][dateKey].timeSlots.findIndex(slot => slot.id === timeSlotId);
        
        if (timeSlotIndex !== -1) {
            // 确保时间段有tasks数组
            if (!timelineData[currentView][dateKey].timeSlots[timeSlotIndex].tasks) {
                timelineData[currentView][dateKey].timeSlots[timeSlotIndex].tasks = [];
            }
            
            // 在指定位置插入任务，或者添加到末尾
            if (taskPositionIndex !== null) {
                timelineData[currentView][dateKey].timeSlots[timeSlotIndex].tasks.splice(taskPositionIndex, 0, newTask);
            } else {
                timelineData[currentView][dateKey].timeSlots[timeSlotIndex].tasks.push(newTask);
            }
        }
    } else if (currentView === 'month') {
        // 月视图的逻辑
        if (!timelineData[currentView][dateKey]) {
            timelineData[currentView][dateKey] = {};
        }
        
        // 查找时间段（需要检查是哪一天）
        const dayKeys = Object.keys(timelineData[currentView][dateKey]);
        let found = false;
        
        for (const dayKey of dayKeys) {
            if (!timelineData[currentView][dateKey][dayKey]) continue;
            
            // 尝试查找特定的时间段ID
            if (timelineData[currentView][dateKey][dayKey].timeSlots) {
                const timeSlotIndex = timelineData[currentView][dateKey][dayKey].timeSlots.findIndex(slot => slot.id === timeSlotId);
                
                if (timeSlotIndex !== -1) {
                    // 确保时间段有tasks数组
                    if (!timelineData[currentView][dateKey][dayKey].timeSlots[timeSlotIndex].tasks) {
                        timelineData[currentView][dateKey][dayKey].timeSlots[timeSlotIndex].tasks = [];
                    }
                    
                    // 在指定位置插入任务，或者添加到末尾
                    if (taskPositionIndex !== null) {
                        timelineData[currentView][dateKey][dayKey].timeSlots[timeSlotIndex].tasks.splice(taskPositionIndex, 0, newTask);
                    } else {
                        timelineData[currentView][dateKey][dayKey].timeSlots[timeSlotIndex].tasks.push(newTask);
                    }
                    
                    found = true;
                    break;
                }
            }
            
            // 向后兼容：检查旧的数据结构
            if (!found && dayKey === timeSlotId) {
                if (!timelineData[currentView][dateKey][dayKey].tasks) {
                    timelineData[currentView][dateKey][dayKey].tasks = [];
                }
                
                if (taskPositionIndex !== null) {
                    timelineData[currentView][dateKey][dayKey].tasks.splice(taskPositionIndex, 0, newTask);
                } else {
                    timelineData[currentView][dateKey][dayKey].tasks.push(newTask);
                }
                
                found = true;
                break;
            }
        }
    }
    
    // 保存数据
    saveData();
    
    // 关闭模态框
    addTaskModal.hide();
    
    // 重新渲染视图
    renderCurrentView();
}

// 显示添加时间段模态框
function showAddTimeSlotModal() {
    // 重置表单
    document.getElementById('addTimeSlotForm').reset();
    
    // 预设时间
    if (currentView === 'day') {
        const currentHour = new Date().getHours();
        document.getElementById('startTime').value = `${currentHour.toString().padStart(2, '0')}:00`;
        document.getElementById('endTime').value = `${(currentHour + 1).toString().padStart(2, '0')}:00`;
    }
    
    // 确保是添加模式
    document.getElementById('addTimeSlotModalLabel').textContent = '添加时间段';
    
    // 确保保存按钮使用正确的处理函数
    const saveBtn = document.getElementById('saveTimeSlotBtn');
    saveBtn.removeEventListener('click', saveEditTimeSlot);
    saveBtn.addEventListener('click', saveNewTimeSlot);
    
    // 删除删除按钮
    const deleteBtn = document.getElementById('deleteTimeSlotBtn');
    if (deleteBtn) {
        deleteBtn.remove();
    }
    
    // 显示模态框
    addTimeSlotModal.show();
}

// 保存新时间段
function saveNewTimeSlot() {
    // 获取开始和结束时间
    const startTime = document.getElementById('startTime').value;
    const endTime = document.getElementById('endTime').value;
    
    if (!startTime || !endTime) {
        alert('请设置开始和结束时间');
        return;
    }
    
    if (startTime >= endTime) {
        alert('开始时间必须早于结束时间');
        return;
    }
    
    // 获取时间段标题（可选）
    const timeSlotTitle = document.getElementById('timeSlotTitle')?.value || '';
    
    // 获取当前日期键
    const dateKey = formatDateKey(currentDate);
    
    // 创建时间段ID
    const timeSlotId = 'ts-' + Date.now().toString();
    
    // 创建新时间段对象
    const newTimeSlot = {
        id: timeSlotId,
        startTime: startTime,
        endTime: endTime,
        title: timeSlotTitle,
        tasks: [],
        createdAt: new Date().toISOString()
    };
    
    if (currentView === 'day') {
        // 确保有日期数据
        if (!timelineData[currentView][dateKey]) {
            timelineData[currentView][dateKey] = {
                timeSlots: []
            };
        } else if (!timelineData[currentView][dateKey].timeSlots) {
            timelineData[currentView][dateKey].timeSlots = [];
        }
        
        // 添加新时间段到列表
        timelineData[currentView][dateKey].timeSlots.push(newTimeSlot);
        
    } else if (currentView === 'week' || currentView === 'month') {
        // 获取用户选择哪一天
        const selectedDay = prompt('请输入日期编号', '1');
        if (!selectedDay) return;
        
        const dayKey = `day-${selectedDay}`;
        
        if (!timelineData[currentView][dateKey]) {
            timelineData[currentView][dateKey] = {};
        }
        
        // 添加或更新时间段
        if (!timelineData[currentView][dateKey][dayKey]) {
            timelineData[currentView][dateKey][dayKey] = {
                timeSlots: [newTimeSlot]
            };
        } else {
            if (!timelineData[currentView][dateKey][dayKey].timeSlots) {
                timelineData[currentView][dateKey][dayKey].timeSlots = [];
            }
            timelineData[currentView][dateKey][dayKey].timeSlots.push(newTimeSlot);
        }
    }
    
    // 保存数据
    saveData();
    
    // 关闭模态框
    addTimeSlotModal.hide();
    
    // 重新渲染视图
    renderCurrentView();
}

// 显示编辑时间段模态框
function showEditTimeSlotModal(timeSlotId) {
    // 获取当前日期键
    const dateKey = formatDateKey(currentDate);
    
    // 获取时间段数据
    let timeSlot;
    
    if (currentView === 'day') {
        // 查找时间段
        timeSlot = timelineData[currentView][dateKey]?.timeSlots?.find(slot => slot.id === timeSlotId);
    } else if (currentView === 'month') {
        // 查找时间段（需要额外检查是哪一天）
        const dayKeys = Object.keys(timelineData[currentView][dateKey] || {});
        for (const dayKey of dayKeys) {
            const foundSlot = timelineData[currentView][dateKey][dayKey]?.timeSlots?.find(slot => slot.id === timeSlotId);
            if (foundSlot) {
                timeSlot = foundSlot;
                break;
            }
        }
    }
    
    if (!timeSlot) {
        alert('找不到该时间段');
        return;
    }
    
    // 设置表单值
    document.getElementById('startTime').value = timeSlot.startTime;
    document.getElementById('endTime').value = timeSlot.endTime;
    if (document.getElementById('timeSlotTitle')) {
        document.getElementById('timeSlotTitle').value = timeSlot.title || '';
    }
    
    // 添加时间段ID到隐藏字段
    const timeSlotIdField = document.getElementById('editTimeSlotId') || 
                           document.createElement('input');
    timeSlotIdField.type = 'hidden';
    timeSlotIdField.id = 'editTimeSlotId';
    timeSlotIdField.value = timeSlotId;
    
    // 确保表单中有隐藏字段
    const form = document.getElementById('addTimeSlotForm');
    if (!document.getElementById('editTimeSlotId')) {
        form.appendChild(timeSlotIdField);
    }
    
    // 更改模态框标题和按钮
    document.getElementById('addTimeSlotModalLabel').textContent = '编辑时间段';
    
    // 更改保存按钮逻辑
    const saveBtn = document.getElementById('saveTimeSlotBtn');
    saveBtn.removeEventListener('click', saveNewTimeSlot);
    saveBtn.addEventListener('click', saveEditTimeSlot);
    
    // 添加删除按钮
    let deleteBtn = document.getElementById('deleteTimeSlotBtn');
    if (!deleteBtn) {
        deleteBtn = document.createElement('button');
        deleteBtn.id = 'deleteTimeSlotBtn';
        deleteBtn.type = 'button';
        deleteBtn.className = 'btn btn-danger';
        deleteBtn.textContent = '删除';
        deleteBtn.addEventListener('click', deleteTimeSlot);
        
        // 添加到模态框底部
        const modalFooter = document.querySelector('#addTimeSlotModal .modal-footer');
        modalFooter.insertBefore(deleteBtn, modalFooter.firstChild);
    }
    
    // 显示模态框
    addTimeSlotModal.show();
}

// 保存编辑后的时间段
function saveEditTimeSlot() {
    // 获取开始和结束时间
    const startTime = document.getElementById('startTime').value;
    const endTime = document.getElementById('endTime').value;
    
    if (!startTime || !endTime) {
        alert('请设置开始和结束时间');
        return;
    }
    
    if (startTime >= endTime) {
        alert('开始时间必须早于结束时间');
        return;
    }
    
    // 获取时间段标题（可选）
    const timeSlotTitle = document.getElementById('timeSlotTitle')?.value || '';
    
    // 获取时间段ID
    const timeSlotId = document.getElementById('editTimeSlotId').value;
    
    // 获取当前日期键
    const dateKey = formatDateKey(currentDate);
    
    // 更新时间段
    if (currentView === 'day') {
        // 查找并更新时间段
        const timeSlotIndex = timelineData[currentView][dateKey].timeSlots.findIndex(slot => slot.id === timeSlotId);
        if (timeSlotIndex !== -1) {
            timelineData[currentView][dateKey].timeSlots[timeSlotIndex].startTime = startTime;
            timelineData[currentView][dateKey].timeSlots[timeSlotIndex].endTime = endTime;
            timelineData[currentView][dateKey].timeSlots[timeSlotIndex].title = timeSlotTitle;
            timelineData[currentView][dateKey].timeSlots[timeSlotIndex].updatedAt = new Date().toISOString();
        }
    } else if (currentView === 'month') {
        // 查找并更新时间段（需要额外检查是哪一天）
        const dayKeys = Object.keys(timelineData[currentView][dateKey] || {});
        dayLoop: for (const dayKey of dayKeys) {
            if (timelineData[currentView][dateKey][dayKey]?.timeSlots) {
                const timeSlotIndex = timelineData[currentView][dateKey][dayKey].timeSlots.findIndex(slot => slot.id === timeSlotId);
                if (timeSlotIndex !== -1) {
                    timelineData[currentView][dateKey][dayKey].timeSlots[timeSlotIndex].startTime = startTime;
                    timelineData[currentView][dateKey][dayKey].timeSlots[timeSlotIndex].endTime = endTime;
                    timelineData[currentView][dateKey][dayKey].timeSlots[timeSlotIndex].title = timeSlotTitle;
                    timelineData[currentView][dateKey][dayKey].timeSlots[timeSlotIndex].updatedAt = new Date().toISOString();
                    break dayLoop;
                }
            }
        }
    }
    
    // 保存数据
    saveData();
    
    // 关闭模态框
    addTimeSlotModal.hide();
    
    // 重置保存按钮逻辑
    const saveBtn = document.getElementById('saveTimeSlotBtn');
    saveBtn.removeEventListener('click', saveEditTimeSlot);
    saveBtn.addEventListener('click', saveNewTimeSlot);
    
    // 删除删除按钮
    const deleteBtn = document.getElementById('deleteTimeSlotBtn');
    if (deleteBtn) {
        deleteBtn.remove();
    }
    
    // 重置模态框标题
    document.getElementById('addTimeSlotModalLabel').textContent = '添加时间段';
    
    // 重新渲染视图
    renderCurrentView();
}

// 删除时间段
function deleteTimeSlot() {
    if (!confirm('确定要删除这个时间段吗？其中的所有任务也将被删除。')) {
        return;
    }
    
    // 获取时间段ID
    const timeSlotId = document.getElementById('editTimeSlotId').value;
    
    // 获取当前日期键
    const dateKey = formatDateKey(currentDate);
    
    // 删除时间段
    if (currentView === 'day') {
        // 查找并删除时间段
        const timeSlots = timelineData[currentView][dateKey].timeSlots;
        timelineData[currentView][dateKey].timeSlots = timeSlots.filter(slot => slot.id !== timeSlotId);
    } else if (currentView === 'month') {
        // 查找并删除时间段（需要额外检查是哪一天）
        const dayKeys = Object.keys(timelineData[currentView][dateKey] || {});
        dayLoop: for (const dayKey of dayKeys) {
            if (timelineData[currentView][dateKey][dayKey]?.timeSlots) {
                const timeSlots = timelineData[currentView][dateKey][dayKey].timeSlots;
                timelineData[currentView][dateKey][dayKey].timeSlots = timeSlots.filter(slot => slot.id !== timeSlotId);
                if (timeSlots.length !== timelineData[currentView][dateKey][dayKey].timeSlots.length) {
                    break dayLoop;
                }
            }
        }
    }
    
    // 保存数据
    saveData();
    
    // 关闭模态框
    addTimeSlotModal.hide();
    
    // 重置保存按钮逻辑
    const saveBtn = document.getElementById('saveTimeSlotBtn');
    saveBtn.removeEventListener('click', saveEditTimeSlot);
    saveBtn.addEventListener('click', saveNewTimeSlot);
    
    // 删除删除按钮
    const deleteBtn = document.getElementById('deleteTimeSlotBtn');
    if (deleteBtn) {
        deleteBtn.remove();
    }
    
    // 重置模态框标题
    document.getElementById('addTimeSlotModalLabel').textContent = '添加时间段';
    
    // 重新渲染视图
    renderCurrentView();
}

// 显示添加月视图时间段模态框
function showAddMonthTimeSlotModal(dayIndex) {
    // 重置表单
    document.getElementById('addTimeSlotForm').reset();
    
    // 预设时间
    const currentHour = new Date().getHours();
    document.getElementById('startTime').value = `${currentHour.toString().padStart(2, '0')}:00`;
    document.getElementById('endTime').value = `${(currentHour + 1).toString().padStart(2, '0')}:00`;
    
    // 设置模态框标题，显示是哪一天
    document.getElementById('addTimeSlotModalLabel').textContent = `添加时间段 (${dayIndex}日)`;
    
    // 添加日期信息到隐藏字段
    const dayIndexField = document.getElementById('dayIndexField') || document.createElement('input');
    dayIndexField.type = 'hidden';
    dayIndexField.id = 'dayIndexField';
    dayIndexField.value = dayIndex;
    
    // 确保表单中有隐藏字段
    const form = document.getElementById('addTimeSlotForm');
    if (!document.getElementById('dayIndexField')) {
        form.appendChild(dayIndexField);
    }
    
    // 确保保存按钮使用正确的处理函数
    const saveBtn = document.getElementById('saveTimeSlotBtn');
    saveBtn.removeEventListener('click', saveEditTimeSlot);
    
    // 使用自定义保存函数处理月视图的添加
    saveBtn.removeEventListener('click', saveNewTimeSlot);
    saveBtn.addEventListener('click', () => {
        // 获取开始和结束时间
        const startTime = document.getElementById('startTime').value;
        const endTime = document.getElementById('endTime').value;
        
        if (!startTime || !endTime) {
            alert('请设置开始和结束时间');
            return;
        }
        
        if (startTime >= endTime) {
            alert('开始时间必须早于结束时间');
            return;
        }
        
        // 获取时间段标题（可选）
        const timeSlotTitle = document.getElementById('timeSlotTitle')?.value || '';
        
        // 获取当前日期键和日期索引
        const dateKey = formatDateKey(currentDate);
        const selectedDay = dayIndex;
        const dayKey = `day-${selectedDay}`;
        
        // 创建时间段ID
        const timeSlotId = 'ts-' + Date.now().toString();
        
        // 创建新时间段对象
        const newTimeSlot = {
            id: timeSlotId,
            startTime: startTime,
            endTime: endTime,
            title: timeSlotTitle,
            tasks: [],
            createdAt: new Date().toISOString()
        };
        
        // 确保数据结构正确
        if (!timelineData[currentView][dateKey]) {
            timelineData[currentView][dateKey] = {};
        }
        
        // 添加或更新时间段
        if (!timelineData[currentView][dateKey][dayKey]) {
            timelineData[currentView][dateKey][dayKey] = {
                timeSlots: [newTimeSlot]
            };
        } else {
            if (!timelineData[currentView][dateKey][dayKey].timeSlots) {
                timelineData[currentView][dateKey][dayKey].timeSlots = [];
            }
            timelineData[currentView][dateKey][dayKey].timeSlots.push(newTimeSlot);
        }
        
        // 保存数据
        saveData();
        
        // 关闭模态框
        addTimeSlotModal.hide();
        
        // 重新渲染视图
        renderCurrentView();
        
        // 重置保存按钮逻辑
        saveBtn.removeEventListener('click', arguments.callee);
        saveBtn.addEventListener('click', saveNewTimeSlot);
    });
    
    // 显示模态框
    addTimeSlotModal.show();
}

// 显示编辑月视图时间段模态框
function showEditMonthTimeSlotModal(timeSlotId, dayIndex) {
    // 获取当前日期键
    const dateKey = formatDateKey(currentDate);
    const dayKey = `day-${dayIndex}`;
    
    // 获取时间段数据
    let timeSlot;
    
    if (timelineData[currentView][dateKey] && 
        timelineData[currentView][dateKey][dayKey] && 
        timelineData[currentView][dateKey][dayKey].timeSlots) {
        
        timeSlot = timelineData[currentView][dateKey][dayKey].timeSlots.find(slot => slot.id === timeSlotId);
    }
    
    if (!timeSlot) {
        alert('找不到该时间段');
        return;
    }
    
    // 设置表单值
    document.getElementById('startTime').value = timeSlot.startTime;
    document.getElementById('endTime').value = timeSlot.endTime;
    if (document.getElementById('timeSlotTitle')) {
        document.getElementById('timeSlotTitle').value = timeSlot.title || '';
    }
    
    // 添加时间段ID到隐藏字段
    const timeSlotIdField = document.getElementById('editTimeSlotId') || 
                           document.createElement('input');
    timeSlotIdField.type = 'hidden';
    timeSlotIdField.id = 'editTimeSlotId';
    timeSlotIdField.value = timeSlotId;
    
    // 添加日期索引到隐藏字段
    const dayIndexField = document.getElementById('dayIndexField') || 
                         document.createElement('input');
    dayIndexField.type = 'hidden';
    dayIndexField.id = 'dayIndexField';
    dayIndexField.value = dayIndex;
    
    // 确保表单中有隐藏字段
    const form = document.getElementById('addTimeSlotForm');
    if (!document.getElementById('editTimeSlotId')) {
        form.appendChild(timeSlotIdField);
    }
    if (!document.getElementById('dayIndexField')) {
        form.appendChild(dayIndexField);
    }
    
    // 更改模态框标题和按钮
    document.getElementById('addTimeSlotModalLabel').textContent = `编辑时间段 (${dayIndex}日)`;
    
    // 更改保存按钮逻辑
    const saveBtn = document.getElementById('saveTimeSlotBtn');
    saveBtn.removeEventListener('click', saveNewTimeSlot);
    saveBtn.addEventListener('click', saveEditTimeSlot);
    
    // 添加删除按钮
    let deleteBtn = document.getElementById('deleteTimeSlotBtn');
    if (!deleteBtn) {
        deleteBtn = document.createElement('button');
        deleteBtn.id = 'deleteTimeSlotBtn';
        deleteBtn.type = 'button';
        deleteBtn.className = 'btn btn-danger';
        deleteBtn.textContent = '删除';
        deleteBtn.addEventListener('click', deleteTimeSlot);
        
        // 添加到模态框底部
        const modalFooter = document.querySelector('#addTimeSlotModal .modal-footer');
        modalFooter.insertBefore(deleteBtn, modalFooter.firstChild);
    }
    
    // 显示模态框
    addTimeSlotModal.show();
} 