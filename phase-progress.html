<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>阶段进度 - ST计划</title>
    <!-- 添加网站图标 -->
    <link rel="icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <link rel="shortcut icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <!-- 样式文件 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    <link rel="stylesheet" type="text/css" href="css/main.css">
    <link rel="stylesheet" type="text/css" href="css/progress.css">
    <style>
        /* 整体样式 */
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: #f7f9fc;
            color: #333;
        }

        /* 阶段卡片样式 */
        .phase-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 16px;
            transition: all 0.3s ease;
        }

        .phase-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .phase-header {
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s ease;
        }

        .phase-header:hover {
            background-color: #f8f9fa;
        }

        .phase-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            white-space: nowrap;
        }

        .phase-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .phase-status.not-started {
            background-color: #e9ecef;
            color: #6c757d;
        }

        .phase-status.in-progress {
            background-color: #fff3cd;
            color: #856404;
        }

        .phase-status.completed {
            background-color: #d1edff;
            color: #0c63e4;
        }

        .phase-body {
            padding: 16px;
        }

        .phase-progress {
            margin-bottom: 12px;
        }

        .progress {
            height: 8px;
            background-color: #e9ecef;
            border-radius: 4px;
        }

        .progress-bar {
            background: linear-gradient(90deg, #4299e1, #3182ce);
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .progress-text {
            display: flex;
            justify-content: space-between;
            font-size: 0.85rem;
            color: #6c757d;
            margin-bottom: 4px;
        }

        .task-list {
            display: block; /* 默认展开 */
        }

        .task-item {
            display: flex;
            align-items: flex-start;
            padding: 8px 0;
            gap: 8px;
            transition: background-color 0.2s ease;
            border-bottom: none; /* 删除任务下面的线 */
        }

        .task-item:hover {
            background-color: #f8f9fa;
            border-radius: 4px;
            margin: 0 -8px;
            padding: 8px;
        }

        .task-checkbox {
            margin-top: 2px;
            flex-shrink: 0;
        }

        .task-name {
            font-size: 0.9rem;
            margin: 0;
            line-height: 1.4;
        }

        .task-completed {
            text-decoration: line-through;
            color: #6c757d;
        }

        .flex-grow-1 {
            flex: 1;
        }

        .task-actions {
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .task-item:hover .task-actions {
            opacity: 1;
        }

        /* 子任务样式 */
        .task-item[style*="margin-left"] {
            padding-left: 8px;
        }

        .task-item[style*="margin-left"] .task-name {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .task-item[style*="margin-left"]:not(.task-completed) .task-name {
            color: #495057;
        }

        .phase-actions {
            display: flex;
            gap: 8px;
        }

        .btn-icon {
            width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 任务列表标题样式 */
        .task-list-header {
            display: flex;
            align-items: center;
            padding: 4px 0;
            user-select: none;
        }

        .task-list-header:hover {
            background-color: #f8f9fa;
            border-radius: 4px;
            margin: 0 -8px;
            padding: 4px 8px;
        }

        .task-list-toggle {
            font-size: 0.9rem;
            color: #6c757d;
            transition: transform 0.2s ease;
        }

        /* 添加阶段按钮 */
        .add-phase-btn {
            position: fixed;
            bottom: 24px;
            right: 24px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }

        .add-phase-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0,0,0,0.2);
            color: white;
        }

        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 16px;
            color: #dee2e6;
        }

        /* 模态框样式优化 */
        .modal-content {
            border-radius: 12px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .modal-header {
            border-bottom: 1px solid #e9ecef;
            padding: 20px 24px 16px;
        }

        .modal-body {
            padding: 20px 24px;
        }

        .modal-footer {
            border-top: 1px solid #e9ecef;
            padding: 16px 24px 20px;
        }
    </style>
</head>

<body>
    <div class="container mt-3">
        <!-- 导航栏 -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-layer-group text-primary me-2"></i>
                        阶段进度
                    </h4>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-info btn-sm" id="phaseBatchModeToggle">
                            <i class="fas fa-list me-1"></i>批量模式
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="window.location.href='index.html'">
                            <i class="fas fa-home me-1" style="font-size: 14px;"></i>🏠 返回主页
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="row mb-3" id="statisticsRow" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-body py-2">
                        <div class="row text-center">
                            <div class="col-4">
                                <small class="text-muted">总阶段</small>
                                <div class="fw-bold" id="totalPhases">0</div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">进行中</small>
                                <div class="fw-bold text-warning" id="inProgressPhases">0</div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">已完成</small>
                                <div class="fw-bold text-success" id="completedPhases">0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 阶段列表 -->
        <div class="row">
            <div class="col-12">
                <div id="phaseContainer">
                    <!-- 阶段卡片将在这里动态生成 -->
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div id="emptyState" class="empty-state" style="display: none;">
            <i class="fas fa-layer-group"></i>
            <h5>还没有创建任何阶段</h5>
            <p class="mb-3">点击右下角的 + 按钮开始创建第一个阶段</p>
            <div class="mt-3">
                <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addPhaseModal">
                    <i class="fas fa-plus me-1"></i>创建第一个阶段
                </button>
            </div>
        </div>

        <!-- 添加阶段按钮 -->
        <button class="add-phase-btn" data-bs-toggle="modal" data-bs-target="#addPhaseModal">
            <i class="fas fa-plus"></i>
        </button>
    </div>

    <!-- 添加阶段模态框 -->
    <div class="modal fade" id="addPhaseModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建新阶段</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addPhaseForm">
                        <div class="mb-3">
                            <label for="phaseName" class="form-label">阶段名称</label>
                            <input type="text" class="form-control" id="phaseName" required>
                        </div>
                        <div class="mb-3">
                            <label for="phaseDescription" class="form-label">阶段描述</label>
                            <textarea class="form-control" id="phaseDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createPhase()">创建</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量添加任务模态框 -->
    <div class="modal fade" id="batchAddTaskModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">批量添加任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="batchAddTaskForm">
                        <input type="hidden" id="batchPhaseId">
                        <div class="mb-3">
                            <label for="batchTaskNames" class="form-label">任务列表</label>
                            <textarea class="form-control" id="batchTaskNames" rows="6" placeholder="输入多个任务名称，每行一个任务，空行会被忽略&#10;例如：&#10;吃饭&#10;睡觉&#10;洗漱" required></textarea>
                            <small class="text-muted">每行一个任务，空行会被忽略</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmBatchAddTasks()">批量添加</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑阶段模态框 -->
    <div class="modal fade" id="editPhaseModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑阶段</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editPhaseForm">
                        <input type="hidden" id="editPhaseId">
                        <div class="mb-3">
                            <label for="editPhaseName" class="form-label">阶段名称</label>
                            <input type="text" class="form-control" id="editPhaseName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editPhaseDescription" class="form-label">阶段描述</label>
                            <textarea class="form-control" id="editPhaseDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="updatePhase()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript 文件 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 引入主系统配置和数据 -->
    <script src="js/st.js"></script>
    <!-- 阶段进度系统脚本 -->
    <script src="js/phase-progress.js"></script>
</body>

</html>
