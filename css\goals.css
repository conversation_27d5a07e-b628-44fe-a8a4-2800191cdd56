/* Savings goals styles */

/* 存款进度卡片样式 - 字体优化版 */
.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c5282;
    margin-bottom: 0.4rem !important;
    padding: 0.3rem 0.5rem;
    padding-left: 0;
    border-bottom: 1px solid rgba(0,0,0,0.06);
    position: relative;
    display: inline-block;
    letter-spacing: 0.3px;
    text-shadow: 0 1px 0 rgba(255,255,255,0.8);
    transition: all 0.3s ease;
}

.card-title::before {
    content: "💰";
    margin-right: 5px;
    font-size: 0.9rem;
    position: relative;
    top: 1px;
}

.card .card-body {
    padding: 0.6rem 0.7rem 0.5rem 0.7rem !important;
    background: linear-gradient(to bottom, #f9f9f9, #ffffff);
    border-radius: 0 0 8px 8px;
}

/* 存款卡片增强样式 */
.card {
    position: relative;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* 左侧状态指示条 - 动态版 */
.card::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 5px;
    background: linear-gradient(to bottom, #4299e1, #3182ce);
    z-index: 2;
    transition: all 0.3s ease;
}

/* 根据完成度改变指示条颜色 */
.card.progress-low::before {
    background: linear-gradient(to bottom, #f56565, #e53e3e);
}

.card.progress-medium::before {
    background: linear-gradient(to bottom, #ed8936, #dd6b20);
}

.card.progress-high::before {
    background: linear-gradient(to bottom, #48bb78, #38a169);
}

.card.progress-complete::before {
    background: linear-gradient(to bottom, #9f7aea, #805ad5);
    animation: completePulse 2s ease-in-out infinite;
}

@keyframes completePulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* 右上角装饰图标 */
.card::after {
    content: "💰";
    position: absolute;
    top: 12px;
    right: 12px;
    font-size: 1.2rem;
    opacity: 0.1;
    z-index: 1;
    filter: grayscale(0.3);
}

.card .card-body .d-flex.justify-content-between {
    margin-bottom: 0.4rem !important;
}

/* 卡片按钮样式 - 字体优化版 */
.card .btn-sm {
    padding: 0.2rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    line-height: 1.3;
}

.card .btn-primary {
    background-color: #3182ce;
    border-color: #3182ce;
}

.card .btn-primary:hover {
    background-color: #2c5282;
    border-color: #2c5282;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card .btn-danger {
    background-color: #e53e3e;
    border-color: #e53e3e;
}

.card .btn-danger:hover {
    background-color: #c53030;
    border-color: #c53030;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 进度容器增强版 */
.progress-wrapper {
    margin-top: 0.4rem !important;
    margin-bottom: 0 !important;
    background: linear-gradient(145deg, #f7fafc, #ffffff);
    box-shadow:
        inset 0 2px 4px rgba(0,0,0,0.06),
        0 1px 3px rgba(0,0,0,0.1);
    padding: 0.5rem 0.6rem;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

.progress-wrapper .progress {
    height: 14px !important;
    border-radius: 7px;
    background: linear-gradient(90deg, #e2e8f0, #f1f5f9);
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

.progress-wrapper .progress-bar {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 11px;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    background: linear-gradient(90deg, #4299e1, #3182ce, #2c5282) !important;
    border-radius: 7px;
    position: relative;
    overflow: hidden;
    animation: progressFill 1.5s ease-out;
}

/* 存款进度条特殊样式 - 覆盖默认样式 */
.progress-wrapper .progress-bar.savings-progress-bar {
    background: linear-gradient(45deg, #FFD700 0%, #FFA500 25%, #FFDF00 50%, #FFB347 75%, #FFD700 100%) !important;
    background-size: 400% 100% !important;
    animation: goldGlowing 1.5s infinite ease-in-out !important;
    position: relative !important;
    overflow: hidden !important;
}

/* 进度条动画效果 */
@keyframes progressFill {
    0% {
        width: 0% !important;
        opacity: 0.8;
    }
    100% {
        opacity: 1;
    }
}

/* 存款进度条金色闪光动画 - 增强版 */
@keyframes goldGlowing {
    0% {
        background-position: -200% 0;
        box-shadow:
            0 0 10px rgba(255, 215, 0, 0.8),
            0 0 20px rgba(255, 215, 0, 0.6),
            0 0 30px rgba(255, 215, 0, 0.4),
            inset 0 0 10px rgba(255, 255, 255, 0.2);
        filter: brightness(1.0) saturate(1.2);
    }
    25% {
        box-shadow:
            0 0 15px rgba(255, 215, 0, 0.9),
            0 0 30px rgba(255, 215, 0, 0.7),
            0 0 45px rgba(255, 215, 0, 0.5),
            inset 0 0 15px rgba(255, 255, 255, 0.3);
        filter: brightness(1.1) saturate(1.3);
    }
    50% {
        background-position: 0% 0;
        box-shadow:
            0 0 20px rgba(255, 215, 0, 1.0),
            0 0 40px rgba(255, 215, 0, 0.8),
            0 0 60px rgba(255, 215, 0, 0.6),
            inset 0 0 20px rgba(255, 255, 255, 0.4);
        filter: brightness(1.2) saturate(1.4);
    }
    75% {
        box-shadow:
            0 0 15px rgba(255, 215, 0, 0.9),
            0 0 30px rgba(255, 215, 0, 0.7),
            0 0 45px rgba(255, 215, 0, 0.5),
            inset 0 0 15px rgba(255, 255, 255, 0.3);
        filter: brightness(1.1) saturate(1.3);
    }
    100% {
        background-position: 200% 0;
        box-shadow:
            0 0 10px rgba(255, 215, 0, 0.8),
            0 0 20px rgba(255, 215, 0, 0.6),
            0 0 30px rgba(255, 215, 0, 0.4),
            inset 0 0 10px rgba(255, 255, 255, 0.2);
        filter: brightness(1.0) saturate(1.2);
    }
}

/* 进度条光泽效果 */
.progress-wrapper .progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255,255,255,0.4),
        transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.progress-wrapper .progress-bar::before,
.progress-wrapper .progress-bar > *,
.progress-wrapper .progress-bar span,
.progress-wrapper .progress-bar i,
.progress-wrapper .progress-bar em {
    font-size: 10px !important;
    line-height: 1;
    position: relative;
    top: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.progress-wrapper .progress-bar [class*="icon"],
.progress-wrapper .progress-bar [class*="fa-"],
.progress-wrapper .progress-bar [class*="emoji"] {
    font-size: 10px !important;
    vertical-align: middle;
    line-height: 1;
}

.progress-wrapper::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, 
                transparent 0%,
                rgba(255,255,255,0.8) 50%,
                transparent 100%);
}

.amount-display-wrapper {
    margin-bottom: 0.3rem !important;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}

/* 金额标签样式 - 字体优化版 */
.amount-label {
    font-size: 0.8rem;
    color: #718096;
    margin-bottom: 0.15rem;
    opacity: 0.9;
    line-height: 1.2;
}

/* 金额值样式 - 增强版 */
.amount-value {
    font-size: 1.2rem;
    font-weight: 700;
    background: linear-gradient(135deg, #2c5282, #4299e1);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    letter-spacing: 0.3px;
    text-shadow: 0 2px 4px rgba(44, 82, 130, 0.2);
    line-height: 1.2;
    transition: all 0.3s ease;
    position: relative;
}

.amount-value:hover {
    transform: scale(1.02);
}

/* 当前金额特殊样式 */
.amount-value.current {
    color: #3182ce;
    position: relative;
}

.amount-value.current::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 25%;
    width: 50%;
    height: 1px;
    background: rgba(49, 130, 206, 0.3);
}

/* 剩余金额样式 - 字体优化版 */
.remaining-amount {
    color: #e53e3e;
    font-weight: 600;
    font-size: 0.85rem;
    text-align: right;
    margin-top: 0.25rem;
    margin-bottom: 0;
    padding-bottom: 0;
    letter-spacing: 0.1px;
    position: relative;
    padding-right: 0.9rem;
    line-height: 1.2;
}

.remaining-amount::after {
    content: '→';
    position: absolute;
    right: 0;
    top: 0;
    font-size: 0.8rem;
    opacity: 0.7;
}

/* Total saved display */
.total-saved-container {
    background: linear-gradient(145deg, #f8f9fa, #ffffff);
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    text-align: center;
}

.total-saved-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.total-saved-amount {
    color: #2ecc71;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.text-success {
    color: #38a169 !important;
    font-weight: 500;
}

h3.text-success {
    font-size: 1.25rem;
    margin-top: 1rem;
}

/* Timeline view for savings history */
.timeline-view {
    padding: 20px;
}

.timeline-group {
    margin-bottom: 2rem;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.timeline-date {
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
    color: #495057;
    font-weight: 500;
    font-size: 0.9rem;
}

.timeline-items {
    padding: 15px;
}

.timeline-item {
    position: relative;
    padding-left: 24px;
    margin-bottom: 15px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 8px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6c757d;
}

.timeline-item::after {
    content: '';
    position: absolute;
    left: 3px;
    top: 16px;
    width: 2px;
    height: calc(100% + 7px);
    background: #e9ecef;
}

.timeline-item:last-child::after {
    display: none;
}

/* Archive styles */
.archive-category-title {
    font-size: 0.9rem;
    font-weight: 500;
    color: #4a5568;
    margin: 1rem 0 0.75rem 0.5rem;
    padding-bottom: 0.25rem;
    border-bottom: 1px solid #e2e8f0;
}

.archive-grid {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.archive-card {
    background: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    padding: 0.75rem;
}

.archive-card .card-body {
    padding: 0 !important;
}

.archive-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
}

.archive-name {
    font-weight: 500;
    color: #2d3748;
    font-size: 0.9rem;
}

.archive-target {
    color: #718096;
    font-size: 0.85rem;
}

.archive-date {
    color: #a0aec0;
    font-size: 0.8rem;
}

.btn-restore {
    font-size: 0.8rem;
    padding: 0.2rem 0.5rem;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
}

.text-muted.small {
    color: #a0aec0 !important;
    font-size: 0.85rem;
    text-align: center;
    padding: 1rem;
}

/* 货币切换按钮样式 */
#currencyToggleBtn {
    padding: 0.2rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 4px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    margin-right: 5px;
    margin-bottom: 5px;
}

#currencyToggleBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
} 