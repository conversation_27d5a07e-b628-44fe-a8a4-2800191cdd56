<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>赚钱项目记录 - ST计划</title>
    <!-- 添加网站图标 -->
    <link rel="icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <link rel="shortcut icon" href="assets/img/favicon.svg" type="image/svg+xml">
    <!-- 样式文件 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    <link rel="stylesheet" type="text/css" href="css/main.css">
    <link rel="stylesheet" type="text/css" href="css/money-projects.css">
</head>

<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <span class="navbar-brand mb-0 h1">
                <i class="fas fa-dollar-sign me-2"></i>赚钱项目记录
            </span>
            <a class="btn btn-light btn-sm" href="index.html">
                <i class="fas fa-home me-1" style="font-size: 14px;"></i>🏠 返回主页
            </a>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- 左侧控制面板 -->
            <div class="col-md-3">
                <!-- 添加项目 -->
                <div class="card mb-3">
                    <div class="card-header py-2">
                        <i class="fas fa-plus me-2"></i>添加项目
                    </div>
                    <div class="card-body py-2">
                        <form id="projectForm">
                            <div class="mb-2">
                                <label class="form-label">项目名称:</label>
                                <input class="form-control form-control-sm" id="projectName" type="text" placeholder="例如：网站开发、设计服务" />
                            </div>
                            <div class="mb-2">
                                <label class="form-label">分类:</label>
                                <select class="form-select form-select-sm" id="projectCategory">
                                    <option value="">选择分类</option>
                                </select>
                            </div>
                            <div class="mb-2">
                                <label class="form-label">预计单个收益:</label>
                                <input class="form-control form-control-sm" id="projectRevenue" type="number" step="0.01" placeholder="例如：500.00" />
                            </div>
                            <div class="mb-2">
                                <label class="form-label">目标数量:</label>
                                <input class="form-control form-control-sm" id="projectTargetQuantity" type="number" min="1" placeholder="例如：10" />
                            </div>
                            <div class="mb-2">
                                <label class="form-label">当前数量:</label>
                                <input class="form-control form-control-sm" id="projectCurrentQuantity" type="number" min="0" placeholder="例如：3" />
                            </div>
                            <div class="mb-2">
                                <label class="form-label">优先级:</label>
                                <select class="form-select form-select-sm" id="projectPriority">
                                    <option value="1">⭐</option>
                                    <option value="2">⭐⭐</option>
                                    <option value="3" selected>⭐⭐⭐</option>
                                    <option value="4">⭐⭐⭐⭐</option>
                                    <option value="5">⭐⭐⭐⭐⭐</option>
                                </select>
                            </div>
                            <div class="mb-2">
                                <label class="form-label">截止日期:</label>
                                <input class="form-control form-control-sm" id="projectDeadline" type="date" />
                            </div>
                            <div class="mb-2">
                                <label class="form-label">备注:</label>
                                <textarea class="form-control form-control-sm" id="projectNote" rows="2" placeholder="项目详情、进度等"></textarea>
                            </div>
                            <div class="mb-2">
                                <label class="form-label">状态:</label>
                                <select class="form-select form-select-sm" id="projectStatus">
                                    <option value="planning">计划中</option>
                                    <option value="in-progress">进行中</option>
                                    <option value="completed">已完成</option>
                                    <option value="paused">暂停</option>
                                </select>
                            </div>
                            <div class="mb-2" id="pauseReasonContainer" style="display: none;">
                                <label class="form-label">暂停原因:</label>
                                <input class="form-control form-control-sm" id="projectPauseReason" type="text" placeholder="请输入暂停原因" />
                            </div>
                            <button class="btn btn-primary btn-sm w-100" type="submit" id="submitProjectBtn">添加项目</button>
                        </form>
                    </div>
                </div>

                <!-- 分类管理 -->
                <div class="card">
                    <div class="card-header py-2">
                        <i class="fas fa-tags me-2"></i>分类管理
                    </div>
                    <div class="card-body py-2">
                        <!-- 添加分类 -->
                        <div class="category-add-section mb-2">
                            <div class="input-group input-group-sm">
                                <input type="text" class="form-control" id="newCategoryName" placeholder="添加分类">
                                <button class="btn btn-success" type="button" id="addCategoryBtn">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 分类列表 -->
                        <div class="categories-container" id="categoriesContainer">
                            <!-- 分类将在这里动态显示 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧项目列表 -->
            <div class="col-md-9">
                <!-- 统计信息 -->
                <div class="row mb-3">
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
                        <div class="card text-center">
                            <div class="card-body py-2">
                                <h6 class="card-title text-primary mb-1">总项目数</h6>
                                <h4 class="text-primary mb-0" id="totalProjects">0</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
                        <div class="card text-center">
                            <div class="card-body py-2">
                                <h6 class="card-title text-warning mb-1">进行中</h6>
                                <h4 class="text-warning mb-0" id="inProgressProjects">0</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
                        <div class="card text-center">
                            <div class="card-body py-2">
                                <h6 class="card-title text-info mb-1">已完成</h6>
                                <h4 class="text-info mb-0" id="completedProjects">0</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-6 mb-2">
                        <div class="card text-center">
                            <div class="card-body py-2">
                                <h6 class="card-title text-success mb-1">当前收益</h6>
                                <h4 class="text-success mb-0" id="currentRevenue">¥0</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-6 mb-2">
                        <div class="card text-center">
                            <div class="card-body py-2">
                                <h6 class="card-title text-primary mb-1">目标收益</h6>
                                <h4 class="text-primary mb-0 target-revenue blurred" id="targetRevenue"
                                    onclick="MoneyProjects.toggleStatisticsTargetRevenueBlur()"
                                    title="点击查看目标收益">¥0</h4>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 筛选和排序 -->
                <div class="card mb-3">
                    <div class="card-body py-2">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <label class="form-label mb-1">按分类筛选:</label>
                                <select class="form-select form-select-sm" id="filterCategory">
                                    <option value="">全部分类</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-2">
                                <label class="form-label mb-1">按状态筛选:</label>
                                <select class="form-select form-select-sm" id="filterStatus">
                                    <option value="">全部状态</option>
                                    <option value="planning">计划中</option>
                                    <option value="in-progress">进行中</option>
                                    <option value="completed">已完成</option>
                                    <option value="paused">暂停</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-2">
                                <label class="form-label mb-1">搜索项目:</label>
                                <div class="input-group input-group-sm">
                                    <input type="text" class="form-control" id="searchInput" placeholder="搜索项目名称、备注...">
                                    <button class="btn btn-outline-secondary" type="button" id="clearSearchBtn">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 批量操作栏 -->
                <div class="card mb-2" id="batchOperationBar" style="display: none;">
                    <div class="card-body py-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-muted">已选择 <span id="selectedCount">0</span> 个项目</span>
                            <div>
                                <button class="btn btn-outline-success btn-sm me-2" id="batchCompleteBtn">
                                    <i class="fas fa-check me-1"></i>标记完成
                                </button>
                                <button class="btn btn-outline-warning btn-sm me-2" id="batchPauseBtn">
                                    <i class="fas fa-pause me-1"></i>批量暂停
                                </button>
                                <button class="btn btn-outline-primary btn-sm me-2" id="batchRestartBtn">
                                    <i class="fas fa-play me-1"></i>重启项目
                                </button>
                                <button class="btn btn-outline-danger btn-sm me-2" id="batchDeleteBtn">
                                    <i class="fas fa-trash me-1"></i>批量删除
                                </button>
                                <button class="btn btn-secondary btn-sm" id="clearSelectionBtn">
                                    <i class="fas fa-times me-1"></i>取消选择
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 项目列表 -->
                <div class="card">
                    <div class="card-header py-2 d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-list me-2"></i>项目列表
                        </div>
                        <div>
                            <input type="checkbox" id="selectAllCheckbox" class="form-check-input me-2">
                            <label for="selectAllCheckbox" class="form-check-label small">全选</label>
                        </div>
                    </div>
                    <div class="card-body py-2">
                        <div id="projectsContainer">
                            <!-- 项目将在这里动态显示 -->
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-folder-open fa-3x mb-3"></i>
                                <p>暂无项目，请添加第一个赚钱项目</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑项目模态框 -->
    <div class="modal fade" id="editProjectModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑项目</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editProjectForm">
                        <input type="hidden" id="editProjectId">
                        <div class="mb-3">
                            <label class="form-label">项目名称:</label>
                            <input class="form-control" id="editProjectName" type="text" />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">分类:</label>
                            <select class="form-select" id="editProjectCategory">
                                <option value="">选择分类</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">预计单个收益:</label>
                            <input class="form-control" id="editProjectRevenue" type="number" step="0.01" />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">目标数量:</label>
                            <input class="form-control" id="editProjectTargetQuantity" type="number" min="1" />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">当前数量:</label>
                            <input class="form-control" id="editProjectCurrentQuantity" type="number" min="0" />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">优先级:</label>
                            <select class="form-select" id="editProjectPriority">
                                <option value="1">⭐</option>
                                <option value="2">⭐⭐</option>
                                <option value="3">⭐⭐⭐</option>
                                <option value="4">⭐⭐⭐⭐</option>
                                <option value="5">⭐⭐⭐⭐⭐</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">截止日期:</label>
                            <input class="form-control" id="editProjectDeadline" type="date" />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">备注:</label>
                            <textarea class="form-control" id="editProjectNote" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">状态:</label>
                            <select class="form-select" id="editProjectStatus">
                                <option value="planning">计划中</option>
                                <option value="in-progress">进行中</option>
                                <option value="completed">已完成</option>
                                <option value="paused">暂停</option>
                            </select>
                        </div>
                        <div class="mb-3" id="editPauseReasonContainer" style="display: none;">
                            <label class="form-label">暂停原因:</label>
                            <input class="form-control" id="editProjectPauseReason" type="text" placeholder="请输入暂停原因" />
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveProjectBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript 文件 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/money-projects.js"></script>
</body>
</html>
